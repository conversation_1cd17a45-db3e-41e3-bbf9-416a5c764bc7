{"name": "simbios-app", "version": "0.0.1", "private": true, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src/ --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --check src/", "format:fix": "prettier --write src/"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@dagrejs/dagre": "^1.1.4", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@sentry/react": "^10.5.0", "@xyflow/react": "^12.4.3", "ahooks": "^3.7.8", "antd": "^5.24.1", "axios": "^1.1.3", "dayjs": "^1.11.13", "emoji-picker-react": "^4.12.2", "lodash": "^4.17.21", "mobx": "^6.13.6", "mobx-react": "^9.2.0", "pusher-js": "^8.4.0", "rc-input-number": "^9.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "^6.8.1", "use-draggable-scroll": "^0.1.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/lodash": "^4.17.20", "@types/node": "^22.16.5", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.3", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-formatter-gitlab": "^6.0.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "prettier-plugin-multiline-arrays": "4.0.3", "sass-embedded": "^1.90.0", "typescript": "^5.9.2", "typescript-eslint": "^8.39.1", "vite": "^7.0.6", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}, "pnpm": {"overrides": {"braces@<3.0.3": ">=3.0.3", "concat-stream@<1.5.2": ">=1.5.2", "micromatch@<4.0.8": ">=4.0.8", "yargs-parser@<=5.0.0": ">=5.0.1"}}}