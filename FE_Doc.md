Авторы: фронтендер (User Dev1).
Дата последнего изменения: 07.07.2025.

0. Для работы в системе спроектированы 4 роли:
- Админ - техническая роль, все права, расширены возможности;
- Архитектор - работа с симуляциями;
- Менеджер - управление пользователями и назначениями симуляций;
- Клиент - прохождение симуляций.

1. Основой работы архитектора является Симуляция.
1.1 Сам объект симуляции несёт основные данные - настройки бюджета, общий план доступных недель, названия характеристик для ПШЕ и узлов.
1.2 Дочерние сущности симуляции - ПШЕ, узлы, сценарные события. Запланированы также календарные события.
1.3 Для работы над симуляцией архитектору доступны экраны "Граф" (постройка дерева узлов с их настройкой), "Гант" (представление о длительности задач, критическом пути, прогноз при назначении разных ПШЕ), а также списки и профили отдельных сущностей.
1.4 Симуляция в ходе разработки имеет следующие статусы:
- В разработке (по умолчанию) - ведётся работа;
- Готова - пометка архитектора, что работы по ней пока заверешены, запрещает редактирование, но позволяет вернуть в разработку и опубликовать;
- Опубликована - симуляция необратимо переходит в режим чтения (для обеспечения целости данных прохождения) и готова к назначению пользователям - при необходимости доработок можно создать редактируемый дубликат одним нажатием;
- Архив - любые симуляции можно обратимо архивировать для скрытия из выдачи и запрета редактирования;
- Удаление - все, кроме опубликованных, могут быть обратимо удалены - просмотр дочерних сущностей симуляции будет запрещён;
--- Временно неиспользуемые ---
- Протестирована - предназначен для автотестирования сервером построенной симуляции;
- Шаблон - предназначен для возможности использовать симуляцию и её дочерние сущности при создании новых;

2. Работа менеджера разделяется на две зоны:
2.1 Пользователи и Приглашения
2.2 Симуляции, Сессии, Назначения
2.2.1 Для опубликованных и не архивированных (для архивированных ранее созданные остаются доступными) симуляций допустимо создание сессий. Сессия - своеобразный перечень назначений конкретной симуляции под руководством менеджера.
2.2.2 Назначения - отдельные "билеты" для пользователей на прохождение симуляции под контролем менеджера (ответственного за сессию).
2.2.3 Назначениям, как несущей сущности прохождения, предусмотрены статусы:
- Предстарт (новое) - данные подготовлены, выставлены стартовые значения к началу прохождения;
- Запущено (старт) - прохождение идёт соответственно настройкам;
- Приостановлено (пауза) - прохождение на паузе, ядро активно, но ожидает снятие паузы;
- Продолжено - аналогично "Запущено";
- Остановлено (стоп) - прохождение на паузе, ядро отключено, ожидается повторный запуск;
- Завершено (финиш, пройдено) - прохождение завершено тем или иным образом (в данный момент - через выполнение всех узлов).

3. Зона видимости клиента ограничена общением с ведущим его менеджером и прохождением назначенных симуляций.
3.1 В режиме прохождения клиенту (пользователю) доступны следующие экраны:
- Рабочий стол - быстрая навигация, в будущем вывод полезных данных;
- Дашборд - метрики симуляции и узлов, переход к чатам, Ганту, графикам;
- Диаграмма Ганта - актуальные длительности узлов, работа с назначениями ПШЕ на узлы;
- Бюджет - данные о тратах и прогрессе по узлам;
- Команда - график занятости ПШЕ;
- Сеть - сетевой график дерева узлов для понимания их последовательности и связей;
- Отчёты - переход к графическим и иным отчётам, в данный момент ограничено до отчёта по плановым и текущим значениями затрат в узлах, в будущем перечень будет расширен;
- Чаты - связь с менеджером, а также чаты для сценарных событий;
--- Запланированный экран ---
- Календарь - заложенные архитектором события и возможность планировать свои