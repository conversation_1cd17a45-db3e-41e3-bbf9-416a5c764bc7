import { message } from 'antd';
import { SettingsManager } from './settingsManager';
import { TUser } from 'types/user/user';
import dayjs from 'dayjs';
import { GlobalConstants } from './constants';
import { TInvitation } from 'types/invitations/invitation';

export class Common {
    /**
     * Проверка на пустую переменную
     * @param value Переменная
     *
     * @returns Результат
     */
    public static isNullOrUndefined(value?: unknown) {
        return value == null || value == undefined;
    }

    public static isNullOrEmptyString(value?: unknown) {
        return value == null || value == '';
    }

    /**
     * Копировать текст в буфер
     * @param textToCopy Копируемый текст
     * @param textToShow Текст уведомления
     */
    public static clipboardCopy(textToCopy: string, textToShow?: string) {
        try {
            navigator.clipboard.writeText(textToCopy);
            if (!this.isNullOrUndefined(textToShow)) message.info(textToShow);
        } catch (errors) {
            message.error('Не удалось скопировать');
            console.log(errors);
        }
    }

    public static checkConnection() {
        return SettingsManager.getConnectionCredentials()?.accessToken != null;
    }

    public static weekdayNumToShortString(day: number) {
        if (day < 0 || day > 6) return 'out-of-bounds[0;6]';
        const weekdays = [
            'Вс',
            'Пн',
            'Вт',
            'Ср',
            'Чт',
            'Пт',
            'Сб',
        ];
        return weekdays[day];
    }

    public static formatDateString(
        ds: string | null,
        mode: 'hms-dmy' | 'dmy' | 'dm' | 'hms' | 'hm' = 'hms-dmy',
    ) {
        if (ds == null) return '-';
        let format = null;
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        switch (mode) {
            case 'hms-dmy': {
                format = 'HH:mm:ss DD.MM.YYYY';
                break;
            }
            case 'dmy': {
                format = 'DD.MM.YYYY';
                break;
            }
            case 'dm': {
                format = 'DD.MM';
                break;
            }
            case 'hms': {
                format = 'HH:mm:ss';
                break;
            }
            case 'hm': {
                format = 'HH:mm';
                break;
            }
        }
        return dayjs.utc(ds).tz(timezone).format(format);
    }

    public static makeRoleName(role: TUser['role']) {
        let rn = '';
        switch (role) {
            case 'Roles.Admin':
                rn = 'Администратор';
                break;
            case 'Roles.Architect':
                rn = 'Архитектор';
                break;
            case 'Roles.Manager':
                rn = 'Менеджер';
                break;
            case 'Roles.Client':
                rn = 'Клиент';
                break;
            default:
                rn = role;
                break;
        }
        return rn;
    }

    public static dateNowString() {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        return dayjs.utc().tz(timezone).format(GlobalConstants.DateTimeFormat);
    }

    public static shouldTextBeDark(hex: string): boolean {
        const value: string = hex;
        const r = Number.parseInt(value.slice(1, 2), 16);
        const g = Number.parseInt(value.slice(3, 4), 16);
        const b = Number.parseInt(value.slice(5, 6), 16);

        // https://stackoverflow.com/questions/3942878/how-to-decide-font-color-in-white-or-black-depending-on-background-color
        //const useDark = (r * 0.299 + g * 0.587 + b * 0.114) > 186;
        let tr = r / 255.0;
        tr = tr <= 0.04045 ? tr / 12.92 : ((tr + 0.055) / 1.055) ^ 2.4;
        let tg = g / 255.0;
        tg = tg <= 0.04045 ? tg / 12.92 : ((tg + 0.055) / 1.055) ^ 2.4;
        let tb = b / 255.0;
        tb = tb <= 0.04045 ? tb / 12.92 : ((tb + 0.055) / 1.055) ^ 2.4;
        const luminosity = 0.2126 * tr + 0.7152 * tg + 0.0722 * tb;
        const useDark = luminosity > 0.179;

        return useDark;
    }

    public static makeInviteLink(invite_id: TInvitation['id']): string {
        //if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
        //    return `http://localhost:3005/invite/${invite_id}`;
        //} else {
        return `http://${GlobalConstants.SocketUrl}:3005/invite/${invite_id}`;
        //}
    }
}

export function debounce(fn: (...args: unknown[]) => void, ms: number) {
    let timer: NodeJS.Timeout | null = null;

    return (...args: any[]) => {
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            fn(...args);
        }, ms);
    };
}
