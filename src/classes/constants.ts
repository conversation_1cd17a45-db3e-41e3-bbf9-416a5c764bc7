import Colors from './colors';

export class GlobalConstants {
    /** ID div для закрепления SPA */
    public static MainRoot = 'simbios-root';
    /** URL бэкенда */
    public static BaseUrl = import.meta.env.VITE_API_URL || 'https://localhost:3000/api';
    /** Адрес для socket-подключений */
    public static SocketUrl = import.meta.env.VITE_SOCKET_URL || 'https://localhost:7090';
    /** Стандартный таймаут запросов */
    public static RequestTimeout = 30000;
    /** Таймаут bulk-запросов */
    public static BulkRequestTimeout = 30000;
    /** Хранит данные сессии, TCredentials */
    public static ConnectionCredentialsProperty = 'Simbios.Properties.ConnectionCredentials';
    /** Основной формат datetime */
    public static DateTimeFormat = 'YYYY-MM-DDTHH:mm:ss';

    public static ListGridSettings = {
        screenXS: 480,
        screenXSMax: 480 * 2 + 16 - 1,
        screenXSMin: 480,
        screenSM: 480 * 2 + 16,
        screenSMMax: 480 * 3 + 16 * 2 - 1,
        screenSMMin: 480 * 2 + 16,
        screenMD: 480 * 3 + 16 * 2,
        screenMDMax: 480 * 4 + 16 * 3 - 1,
        screenMDMin: 480 * 3 + 16 * 2,
        screenLG: 480 * 4 + 16 * 3,
        screenLGMax: 480 * 5 + 16 * 4 - 1,
        screenLGMin: 480 * 4 + 16 * 3,
        screenXL: 480 * 5 + 16 * 4,
        screenXLMax: 480 * 6 + 16 * 5 - 1,
        screenXLMin: 480 * 5 + 16 * 4,
        screenXXL: 480 * 6 + 16 * 5,
        screenXXLMin: 480 * 6 + 16 * 5,
    };

    public static ListGridCols = {
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 5,
        xxl: 6,
    };

    public static TableStatuses: { [key: string]: { color: string; text: string } } = {
        accepted: {
            color: Colors.Accent.cold[800],
            text: 'Принято',
        },
        ban: {
            color: Colors.Error.warm[300],
            text: 'Бан',
        },
        deleted: {
            color: Colors.Error.cold[300],
            text: 'Удален(а)',
        },
        finished: {
            color: Colors.Accent.cold[800],
            text: 'Завершено',
        },
        new: {
            color: Colors.Success.warm[300],
            text: 'Новое',
        },
        'no-user': {
            color: Colors.Error.cold[300],
            text: 'Не выбран пользователь',
        },
        paused: {
            color: Colors.Accent.cold[500],
            text: 'Пауза',
        },
        prestarted: {
            color: Colors.Accent.cold[300],
            text: 'Предстарт',
        },
        restored: {
            color: Colors.Success.warm[300],
            text: 'Восстановлен(а)',
        },
        resumed: {
            color: Colors.Accent.cold[600],
            text: 'Продолжено',
        },
        'role-change': {
            color: Colors.Warning.warm[300],
            text: 'Смена роли',
        },
        sent: {
            color: Colors.Accent.cold[400],
            text: 'Отправлено',
        },
        started: {
            color: Colors.Accent.cold[400],
            text: 'Идёт',
        },
        stopped: {
            color: Colors.Accent.cold[700],
            text: 'Стоп',
        },
        unban: {
            color: Colors.Success.cold[300],
            text: 'Разбан',
        },
        unfilled: {
            color: Colors.Error.cold[300],
            text: 'Не заполнен(а)',
        },
        updated: {
            color: Colors.Accent.warm[500],
            text: 'Изменен(а)',
        },
    };
}
