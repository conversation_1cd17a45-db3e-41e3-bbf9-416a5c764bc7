import { TPermissionCategory } from 'types/user/permissions';
import { SettingsManager } from './settingsManager';
import { rootStore } from '@store/instanse';
import { TUser } from 'types/user/user';
import { Common } from './common';

export class Permissions {
    public static UserList = 'User.getList';
    public static UserGet = 'User.get';
    public static UserUpdate = 'User.update';
    public static UserCreate = 'User.create';
    public static UserRestore = 'User.restore';
    public static UserDelete = 'User.delete';
    public static UserCurrent = 'User.current';
    public static UserBan = 'User.ban';
    public static UserUnban = 'User.unban';
    public static UserChangeRole = 'User.changeRole';

    public static PermissionCategoryList = 'PermissionCategory.getList';
    public static PermissionCategoryGet = 'PermissionCategory.get';
    public static PermissionCategoryUpdate = 'PermissionCategory.update';
    public static PermissionCategoryCreate = 'PermissionCategory.create';
    public static PermissionCategoryRestore = 'PermissionCategory.restore';
    public static PermissionCategoryDelete = 'PermissionCategory.delete';

    public static PermissionList = 'Permission.getList';
    public static PermissionGet = 'Permission.get';
    public static PermissionUpdate = 'Permission.update';
    public static PermissionCreate = 'Permission.create';
    public static PermissionRestore = 'Permission.restore';
    public static PermissionDelete = 'Permission.delete';

    public static RoleList = 'Role.getList';
    public static RoleGet = 'Role.get';
    public static RoleUpdate = 'Role.update';
    public static RoleCreate = 'Role.create';
    public static RoleSyncPermissions = 'Role.syncPermission';

    public static FilterList = 'Filter.getList';
    public static FilterGet = 'Filter.get';
    public static FilterCreate = 'Filter.create';
    public static FilterUpdate = 'Filter.update';
    public static FilterDelete = 'Filter.delete';
    public static FilterRestore = 'Filter.restore';
    public static FilterBulkAdd = 'Filter.bulkAdd';
    public static FilterBulkResult = 'Filter.getBulkResult';

    public static InvitationList = 'Invitation.getList';
    public static InvitationGet = 'Invitation.get';
    public static InvitationUpdate = 'Invitation.update';
    public static InvitationCreate = 'Invitation.create';
    public static InvitationRestore = 'Invitation.restore';
    public static InvitationDelete = 'Invitation.delete';
    public static InvitationResendEmails = 'Invitation.resendEmails';
    public static InvitationBulkAdd = 'Invitation.bulkAdd';
    public static InvitationBulkResult = 'Invitation.getBulkResult';

    public static NotificationList = 'Notification.getList';
    public static NotificationGet = 'Notification.get';

    public static ChatList = 'Chat.getList';
    public static ChatGet = 'Chat.get';
    public static ChatUpdate = 'Chat.update';
    public static ChatCreate = 'Chat.create';
    public static ChatRestore = 'Chat.restore';
    public static ChatDelete = 'Chat.delete';
    public static ChatRegisterSocket = 'Chat.regeisterSocket';
    public static ChatAuth = 'Chat.authChat';
    public static ChatWithManager = 'Chat.chatWithManager';

    public static UserChatList = 'UserChat.getList';
    public static UserChatBan = 'UserChat.ban';
    public static UserChatUnban = 'UserChat.unban';
    public static UserChatMakeAdmin = 'UserChat.makeAdmin';
    public static UserChatUnmakeAdmin = 'UserChat.unmakeAdmin';
    public static UserChatCreate = 'UserChat.create';

    public static ChatMessageList = 'ChatMessage.getList';
    public static ChatMessageGet = 'ChatMessage.get';
    public static ChatMessageSend = 'ChatMessage.sendMessage';

    public static SimulationList = 'Simulation.getList';
    public static SimulationGet = 'Simulation.get';
    public static SimulationUpdate = 'Simulation.update';
    public static SimulationCreate = 'Simulation.create';
    public static SimulationRestore = 'Simulation.restore';
    public static SimulationDelete = 'Simulation.delete';
    public static SimulationTest = 'Simulation.test';
    public static SimulationBulkAdd = 'Simulation.bulkAdd';
    public static SimulationBulkResult = 'Simulation.getBulkResult';

    public static SimulationTaskList = 'SimulationTask.getList';
    public static SimulationTaskGet = 'SimulationTask.get';
    public static SimulationTaskUpdate = 'SimulationTask.update';
    public static SimulationTaskCreate = 'SimulationTask.create';
    public static SimulationTaskRestore = 'SimulationTask.restore';
    public static SimulationTaskDelete = 'SimulationTask.delete';
    public static SimulationTaskBulkAdd = 'SimulationTask.bulkAdd';
    public static SimulationTaskBulkResult = 'SimulationTask.getBulkResult';

    public static SimulationWorkerList = 'SimulationWorker.getList';
    public static SimulationWorkerGet = 'SimulationWorker.get';
    public static SimulationWorkerUpdate = 'SimulationWorker.update';
    public static SimulationWorkerCreate = 'SimulationWorker.create';
    public static SimulationWorkerRestore = 'SimulationWorker.restore';
    public static SimulationWorkerDelete = 'SimulationWorker.delete';
    public static SimulationWorkerBulkAdd = 'SimulationWorker.bulkAdd';
    public static SimulationWorkerBulkResult = 'SimulationWorker.getBulkResult';

    public static SimulationWorkerScheduleEventList = 'SimulationWorkerScheduleEvent.getList';
    public static SimulationWorkerScheduleEventGet = 'SimulationWorkerScheduleEvent.get';
    public static SimulationWorkerScheduleEventUpdate = 'SimulationWorkerScheduleEvent.update';
    public static SimulationWorkerScheduleEventCreate = 'SimulationWorkerScheduleEvent.create';
    public static SimulationWorkerScheduleEventRestore = 'SimulationWorkerScheduleEvent.restore';
    public static SimulationWorkerScheduleEventDelete = 'SimulationWorkerScheduleEvent.delete';
    public static SimulationWorkerScheduleEventBulkAdd = 'SimulationWorkerScheduleEvent.bulkAdd';
    public static SimulationWorkerScheduleEventBulkResult =
        'SimulationWorkerScheduleEvent.getBulkResult';

    public static SimulationScheduleEventList = 'SimulationScheduleEvent.getList';
    public static SimulationScheduleEventGet = 'SimulationScheduleEvent.get';
    public static SimulationScheduleEventUpdate = 'SimulationScheduleEvent.update';
    public static SimulationScheduleEventCreate = 'SimulationScheduleEvent.create';
    public static SimulationScheduleEventDelete = 'SimulationScheduleEvent.delete';
    public static SimulationScheduleEventBulkAdd = 'SimulationScheduleEvent.bulkAdd';
    public static SimulationScheduleEventBulkResult = 'SimulationScheduleEvent.getBulkResult';

    public static SimulationScheduleEventTypeList = 'SimulationScheduleEventType.getList';
    public static SimulationScheduleEventTypeGet = 'SimulationScheduleEventType.get';
    public static SimulationScheduleEventTypeCreate = 'SimulationScheduleEventType.create';
    public static SimulationScheduleEventTypeUpdate = 'SimulationScheduleEventType.update';
    public static SimulationScheduleEventTypeDelete = 'SimulationScheduleEventType.delete';
    public static SimulationScheduleEventTypeRestore = 'SimulationScheduleEventType.restore';

    public static SimulationEventList = 'SimulationEvent.getList';
    public static SimulationEventGet = 'SimulationEvent.get';
    public static SimulationEventUpdate = 'SimulationEvent.update';
    public static SimulationEventCreate = 'SimulationEvent.create';
    public static SimulationEventRestore = 'SimulationEvent.restore';
    public static SimulationEventDelete = 'SimulationEvent.delete';

    public static SessionList = 'Session.getList';
    public static SessionGet = 'Session.get';
    public static SessionUpdate = 'Session.update';
    public static SessionCreate = 'Session.create';
    public static SessionDelete = 'Session.delete';

    public static SessionAssignmentList = 'SessionAssignment.getList';
    public static SessionAssignmentGet = 'SessionAssignment.get';
    public static SessionAssignmentCreate = 'SessionAssignment.create';
    public static SessionAssignmentDelete = 'SessionAssignment.delete';
    public static SessionAssignmentStart = 'SessionAssignment.start';
    public static SessionAssignmentPause = 'SessionAssignment.pause';
    public static SessionAssignmentResume = 'SessionAssignment.resume';
    public static SessionAssignmentStop = 'SessionAssignment.stop';
    public static SessionAssignmentFinish = 'SessionAssignment.finish';
    public static SessionAssignmentBulkAdd = 'SessionAssignment.bulkAdd';
    public static SessionAssignmentBulkResult = 'SessionAssignment.getBulkResult';

    public static SessionAssignmentInfo = 'SessionAssignmentInfo.info';
    public static SessionAssignmentInfoCWT = 'SessionAssignmentInfo.currentWorkerTasks';
    public static SessionAssignmentInfoCTW = 'SessionAssignmentInfo.currentTaskWorkers';
    public static SessionAssignmentInfoWorkers = 'SessionAssignmentInfo.workerExtendedList';
    public static SessionAssignmentInfoTasks = 'SessionAssignmentInfo.taskExtendedList';
    public static SessionAssignmentInfoSimulation = 'SessionAssignmentInfo.simulationInfo';
    public static SessionAssignmentInfoTaskProgress = 'SessionAssignmentInfo.taskProgressesList';

    public static SessionWorkerAssignmentList = 'SessionWorkerAssignment.getList';
    public static SessionWorkerAssignmentGet = 'SessionWorkerAssignment.get';
    public static SessionWorkerAssignmentAssign = 'SessionWorkerAssignment.assign';
    public static SessionWorkerAssignmentCancel = 'SessionWorkerAssignment.cancel';
    public static SessionWorkerAssignmentPrioritize = 'SessionWorkerAssignment.prioritize';
    public static SessionWorkerAssignmentBulkAdd = 'SessionWorkerAssignment.bulkAdd';
    public static SessionWorkerAssignmentBulkResult = 'SessionWorkerAssignment.getBulkResult';

    public static checkPermission(permission: string): boolean {
        const creds = SettingsManager.getConnectionCredentials();
        if (creds?.accessToken == null) return false;
        const permissionSet = rootStore.currentUserStore.getUser?.permissions;
        if (Common.isNullOrUndefined(permissionSet)) {
            return false;
        }
        const permValues: TPermissionCategory[] = Object.values(permissionSet);
        return (
            permValues?.find((item) =>
                item?.entity_permissions?.find((perm) => perm?.name == permission),
            ) != undefined
        );
    }

    public static checkUserPermission(user: TUser, permission: string): boolean {
        if (user == null || user?.permissions == undefined) return false;
        const permissionSet = user?.permissions;
        const permValues: TPermissionCategory[] = Object.values(permissionSet);
        return (
            permValues?.find((item) =>
                item?.entity_permissions?.find((perm) => perm?.name == permission),
            ) != undefined
        );
    }
}
