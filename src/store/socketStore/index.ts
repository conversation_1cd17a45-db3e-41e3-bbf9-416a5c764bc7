import { CRMAPIManager } from '@api/crmApiManager';
import { ChatListResp } from '@api/responseModels/chat/chatListResponse';
import { ChatResp } from '@api/responseModels/chat/chatResponse';
import { ChatMessageListResp } from '@api/responseModels/chatMessage/chatMessageListResponse';
import { ChatMessageResp } from '@api/responseModels/chatMessage/chatMessageResponse';
import { Common } from '@classes/common';
import { message } from 'antd';
import { makeAutoObservable } from 'mobx';
import { TChat, TChatMessage } from 'types/chats/chats';
import { TNotification } from 'types/chats/notification';
import { TDictionary, TUser } from 'types/user/user';

export type TChatExtended = TChat & {
    hasMore: boolean;
    newMessages: number;
};

export type StoreActionResult = null | {
    redir: boolean;
    text: string;
};

class SocketStore {
    public verbose = false;
    public per_page = 10;

    public newNotificationList: TNotification[] = [];
    public notificationList: TNotification[] = [];

    public chatList: TChatExtended[] = [];
    public currentChat: TChatExtended | null = null;
    public totalNewMessages = 0;

    public chatMessageDictionary: TDictionary<TChatMessage[]> = {};

    constructor() {
        makeAutoObservable(this);
    }

    /// Технические методы
    dump() {
        return {
            verbose: this.verbose,
            per_page: this.per_page,
            newNotificationList: this.newNotificationList,
            notificationList: this.notificationList,
            chatList: this.chatList,
            currentChat: this.currentChat,
            totalNewMessages: this.totalNewMessages,
            chatMessageDictionary: this.chatMessageDictionary,
        };
    }

    clearStore() {
        this.verbose = false;
        this.per_page = 10;
        this.newNotificationList = [];
        this.notificationList = [];
        this.chatList = [];
        this.currentChat = null;
        this.totalNewMessages = 0;
        this.chatMessageDictionary = {};
    }

    /// Работа с уведомлениями
    handleNewNotification(data: TNotification) {
        /*
        {
            "id": "e065283c-3d2c-495c-a20d-edb554b010c8",
            "user_id": "bb5af9d4-fdf8-4f56-a725-2852b262f0ce",
            "chat_id": "fa52b0e5-0d6a-4bb7-aea5-9688e7383685",
            "text": "Пришло новое сообщение",
            "entity_type": "chat",
            "entity_name": "9d6cd406-3c5b-458a-866f-02641495c24d",
            "created_at": "2025-07-02T16:53:46",
            "updated_at": "2025-07-02T16:53:46",
            "deleted_at": null
        }
        */
        message.info(`Новое сообщение в чате ${data.chat_id}`);
        const dublicate = this.newNotificationList.find((nli) => nli.id == data.id);
        if (!dublicate) {
            this.newNotificationList.push(data);
        }
    }

    clearNewNotificationList() {
        this.newNotificationList = [];
    }

    /// Работа с чатами
    async fetchChatList(clearList = true, onlyDeleted: boolean): Promise<boolean> {
        let anyErrors = false;
        try {
            const tempChatList = await CRMAPIManager.request<ChatListResp>(async (api) => {
                return await api.getChatList({
                    user_id: null,
                    page: 1,
                    per_page: 100, //this.per_page,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: onlyDeleted ? 'only' : 'null',
                    },
                });
            });
            if (tempChatList.errorMessages) throw tempChatList.errorMessages;
            this.chatList = this.extendChatList(tempChatList.data.data, clearList);
            if (this.currentChat != null) {
                const newCurChat = this.chatList.find((chat) => chat.id == this.currentChat.id);
                if (newCurChat) {
                    this.currentChat = {
                        ...newCurChat,
                        messages: this.currentChat.messages,
                    };
                }
            }
        } catch (errors) {
            console.log(errors);
            anyErrors = true;
        }
        return anyErrors;
    }

    extendChatList(tChatList: TChat[], clearList: boolean): TChatExtended[] {
        if (clearList) {
            return tChatList.map((tcli) => {
                return {
                    ...tcli,
                    hasMore: true,
                    newMessages: 0,
                };
            });
        } else {
            const chatListCopy = [...this.chatList];
            for (let i = 0; i < tChatList.length; i++) {
                const curTChat = tChatList[i];
                if (!chatListCopy.find((clci) => clci.id == curTChat.id)) {
                    chatListCopy.push({
                        ...curTChat,
                        hasMore: true,
                        newMessages: 0,
                    });
                }
            }
            return chatListCopy;
        }
    }

    async getChatList(
        chatId: TChat['id'] | undefined,
        clearList: boolean,
        onlyDeleted: boolean,
    ): Promise<StoreActionResult> {
        let anyErrors = false;
        if (
            this.chatList.length == 0 ||
            clearList ||
            this.chatList.find((ch) => ch.id == chatId) == undefined
        ) {
            anyErrors = await this.fetchChatList(clearList, onlyDeleted);
        }
        if (anyErrors == true) {
            return { redir: true, text: 'Ошибка при загрузке списка чатов' };
        }
        if (chatId == undefined) {
            return null;
        } else {
            const targetChat = this.chatList.find((chat) => chat.id == chatId);
            return targetChat == undefined
                ? { redir: true, text: 'Указанный чат не найден' }
                : null;
        }
    }

    async setCurrentChat(chatId: TChat['id'] | undefined): Promise<StoreActionResult> {
        if (chatId == undefined) {
            this.currentChat = null;
            return null;
        } else {
            const anyErrors = await this.fetchChatMessages(chatId, null);
            if (anyErrors) {
                return { redir: false, text: 'Ошибка при загрузке сообщений' };
            } else {
                const targetChat = this.chatList.find((cli) => cli.id == chatId);
                if (targetChat == undefined) {
                    return { redir: true, text: 'Указанный чат не найден' };
                }
                this.currentChat = {
                    ...targetChat,
                    hasMore: this.chatMessageDictionary[chatId].length == this.per_page,
                    messages: this.chatMessageDictionary[chatId],
                    newMessages: 0,
                };
                this.calculateTotalNew();
                return null;
            }
        }
    }

    calculateTotalNew() {
        let tempTotal = 0;
        for (let i = 0; i < this.chatList.length; i++) {
            tempTotal += this.chatList[i].newMessages;
        }
        this.totalNewMessages = tempTotal;
    }

    makePrivateChatName(creator: TUser, recipient: TUser) {
        let creatorUserName = creator.name;
        if (Common.isNullOrUndefined(creatorUserName) || creatorUserName == '') {
            creatorUserName = 'Вы';
        }
        const otherUserName =
            recipient?.name == null || recipient?.name == '' ? 'Неизвестный' : recipient.name;
        return `${creatorUserName}, ${otherUserName}`;
    }

    async findOrCreatePrivateChat(
        creator: TUser,
        recipient: TUser,
    ): Promise<{ success: boolean; chat_id: string }> {
        const chatList = await CRMAPIManager.request<ChatListResp>(async (api) => {
            return await api.getChatList({
                user_id: creator.id,
                page: 1,
                per_page: 100,
                sort_by: null,
                sort_direction: null,
                filters: {
                    deleted: 'all',
                },
            });
        });
        if (chatList.errorMessages) throw chatList.errorMessages;
        const existingChat = chatList.data.data.find(
            (chat) => chat.type == 'private' && chat.users.find((u) => u.id == recipient.id),
        );
        if (existingChat != undefined) {
            if (existingChat.deleted_at != null) {
                const tryFindNotDeleted = chatList.data.data.find(
                    (chat) =>
                        chat.type == 'private' &&
                        chat.deleted_at == null &&
                        chat.users.find((u) => u.id == recipient.id),
                );
                if (tryFindNotDeleted != undefined) {
                    return { success: true, chat_id: tryFindNotDeleted.id };
                }
            }
            return { success: true, chat_id: existingChat.id };
        } else {
            const newChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.createChat({
                    id: null,
                    chat_name: this.makePrivateChatName(creator, recipient),
                    creator_id: creator.id,
                    type: 'private',
                    users: [creator.id, recipient.id],
                });
            });
            if (newChat.errorMessages) throw newChat.errorMessages;
            return { success: true, chat_id: newChat.data.data.id };
        }
    }

    async findOrCreateGroupChat(
        creator: TUser,
        chat_name: TChat['chat_name'],
        recipients: TUser['id'][],
    ): Promise<{ success: boolean; chat_id: string }> {
        const chatList = await CRMAPIManager.request<ChatListResp>(async (api) => {
            return await api.getChatList({
                user_id: creator.id,
                page: 1,
                per_page: 100,
                sort_by: null,
                sort_direction: null,
                filters: {
                    deleted: 'all',
                },
            });
        });
        if (chatList.errorMessages) throw chatList.errorMessages;
        const existingChat = chatList.data.data.find((chat) => {
            let conclusion = true;
            if (chat.type != 'group') {
                conclusion = false;
            }
            if (conclusion) {
                for (let i = 0; i < recipients.length; i++) {
                    if (chat.users.find((u) => u.id == recipients[i]) == undefined) {
                        conclusion = false;
                        break;
                    }
                }
            }
            return conclusion;
        });
        if (existingChat != undefined) {
            if (existingChat.deleted_at != null) {
                const tryFindNotDeleted = chatList.data.data.find((chat) => {
                    let conclusion = true;
                    if (chat.type != 'group' || chat.deleted_at != null) {
                        conclusion = false;
                    }
                    if (conclusion) {
                        for (let i = 0; i < recipients.length; i++) {
                            if (chat.users.find((u) => u.id == recipients[i]) == undefined) {
                                conclusion = false;
                                break;
                            }
                        }
                    }
                    return conclusion;
                });
                if (tryFindNotDeleted != undefined) {
                    return { success: true, chat_id: tryFindNotDeleted.id };
                }
            }
            return { success: true, chat_id: existingChat.id };
        } else {
            const userList = [...recipients];
            if (!recipients.includes(creator.id)) {
                userList.push(creator.id);
            }
            const newChat = await CRMAPIManager.request<ChatResp>(async (api) => {
                return await api.createChat({
                    id: null,
                    chat_name: chat_name,
                    creator_id: creator.id,
                    type: 'group',
                    users: userList,
                });
            });
            if (newChat.errorMessages) throw newChat.errorMessages;
            return { success: true, chat_id: newChat.data.data.id };
        }
    }

    /// Работа с сообщениями в чатах
    async fetchChatMessages(chat_id: TChat['id'], query: string | null) {
        let anyErrors = false;
        try {
            const messageList = await CRMAPIManager.request<ChatMessageListResp>(async (api) => {
                return await api.getChatMessageList({
                    chat_id: chat_id,
                    count: null,
                    from_id: null,
                    page: 1,
                    per_page: this.per_page,
                    query: query,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                });
            });
            if (messageList.errorMessages) throw messageList.errorMessages;
            this.handleChatMessageList(chat_id, messageList.data.data);
            if (messageList.data.data.length < this.per_page) {
                this.chatList = this.chatList.map((cli) =>
                    cli.id == chat_id ? { ...cli, hasMore: false } : cli,
                );
                if (this.currentChat?.id == chat_id) {
                    this.currentChat = { ...this.currentChat, hasMore: false };
                }
            } else {
                this.chatList = this.chatList.map((cli) =>
                    cli.id == chat_id ? { ...cli, hasMore: true } : cli,
                );
                if (this.currentChat?.id == chat_id) {
                    this.currentChat = { ...this.currentChat, hasMore: true };
                }
            }
        } catch (errors) {
            anyErrors = true;
            console.log(`chat_id: ${chat_id}`);
            console.log(errors);
        }
        return anyErrors;
    }

    getChatMessageList(chat_id: TChat['id']) {
        if (this.chatMessageDictionary[chat_id] == undefined) {
            this.chatMessageDictionary[chat_id] = [];
            return [];
        } else {
            return this.chatMessageDictionary[chat_id];
        }
    }

    handleChatMessageList(chat_id: TChat['id'], messageList: TChatMessage[]) {
        this.chatMessageDictionary[chat_id] = messageList;
        if (this.currentChat?.id == chat_id) {
            this.currentChat.messages = messageList;
        }
    }

    handleChatMessage(data: TChatMessage) {
        if (this.verbose) {
            message.info(`ChatMessages: Новое сообщение в чате ${data.chat_id}`);
        }
        if (this.chatMessageDictionary[data.chat_id] == undefined) {
            this.chatMessageDictionary[data.chat_id] = [];
        }
        const dublicate = this.chatMessageDictionary[data.chat_id].find((cmi) => cmi.id == data.id);
        if (!dublicate) {
            this.chatMessageDictionary[data.chat_id] = [
                data,
                ...this.chatMessageDictionary[data.chat_id],
            ];
            if (data.chat_id != this.currentChat?.id) {
                this.chatList = this.chatList.map((cli) => {
                    if (cli.id == data.chat_id) {
                        return { ...cli, newMessages: cli.newMessages + 1 };
                    } else {
                        return cli;
                    }
                });
                this.calculateTotalNew();
            } else {
                this.currentChat = {
                    ...this.currentChat,
                    messages: this.chatMessageDictionary[this.currentChat.id],
                };
            }
        }
    }

    async sendMessage(chat_id: TChat['id'], text: TChatMessage['text']) {
        let anyErrors = false;
        try {
            const result = await CRMAPIManager.request<ChatMessageResp>(async (api) => {
                return await api.sendMessage(chat_id, text, [], []);
            });
            if (result.errorMessages) throw result.errorMessages;
        } catch (errors) {
            anyErrors = true;
            console.log({ chat_id, text });
            console.log(errors);
        }
        return anyErrors;
    }

    async loadMoreCurrentChatMessages(query: string | null) {
        let result = null;
        if (this.currentChat == null) {
            return { redir: true, text: 'Нет выбранного чата' };
        }
        if (this.currentChat.hasMore == false || this.currentChat.messages.length == 0) {
            return { redir: false, text: 'Все сообщения загружены' };
        }
        try {
            const messageList = await CRMAPIManager.request<ChatMessageListResp>(async (api) => {
                return await api.getChatMessageList({
                    chat_id: this.currentChat.id,
                    count: this.per_page - 2,
                    from_id:
                        this.currentChat.messages[this.currentChat.messages.length - 1]?.order_id,
                    page: null,
                    per_page: null,
                    query: query,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                });
            });
            if (messageList.errorMessages) throw messageList.errorMessages;
            const tMLData = messageList.data.data.length >= 1 ? messageList.data.data.slice(1) : [];
            this.chatMessageDictionary[this.currentChat.id] = [
                ...this.chatMessageDictionary[this.currentChat.id],
                ...tMLData,
            ];
            this.currentChat = {
                ...this.currentChat,
                hasMore: tMLData.length == this.per_page,
                messages: this.chatMessageDictionary[this.currentChat.id],
            };
        } catch (errors) {
            result = { redir: true, text: 'Ошибка при загрузке списка чатов' };
            console.log(`loadMore chat_id: ${this.currentChat?.id}`);
            console.log(errors);
        }
        return result;
    }
}

export { SocketStore };
