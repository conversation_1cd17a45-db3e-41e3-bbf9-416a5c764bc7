import { CRMAPIManager } from '@api/crmApiManager';
import { UserResp } from '@api/responseModels/users/userResp';
import storeLocalStorage from '@hooks/storeLocalStorage';
import { message } from 'antd';
import { action, computed, makeObservable, observable } from 'mobx';
import { TUser } from 'types/user/user';

class CurrentUserStore {
    public user: TUser | object = {};

    constructor() {
        makeObservable(this, {
            user: observable,
            setUser: action,
            getUser: computed,
        });
    }

    get getUser(): TUser | null {
        const { getValue } = storeLocalStorage(this.user);
        return getValue('currentUser');
    }

    setUser(user) {
        const { setValue } = storeLocalStorage({});
        this.user = user;
        setValue('currentUser', user);
    }

    async fetchCurrentUser() {
        try {
            const cUser = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.currentUser();
            });
            if (cUser.errorMessages) throw cUser.errorMessages;
            this.setUser({ ...cUser?.data?.data });
        } catch (errors) {
            message.error('CurrentUserStore: Ошибка подгрузки пользователя');
            console.log(errors);
        }
    }
}

export { CurrentUserStore };
