@use './fonts';
@use './colors';
@use './icons';

html,
body {
    box-sizing: border-box;
    height: 100%;
    margin: 0;
    padding: 0;
    width: 100% !important;

    *,
    *:before,
    *:after {
        box-sizing: inherit;
    }
}
#simbios-root {
    #simbios-app-content {
        .logo-container {
            align-items: center;
            display: flex;
            flex-direction: column;

            .logo-text {
                background-image: url('../assets/text.svg');
                background-repeat: no-repeat;
                background-size: contain;
                height: 20px;
                width: 118px;
            }
            .logo-pure {
                background-image: url('../assets/logo_pure.svg');
                background-repeat: no-repeat;
                background-size: contain;
                height: 148px;
                width: 128px;
            }
        }

        .copy-btn {
            margin: 0 4px;

            svg {
                color: colors.$accentW300;
            }
            /*.ant-btn-icon {
                height: 100%;
                width: 100%;
            }
            .copy-icon {
                @include icons.icon-copy("#68C0FF");
                background-repeat: no-repeat;
                background-size: contain;
                height: 100%;
                width: 100%;
            }*/
        }
        .link-btn {
            margin: 0 4px;

            svg {
                color: colors.$accentW300;
            }
            /*.ant-btn-icon {
                height: 100%;
                width: 100%;
            }
            .link-icon {
                @include icons.icon-link("#68C0FF");
                background-repeat: no-repeat;
                background-size: contain;
                height: 100%;
                width: 100%;
            }*/
        }
    }
}
