//Accent.warm
$accentW0: #ffffff;
$accentW10: #e6f5ff;
$accentW25: #cdeaff;
$accentW50: #b3e0ff;
$accentW100: #9ad5ff;
$accentW200: #81cbff;
$accentW300: #68c0ff;
$accentW400: #4eb5ff;
$accentW500: #35abff;
$accentW600: #2e96df;
$accentW700: #2880bf;
$accentW800: #216b9f;
$accentW900: #1b5680;
$accentW925: #144060;
$accentW950: #0d2b40;
$accentW990: #071520;
$accentW1000: #000000;

//Accent.cold
$accentC0: #ffffff;
$accentC10: #e6faff;
$accentC25: #cdf6ff;
$accentC50: #b3f1ff;
$accentC100: #9aedff;
$accentC200: #81e8ff;
$accentC300: #68e3ff;
$accentC400: #4edfff;
$accentC500: #35daff;
$accentC600: #2ebfdf;
$accentC700: #28a3bf;
$accentC800: #21889f;
$accentC900: #1b6d80;
$accentC925: #145260;
$accentC950: #0d3740;
$accentC990: #071b20;
$accentC1000: #000000;

//Success.warm
$successW0: #ffffff;
$successW10: #f2ffe2;
$successW25: #e5ffc6;
$successW50: #d8ffa9;
$successW100: #ccff8c;
$successW200: #bfff6f;
$successW300: #b2ff53;
$successW400: #a5ff36;
$successW500: #98ff19;
$successW600: #85df16;
$successW700: #72bf13;
$successW800: #5f9f10;
$successW900: #4c800d;
$successW925: #396009;
$successW950: #396009;
$successW990: #132003;
$successW1000: #000000;

//Success.cold
$successC0: #ffffff;
$successC10: #ecffe7;
$successC25: #d8ffcf;
$successC50: #c5ffb6;
$successC100: #b1ff9e;
$successC200: #9eff86;
$successC300: #8aff6e;
$successC400: #77ff55;
$successC500: #63ff3d;
$successC600: #57df35;
$successC700: #4abf2e;
$successC800: #3e9f26;
$successC900: #32801f;
$successC925: #256017;
$successC950: #19400f;
$successC990: #132003;
$successC1000: #000000;

//Warning.warm
$warningW0: #ffffff;
$warningW10: #fff6e4;
$warningW25: #ffecc9;
$warningW50: #ffe3ae;
$warningW100: #ffd993;
$warningW200: #ffd077;
$warningW300: #ffc65c;
$warningW400: #ffbc41;
$warningW500: #ffb326;
$warningW600: #df9d21;
$warningW700: #bf861c;
$warningW800: #9f7018;
$warningW900: #805a13;
$warningW925: #604d0e;
$warningW950: #402d0a;
$warningW990: #201605;
$warningW1000: #000000;

//Warning.cold
$warningC0: #ffffff;
$warningC10: #fff9e4;
$warningC25: #fff2c9;
$warningC50: #ffecae;
$warningC100: #ffe693;
$warningC200: #ffdf77;
$warningC300: #ffd95c;
$warningC400: #ffd241;
$warningC500: #ffcc26;
$warningC600: #dfb321;
$warningC700: #bf991c;
$warningC800: #9f8018;
$warningC900: #806613;
$warningC925: #60430e;
$warningC950: #40330a;
$warningC990: #201a05;
$warningC1000: #000000;

//Error.warm
$errorW0: #ffffff;
$errorW10: #ffe9e2;
$errorW25: #ffd3c5;
$errorW50: #ffbda7;
$errorW100: #ffa78a;
$errorW200: #fe916d;
$errorW300: #fe7b50;
$errorW400: #fe6532;
$errorW500: #fe4f15;
$errorW600: #de4512;
$errorW700: #bf3b10;
$errorW800: #9f310d;
$errorW900: #7f280b;
$errorW925: #5f1e08;
$errorW950: #401405;
$errorW990: #201605;
$errorW1000: #000000;

//Error.cold
$errorC0: #ffffff;
$errorC10: #fee7e5;
$errorC25: #fdcfcb;
$errorC50: #fbb6b1;
$errorC100: #fa9e98;
$errorC200: #f9867e;
$errorC300: #f86e64;
$errorC400: #f6554a;
$errorC500: #f53d30;
$errorC600: #d6352a;
$errorC700: #b82e24;
$errorC800: #99261e;
$errorC900: #7b1f18;
$errorC925: #5c1712;
$errorC950: #3d0f0c;
$errorC990: #1f0806;
$errorC1000: #000000;

//Black
$black0: #ffffff;
$black10: #f7f7f7;
$black25: #efefef;
$black50: #e7e7e7;
$black100: #dfdfdf;
$black200: #d7d7d7;
$black300: #cfcfcf;
$black400: #c7c7c7;
$black500: #bfbfbf;
$black600: #a7a7a7;
$black700: #8f8f8f;
$black800: #777777;
$black900: #606060;
$black925: #484848;
$black950: #303030;
$black990: #181818;
$black1000: #000000;

//Neutral
$neutral0: #ffffff;
$neutral10: #eceef1;
$neutral25: #dadde3;
$neutral50: #c7cbd5;
$neutral100: #b4bac7;
$neutral200: #a1a9b8;
$neutral300: #8f98aa;
$neutral400: #7c869c;
$neutral500: #69758e;
$neutral600: #5c667c;
$neutral700: #4f586b;
$neutral800: #424959;
$neutral900: #353b47;
$neutral925: #272c35;
$neutral950: #1a1d24;
$neutral990: #0d0f12;
$neutral1000: #000000;
