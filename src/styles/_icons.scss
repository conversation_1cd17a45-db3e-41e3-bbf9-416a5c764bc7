@use 'sass:string';

@function str-replace($string, $search, $replace: '') {
    $index: string.index($string, $search);
    @return if(
        $index,
        string.slice($string, 1, $index - 1) + $replace +
            str-replace(string.slice($string, $index + string.length($search)), $search, $replace),
        $string
    );
}

@function hexToUrl($color) {
    $newcolor: str-replace($color, '#', '%23');
    @return $newcolor;
}

@mixin icon-simulations($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M6 4.5C6 3.67157 5.32843 3 4.5 3C3.67157 3 3 3.67157 3 4.5C3 5.32843 3.67157 6 4.5 6C5.32843 6 6 5.32843 6 4.5ZM9.5 3C10.3284 3 11 3.67157 11 4.5C11 5.32843 10.3284 6 9.5 6C9.36563 6 9.23539 5.98233 9.11147 5.94919L5.94919 9.11147C5.98233 9.23539 6 9.36563 6 9.5C6 10.3284 5.32843 11 4.5 11C3.67157 11 3 10.3284 3 9.5C3 8.67157 3.67157 8 4.5 8C4.63437 8 4.76461 8.01767 4.88853 8.05081L8.05081 4.88853C8.01767 4.76461 8 4.63437 8 4.5C8 3.67157 8.67157 3 9.5 3ZM16 4.5C16 3.67157 15.3284 3 14.5 3C13.6716 3 13 3.67157 13 4.5C13 5.32843 13.6716 6 14.5 6C15.3284 6 16 5.32843 16 4.5ZM21 4.5C21 3.67157 20.3284 3 19.5 3C18.6716 3 18 3.67157 18 4.5C18 4.63437 18.0177 4.76461 18.0508 4.88853L14.9393 8H14.5H10C8.89543 8 8 8.89543 8 10V14.5V15L7.96967 14.9697L4.88853 18.0508C4.76461 18.0177 4.63437 18 4.5 18C3.67157 18 3 18.6716 3 19.5C3 20.3284 3.67157 21 4.5 21C5.32843 21 6 20.3284 6 19.5C6 19.3656 5.98233 19.2354 5.94919 19.1115L9.03033 16.0303L9 16H9.5H14C15.1046 16 16 15.1046 16 14V9.5V9.06066L19.1115 5.94919C19.2354 5.98233 19.3656 6 19.5 6C20.3284 6 21 5.32843 21 4.5ZM19.5 8C20.3284 8 21 8.67157 21 9.5C21 10.3284 20.3284 11 19.5 11C18.6716 11 18 10.3284 18 9.5C18 8.67157 18.6716 8 19.5 8ZM19.5 13C20.3284 13 21 13.6716 21 14.5C21 15.3284 20.3284 16 19.5 16C19.3656 16 19.2354 15.9823 19.1115 15.9492L15.9492 19.1115C15.9823 19.2354 16 19.3656 16 19.5C16 20.3284 15.3284 21 14.5 21C13.6716 21 13 20.3284 13 19.5C13 18.6716 13.6716 18 14.5 18C14.6344 18 14.7646 18.0177 14.8885 18.0508L18.0508 14.8885C18.0177 14.7646 18 14.6344 18 14.5C18 13.6716 18.6716 13 19.5 13ZM6 14.5C6 13.6716 5.32843 13 4.5 13C3.67157 13 3 13.6716 3 14.5C3 15.3284 3.67157 16 4.5 16C5.32843 16 6 15.3284 6 14.5ZM19.5 18C20.3284 18 21 18.6716 21 19.5C21 20.3284 20.3284 21 19.5 21C18.6716 21 18 20.3284 18 19.5C18 18.6716 18.6716 18 19.5 18ZM9.5 18C10.3284 18 11 18.6716 11 19.5C11 20.3284 10.3284 21 9.5 21C8.67157 21 8 20.3284 8 19.5C8 18.6716 8.67157 18 9.5 18ZM10 9.5H14.5V14C14.5 14.2761 14.2761 14.5 14 14.5H9.5V10C9.5 9.72386 9.72386 9.5 10 9.5Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-constructor($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.0311 4.84752C7.38426 6.255 6.25447 7.38492 4.84717 8.03184C6.25447 8.67876 7.38426 9.80868 8.0311 11.2161C8.67794 9.80868 9.80772 8.67876 11.215 8.03184C9.80772 7.38492 8.67794 6.25499 8.0311 4.84752ZM8.68083 2.44225C8.4483 1.85258 7.61389 1.85258 7.38136 2.44225L6.77931 3.96894C6.27222 5.25485 5.25445 6.27274 3.9687 6.7799L2.44219 7.38202C1.8526 7.61458 1.8526 8.4491 2.44219 8.68166L3.9687 9.28378C5.25445 9.79093 6.27222 10.8088 6.77931 12.0947L7.38136 13.6214C7.61389 14.2111 8.4483 14.2111 8.68083 13.6214L9.28288 12.0947C9.78997 10.8088 10.8077 9.79093 12.0935 9.28378L13.62 8.68166C14.2096 8.4491 14.2096 7.61458 13.62 7.38202L12.0935 6.7799C10.8077 6.27274 9.78997 5.25485 9.28288 3.96894L8.68083 2.44225ZM16.7279 9.15412L16.7056 9.21077L16.4735 9.79921C16.1693 10.5708 15.5586 11.1815 14.7872 11.4858L14.1988 11.7179L14.1421 11.7402L13.7694 11.8872L13.3359 12.0582C12.9147 12.2244 12.9147 12.8204 13.3359 12.9866L13.7694 13.1575L14.1421 13.3046L14.1988 13.3269L14.7872 13.559C15.5586 13.8633 16.1693 14.474 16.4735 15.2456L16.7056 15.834L16.7279 15.8907L16.8749 16.2635L17.0459 16.6971C17.212 17.1182 17.808 17.1182 17.9741 16.6971L18.1451 16.2635L18.2921 15.8907L18.3144 15.834L18.5465 15.2456C18.8507 14.474 19.4614 13.8633 20.2328 13.559L20.8212 13.3269L20.8779 13.3046L21.2506 13.1575L21.6841 12.9866C22.1053 12.8204 22.1053 12.2244 21.6841 12.0582L21.2506 11.8872L20.8779 11.7402L20.8212 11.7179L20.2329 11.4858C19.4614 11.1815 18.8507 10.5708 18.5465 9.79921L18.3144 9.21077L18.2921 9.15412L18.1451 8.7813L17.9741 8.34774C17.808 7.92655 17.212 7.92655 17.0459 8.34774L16.8749 8.7813L16.7279 9.15412ZM17.51 11.0591C17.1429 11.654 16.6417 12.1553 16.0468 12.5224C16.6417 12.8895 17.1429 13.3908 17.51 13.9857C17.8771 13.3908 18.3783 12.8895 18.9732 12.5224C18.3783 12.1553 17.8771 11.654 17.51 11.0591ZM11.6426 16.2946C11.7616 15.9258 12.2833 15.9258 12.4023 16.2946L12.6579 17.0869C12.8549 17.6977 13.3336 18.1764 13.9443 18.3734L14.7365 18.6291C15.1053 18.7481 15.1053 19.2699 14.7365 19.3889L13.9443 19.6445C13.3336 19.8416 12.8549 20.3203 12.6579 20.931L12.4023 21.7234C12.2833 22.0922 11.7616 22.0922 11.6426 21.7234L11.387 20.931C11.1899 20.3203 10.7113 19.8416 10.1006 19.6445L9.30835 19.3889C8.93961 19.2699 8.93961 18.7481 9.30835 18.6291L10.1006 18.3734C10.7113 18.1764 11.1899 17.6977 11.387 17.0869L11.6426 16.2946Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-management($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M13 2C15.4261 2 17.4486 3.72788 17.904 6.02015C17.9361 6.02337 17.9681 6.02697 18 6.03095C19.9732 6.277 21.5 7.96019 21.5 10V12C21.5 14.0398 19.9732 15.723 18 15.9691C17.9806 15.9715 17.9611 15.9738 17.9415 15.9759C17.6468 17.8892 16.2674 19.4446 14.4511 19.9945C14.2216 21.1384 13.2114 22 12 22C10.6193 22 9.5 20.8807 9.5 19.5C9.5 18.1193 10.6193 17 12 17C13.0127 17 13.8847 17.6021 14.2777 18.4678C15.5717 17.9602 16.4901 16.7042 16.5 15.2324V14.5V7.5L16.5 7C16.5 5.067 14.933 3.5 13 3.5H11C9.067 3.5 7.5 5.067 7.5 7V7.5V14.5V16H6.5C6.33067 16 6.1638 15.9895 6 15.9691C4.02684 15.723 2.5 14.0398 2.5 12V10C2.5 7.96019 4.02684 6.277 6 6.03095C6.03188 6.02697 6.06387 6.02337 6.09597 6.02015C6.55142 3.72788 8.57393 2 11 2H13ZM6 14.45C4.85888 14.2184 4 13.2095 4 12V10C4 8.79052 4.85888 7.78164 6 7.55001V14.45ZM12 20.5C12.5523 20.5 13 20.0523 13 19.5C13 18.9477 12.5523 18.5 12 18.5C11.4477 18.5 11 18.9477 11 19.5C11 20.0523 11.4477 20.5 12 20.5ZM20 10C20 8.79052 19.1411 7.78164 18 7.55001V14.45C19.1411 14.2184 20 13.2095 20 12V10Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-controls($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5ZM7.5 11C9.17556 11 10.5761 9.82259 10.9195 8.25L22 8.25L22 6.75L10.9195 6.75C10.5761 5.17741 9.17556 4 7.5 4C5.82444 4 4.42388 5.17741 4.08054 6.75L2 6.75L2 8.25L4.08054 8.25C4.42388 9.82259 5.82444 11 7.5 11ZM13.0805 14.75L2 14.75L2 16.25L13.0805 16.25C13.4239 17.8226 14.8244 19 16.5 19C18.1756 19 19.5761 17.8226 19.9195 16.25L22 16.25L22 14.75L19.9195 14.75C19.5761 13.1774 18.1756 12 16.5 12C14.8244 12 13.4239 13.1774 13.0805 14.75ZM18.5 15.5C18.5 16.6046 17.6046 17.5 16.5 17.5C15.3954 17.5 14.5 16.6046 14.5 15.5C14.5 14.3954 15.3954 13.5 16.5 13.5C17.6046 13.5 18.5 14.3954 18.5 15.5Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-notifications($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.2772 17.8839L5.91102 15.0666L8.54721 7.50064C9.32052 5.28122 11.67 4.0432 13.9393 4.65939L14.4599 4.80074C16.7292 5.41693 18.1531 7.67953 17.7277 9.99342L16.2772 17.8839ZM3.96209 16.099L3.96453 16.092L3.00024 15.8299L3.38337 14.3797L4.45927 14.6721L7.13329 6.99756C8.16436 4.03834 11.297 2.38765 14.3228 3.20923L14.8433 3.35058C17.8691 4.17216 19.7676 7.18897 19.2004 10.2742L17.729 18.2784L18.8042 18.5706L18.421 20.0209L17.4569 19.7588L17.4561 19.763L16.0042 19.3688L14.4431 18.9449C13.5696 20.5139 11.7308 21.3492 9.93427 20.8614C8.13774 20.3736 6.95746 18.7185 6.9751 16.9171L5.41394 16.4932L3.96209 16.099ZM8.49843 17.3307C8.63897 18.3085 9.33717 19.145 10.3177 19.4112C11.2983 19.6775 12.3157 19.3068 12.9198 18.5313L8.49843 17.3307Z" fill="#{hexToUrl($color)}"/><path d="M22 12C22 14.2091 20.2091 16 18 16C15.7909 16 14 14.2091 14 12C14 9.79086 15.7909 8 18 8C20.2091 8 22 9.79086 22 12Z" fill="%23F53D30"/></svg>');
}
@mixin icon-chats($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 7.47226V17C3.5 17.8284 4.17157 18.5 5 18.5H19C19.8284 18.5 20.5 17.8284 20.5 17V7.47225L12.5057 14.7726C12.2193 15.0341 11.7807 15.0341 11.4943 14.7726L3.5 7.47226ZM20.4353 5.5H3.5647L12 13.2031L20.4353 5.5ZM2 5C2 4.44772 2.44772 4 3 4H21C21.5523 4 22 4.44772 22 5V17C22 18.6569 20.6569 20 19 20H5C3.34315 20 2 18.6569 2 17V5Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-profile($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.3266 18.6244C19.2617 17.0664 20.5 14.6778 20.5 12C20.5 7.30558 16.6944 3.5 12 3.5C7.30558 3.5 3.5 7.30558 3.5 12C3.5 14.6778 4.73826 17.0664 6.67343 18.6244C7.2842 16.2525 9.43744 14.5 12 14.5C14.5626 14.5 16.7158 16.2525 17.3266 18.6244ZM15.9712 19.5172C15.7328 17.5357 14.0457 16 12 16C9.95426 16 8.26721 17.5357 8.02884 19.5172C9.21409 20.1447 10.5655 20.5 12 20.5C13.4345 20.5 14.7859 20.1447 15.9712 19.5172ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22ZM12 11.5C12.8284 11.5 13.5 10.8284 13.5 10C13.5 9.17157 12.8284 8.5 12 8.5C11.1716 8.5 10.5 9.17157 10.5 10C10.5 10.8284 11.1716 11.5 12 11.5ZM12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-arrow($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.47115 11.4693L13.9711 5.99982L15.0289 7.06343L10.0636 12.0011L15.0289 16.9387L13.9711 18.0023L8.47115 12.5329C8.32959 12.3921 8.25 12.2007 8.25 12.0011C8.25 11.8014 8.32959 11.61 8.47115 11.4693Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-search($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 11C17.5 14.5899 14.5899 17.5 11 17.5C7.41015 17.5 4.5 14.5899 4.5 11C4.5 7.41015 7.41015 4.5 11 4.5C14.5899 4.5 17.5 7.41015 17.5 11ZM16.1018 17.1624C14.717 18.3101 12.9391 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11C19 12.9391 18.3101 14.717 17.1624 16.1018L20.9991 19.9384L19.9384 20.9991L16.1018 17.1624Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-filters($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.6666 3.5H1.33325V4.5H14.6666V3.5ZM3.99992 7.5H11.9999V8.5H3.99992V7.5ZM6.66659 11.5H9.33325V12.5H6.66659V11.5Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-plus($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 50 50" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M23.4375 26.5625V37.5H26.5625V26.5625H37.5V23.4375H26.5625V12.5H23.4375V23.4375H12.5V26.5625H23.4375Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-edit($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.3842 3.99201L13.253 5.12382L14.8714 6.7431L16.0056 5.60828C16.3303 5.28334 16.3303 4.75738 16.0056 4.43244L15.5654 3.99201C15.2394 3.66591 14.7101 3.6659 14.3842 3.99201ZM7.20637 11.1737L12.3693 6.00793L13.9877 7.62722L8.82207 12.7956C8.74382 12.8739 8.63757 12.9179 8.52677 12.9179L7.08458 12.9179V11.4676C7.08458 11.3574 7.12838 11.2517 7.20637 11.1737ZM8.52678 14.1667C8.96998 14.1667 9.39497 13.9907 9.70796 13.6775L16.8915 6.49016C17.7034 5.67781 17.7034 4.36291 16.8914 3.55056L16.4512 3.11014C15.6364 2.29488 14.3131 2.29487 13.4983 3.11013L6.32048 10.2918C6.00852 10.6039 5.83333 11.0267 5.83333 11.4676V13.7504C5.83333 13.9803 6.02007 14.1666 6.25042 14.1666L8.52678 14.1667ZM5 3.75H10V2.5H5C3.61929 2.5 2.5 3.61929 2.5 5V15C2.5 16.3807 3.61929 17.5 5 17.5H15C16.3807 17.5 17.5 16.3807 17.5 15V10H16.25V15C16.25 15.6904 15.6904 16.25 15 16.25H5C4.30964 16.25 3.75 15.6904 3.75 15V5C3.75 4.30964 4.30964 3.75 5 3.75Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-message($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 11.3524V11.7992V13.0988L6.14489 12.3479L6.49801 12.1163L6.91033 12.2078C7.26019 12.2855 7.6247 12.3266 8 12.3266C10.7614 12.3266 13 10.0894 13 7.32967C13 4.56992 10.7614 2.33271 8 2.33271C5.23858 2.33271 3 4.56992 3 7.32967C3 8.80951 3.6424 10.1381 4.66691 11.0545L5 11.3524ZM5 14.2942L4.99898 14.2949L4.94088 14.333L4.51622 14.6115C4.29455 14.7569 4 14.5979 4 14.333V13.8253V13.7559V13.7547V11.7992C2.7725 10.7012 2 9.10559 2 7.32967C2 4.01797 4.68629 1.33331 8 1.33331C11.3137 1.33331 14 4.01797 14 7.32967C14 10.6414 11.3137 13.326 8 13.326C7.55131 13.326 7.11412 13.2768 6.69355 13.1835L5 14.2942ZM5.66687 7.99634C5.29879 7.99634 5.00041 7.69795 5.00041 7.32987C5.00041 6.96179 5.29879 6.66341 5.66687 6.66341C6.03495 6.66341 6.33333 6.96179 6.33333 7.32987C6.33333 7.69795 6.03495 7.99634 5.66687 7.99634ZM7.33374 7.32987C7.33374 7.69795 7.63212 7.99634 8.0002 7.99634C8.36828 7.99634 8.66667 7.69795 8.66667 7.32987C8.66667 6.96179 8.36828 6.66341 8.0002 6.66341C7.63212 6.66341 7.33374 6.96179 7.33374 7.32987ZM9.66707 7.32987C9.66707 7.69795 9.96546 7.99634 10.3335 7.99634C10.7016 7.99634 11 7.69795 11 7.32987C11 6.96179 10.7016 6.66341 10.3335 6.66341C9.96546 6.66341 9.66707 6.96179 9.66707 7.32987Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-three-dots($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="25" height="26" viewBox="0 0 25 26" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.345 6.9115C14.345 7.75215 13.6636 8.43362 12.8229 8.43362C11.9823 8.43362 11.3008 7.75215 11.3008 6.9115C11.3008 6.07085 11.9823 5.38937 12.8229 5.38937C13.6636 5.38937 14.345 6.07085 14.345 6.9115ZM14.345 13C14.345 13.8406 13.6636 14.5221 12.8229 14.5221C11.9823 14.5221 11.3008 13.8406 11.3008 13C11.3008 12.1594 11.9823 11.4779 12.8229 11.4779C13.6636 11.4779 14.345 12.1594 14.345 13ZM12.8229 20.6106C13.6636 20.6106 14.345 19.9291 14.345 19.0885C14.345 18.2479 13.6636 17.5664 12.8229 17.5664C11.9823 17.5664 11.3008 18.2479 11.3008 19.0885C11.3008 19.9291 11.9823 20.6106 12.8229 20.6106Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-emoji($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none"><path d="M12 22.2701C17.5228 22.2701 22 17.7929 22 12.2701C22 6.74723 17.5228 2.27008 12 2.27008C6.47715 2.27008 2 6.74723 2 12.2701C2 17.7929 6.47715 22.2701 12 22.2701Z" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 14.2701C8 14.2701 9.5 16.2701 12 16.2701C14.5 16.2701 16 14.2701 16 14.2701" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 9.27008H9.01" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M15 9.27008H15.01" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}
@mixin icon-image($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none"><path d="M19 3.27008H5C3.89543 3.27008 3 4.16551 3 5.27008V19.2701C3 20.3747 3.89543 21.2701 5 21.2701H19C20.1046 21.2701 21 20.3747 21 19.2701V5.27008C21 4.16551 20.1046 3.27008 19 3.27008Z" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 11.2701C10.1046 11.2701 11 10.3747 11 9.27008C11 8.16551 10.1046 7.27008 9 7.27008C7.89543 7.27008 7 8.16551 7 9.27008C7 10.3747 7.89543 11.2701 9 11.2701Z" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M21 15.2701L17.914 12.1841C17.5389 11.8091 17.0303 11.5985 16.5 11.5985C15.9697 11.5985 15.4611 11.8091 15.086 12.1841L6 21.2701" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}
@mixin icon-no-notifications($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.3788 16.5L16.7683 8.65106C16.5861 6.30819 14.6318 4.5 12.2819 4.5H11.7181C9.36818 4.5 7.41389 6.30819 7.23167 8.65105L6.6212 16.5L17.3788 16.5ZM5.73619 8.53474L5.11667 16.5L4 16.5V18L5 18H6.50453H8.12602C8.57006 19.7252 10.1362 21 12 21C13.8638 21 15.4299 19.7252 15.874 18H17.4955H19H20V16.5H18.8833L18.2638 8.53474C18.0208 5.41092 15.4151 3 12.2819 3H11.7181C8.58487 3 5.97915 5.41092 5.73619 8.53474ZM14.292 18L9.70802 18C10.0938 18.883 10.9748 19.5 12 19.5C13.0252 19.5 13.9062 18.883 14.292 18Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-item-list($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.99984 3.6665H5.99984C6.18393 3.6665 6.33317 3.81574 6.33317 3.99984V5.99984C6.33317 6.18393 6.18393 6.33317 5.99984 6.33317H1.99984C1.81574 6.33317 1.6665 6.18393 1.6665 5.99984V3.99984C1.6665 3.81574 1.81574 3.6665 1.99984 3.6665ZM0.666504 3.99984C0.666504 3.26346 1.26346 2.6665 1.99984 2.6665H5.99984C6.73622 2.6665 7.33317 3.26346 7.33317 3.99984V5.99984C7.33317 6.73622 6.73622 7.33317 5.99984 7.33317H1.99984C1.26346 7.33317 0.666504 6.73622 0.666504 5.99984V3.99984ZM1.99984 9.6665H5.99984C6.18393 9.6665 6.33317 9.81574 6.33317 9.99984V11.9998C6.33317 12.1839 6.18393 12.3332 5.99984 12.3332H1.99984C1.81574 12.3332 1.6665 12.1839 1.6665 11.9998V9.99984C1.6665 9.81574 1.81574 9.6665 1.99984 9.6665ZM0.666504 9.99984C0.666504 9.26346 1.26346 8.6665 1.99984 8.6665H5.99984C6.73622 8.6665 7.33317 9.26346 7.33317 9.99984V11.9998C7.33317 12.7362 6.73622 13.3332 5.99984 13.3332H1.99984C1.26346 13.3332 0.666504 12.7362 0.666504 11.9998V9.99984ZM13.9998 3.6665H9.99984C9.81574 3.6665 9.6665 3.81574 9.6665 3.99984V5.99984C9.6665 6.18393 9.81574 6.33317 9.99984 6.33317H13.9998C14.1839 6.33317 14.3332 6.18393 14.3332 5.99984V3.99984C14.3332 3.81574 14.1839 3.6665 13.9998 3.6665ZM9.99984 2.6665C9.26346 2.6665 8.6665 3.26346 8.6665 3.99984V5.99984C8.6665 6.73622 9.26346 7.33317 9.99984 7.33317H13.9998C14.7362 7.33317 15.3332 6.73622 15.3332 5.99984V3.99984C15.3332 3.26346 14.7362 2.6665 13.9998 2.6665H9.99984ZM9.99984 9.6665H13.9998C14.1839 9.6665 14.3332 9.81574 14.3332 9.99984V11.9998C14.3332 12.1839 14.1839 12.3332 13.9998 12.3332H9.99984C9.81574 12.3332 9.6665 12.1839 9.6665 11.9998V9.99984C9.6665 9.81574 9.81574 9.6665 9.99984 9.6665ZM8.6665 9.99984C8.6665 9.26346 9.26346 8.6665 9.99984 8.6665H13.9998C14.7362 8.6665 15.3332 9.26346 15.3332 9.99984V11.9998C15.3332 12.7362 14.7362 13.3332 13.9998 13.3332H9.99984C9.26346 13.3332 8.6665 12.7362 8.6665 11.9998V9.99984Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-item-table($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.3335 5C2.88578 5 3.3335 4.55228 3.3335 4C3.3335 3.44772 2.88578 3 2.3335 3C1.78121 3 1.3335 3.44772 1.3335 4C1.3335 4.55228 1.78121 5 2.3335 5ZM4.66683 3.5H14.6668V4.5H4.66683V3.5ZM14.6668 7.5H4.66683V8.5H14.6668V7.5ZM14.6668 11.5H4.66683V12.5H14.6668V11.5ZM3.3335 8C3.3335 8.55229 2.88578 9 2.3335 9C1.78121 9 1.3335 8.55229 1.3335 8C1.3335 7.44772 1.78121 7 2.3335 7C2.88578 7 3.3335 7.44772 3.3335 8ZM2.3335 13C2.88578 13 3.3335 12.5523 3.3335 12C3.3335 11.4477 2.88578 11 2.3335 11C1.78121 11 1.3335 11.4477 1.3335 12C1.3335 12.5523 1.78121 13 2.3335 13Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-eye-open($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.0002 9.3335L17.0002 5.3335H15.0002V9.3335H17.0002ZM24.6497 11.3272L26.6497 7.99382L24.9347 6.96483L22.9347 10.2982L24.6497 11.3272ZM5.35103 7.99383L7.35103 11.3272L9.06602 10.2982L7.06602 6.96483L5.35103 7.99383ZM6.23919 18.6678C8.44056 21.4906 11.9847 23.3347 15.9989 23.3347C20.0135 23.3347 23.5579 21.4903 25.7592 18.667C24.3777 16.8955 22.4674 15.5095 20.2439 14.7228C20.5155 15.3146 20.6669 15.9731 20.6669 16.6668C20.6669 19.2442 18.5775 21.3335 16.0002 21.3335C13.4229 21.3335 11.3335 19.2442 11.3335 16.6668C11.3335 15.9728 11.485 15.3142 11.7568 14.7222C9.53225 15.5089 7.62108 16.8955 6.23919 18.6678ZM27.8225 18.1012C25.295 14.4176 20.935 12.0003 16 12.0002H15.9995C11.0638 12.0002 6.70337 14.4179 4.17588 18.1021C3.94201 18.4431 3.94203 18.8927 4.17593 19.2336C6.70351 22.9173 11.0637 25.3347 15.9989 25.3347C20.9346 25.3347 25.2951 22.9169 27.8225 19.2327C28.0564 18.8918 28.0564 18.4421 27.8225 18.1012ZM16.0002 19.3335C17.473 19.3335 18.6669 18.1396 18.6669 16.6668C18.6669 15.1941 17.473 14.0002 16.0002 14.0002C14.5274 14.0002 13.3335 15.1941 13.3335 16.6668C13.3335 18.1396 14.5274 19.3335 16.0002 19.3335Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-gear($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.2632 3.5L13.7351 3.5L14.1925 5.09597C14.5266 6.26137 15.7239 6.95258 16.9002 6.65906L18.522 6.25436L20.2537 9.24405L19.0998 10.4329C18.2524 11.3058 18.2524 12.6942 19.0998 13.5671L20.2537 14.756L18.522 17.7457L16.9002 17.3409C15.7239 17.0474 14.5266 17.7386 14.1925 18.904L13.7351 20.5L10.2632 20.5L9.80576 18.9041C9.47171 17.7387 8.27438 17.0474 7.09811 17.341L5.47626 17.7457L3.74459 14.756L4.89854 13.5671C5.74587 12.6942 5.74587 11.3058 4.89854 10.4329L3.74458 9.24404L5.47625 6.25434L7.0981 6.65905C8.27437 6.95257 9.4717 6.26136 9.80575 5.09596L10.2632 3.5ZM16.537 5.20369C16.1449 5.30153 15.7458 5.07113 15.6345 4.68266L14.9693 2.36223C14.9079 2.14778 14.7118 2 14.4887 2L9.50958 2C9.2865 2 9.09041 2.14778 9.02894 2.36223L8.36382 4.68265C8.25247 5.07112 7.85335 5.30152 7.46126 5.20368L5.1092 4.61676C4.89299 4.56281 4.66717 4.65845 4.55548 4.85128L2.06735 9.14699C1.95518 9.34065 1.98536 9.58525 2.14123 9.74584L3.82221 11.4776C4.10466 11.7686 4.10466 12.2314 3.82221 12.5224L2.14124 14.2542C1.98536 14.4148 1.95518 14.6594 2.06735 14.853L4.55548 19.1487C4.66717 19.3416 4.89299 19.4372 5.1092 19.3833L7.46127 18.7963C7.85336 18.6985 8.25248 18.9289 8.36383 19.3174L9.02894 21.6378C9.09041 21.8522 9.28651 22 9.50959 22L14.4887 22C14.7118 22 14.9079 21.8522 14.9693 21.6378L15.6345 19.3173C15.7458 18.9289 16.1449 18.6985 16.537 18.7963L18.8891 19.3832C19.1053 19.4372 19.3311 19.3415 19.4428 19.1487L21.9309 14.853C22.0431 14.6594 22.0129 14.4147 21.8571 14.2542L20.1761 12.5224C19.8936 12.2314 19.8936 11.7686 20.1761 11.4776L21.857 9.74585C22.0129 9.58527 22.0431 9.34066 21.9309 9.147L19.4428 4.85129C19.3311 4.65846 19.1053 4.56282 18.8891 4.61677L16.537 5.20369ZM12 9.5C13.3807 9.5 14.5 10.6193 14.5 12C14.5 13.3807 13.3807 14.5 12 14.5C10.6193 14.5 9.5 13.3807 9.5 12C9.5 10.6193 10.6193 9.5 12 9.5ZM12 8C14.2091 8 16 9.79086 16 12C16 14.2091 14.2091 16 12 16C9.79086 16 7.99999 14.2091 7.99999 12C7.99999 9.79086 9.79086 8 12 8Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-close($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M27.3332 15.9998C27.3332 22.2591 22.2591 27.3332 15.9998 27.3332C9.74061 27.3332 4.6665 22.2591 4.6665 15.9998C4.6665 9.74061 9.74061 4.6665 15.9998 4.6665C22.2591 4.6665 27.3332 9.74061 27.3332 15.9998ZM29.3332 15.9998C29.3332 23.3636 23.3636 29.3332 15.9998 29.3332C8.63604 29.3332 2.6665 23.3636 2.6665 15.9998C2.6665 8.63604 8.63604 2.6665 15.9998 2.6665C23.3636 2.6665 29.3332 8.63604 29.3332 15.9998ZM9.95854 20.6268L14.5856 15.9998L9.95854 11.3727L11.3745 9.96023L15.9998 14.5856L20.6252 9.96022L22.0411 11.3727L17.414 15.9998L22.0412 20.627L20.6252 22.0395L15.9998 17.414L11.3745 22.0393L9.95854 20.6268Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-dashboard($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M19.714 8L13.2582 3.80381C12.7924 3.50108 12.2063 3.5011 11.7406 3.80384L5.28581 7.99977L11.7406 12.1959C12.2063 12.4987 12.7925 12.4987 13.2582 12.196L19.714 8ZM14.0169 2.48614C13.0855 1.88069 11.9132 1.88072 10.9818 2.48621L3.85134 7.12134C3.23347 7.52298 3.23347 8.47651 3.85134 8.87817L10.9818 13.5135C11.9132 14.119 13.0855 14.1191 14.0169 13.5137L21.1485 8.87848C21.7665 8.47685 21.7665 7.52322 21.1486 7.12158L14.0169 2.48614ZM12.5941 16.4511L20.7602 11.5375L21.498 12.8595L13.332 17.773C12.8031 18.0912 12.1512 18.0935 11.6203 17.779L3.50678 12.9725L4.23603 11.6454L12.3496 16.4519C12.4254 16.4969 12.5186 16.4965 12.5941 16.4511ZM20.7364 15.3662L12.5844 20.427C12.5088 20.474 12.4147 20.4752 12.338 20.4302L4.23103 15.6759L3.50778 17.0067L11.6148 21.761C12.1517 22.0759 12.8099 22.0674 13.3391 21.7389L21.4911 16.678L20.7364 15.3662Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-gantt($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.99927 -0.000610352V16.2506C1.99927 16.3887 2.1112 16.5006 2.24927 16.5006H4.50052V16.4994V6.99939C4.50052 6.4471 4.94823 5.99939 5.50052 5.99939H8.50052C9.0528 5.99939 9.50052 6.4471 9.50052 6.99939V16.4994V16.5006H11.5005V16.4994V2.99939C11.5005 2.4471 11.9482 1.99939 12.5005 1.99939H15.5005C16.0528 1.99939 16.5005 2.4471 16.5005 2.99939V16.4994V16.5006H18.5005V18.0006H2.24927C1.28277 18.0006 0.499268 17.2171 0.499268 16.2506V-0.000610352H1.99927ZM6.00052 7.49939V16.4994H8.00052V7.49939H6.00052ZM13.0005 16.4994V3.49939H15.0005V16.4994H13.0005Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-calendar($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M13 3.5V5.5H14.5V3.5H15.5C16.3284 3.5 17 4.17157 17 5V7.01562L2 7.01562V5C2 4.17157 2.67157 3.5 3.5 3.5H4.5V5.5H6V3.5L13 3.5ZM2 8.51562L2 17C2 17.8284 2.67157 18.5 3.5 18.5H15.5C16.3284 18.5 17 17.8284 17 17V8.51562L2 8.51562ZM3.5 2H4.5V0H6V2L13 2V0H14.5V2H15.5C17.1569 2 18.5 3.34315 18.5 5V17C18.5 18.6569 17.1569 20 15.5 20H3.5C1.84315 20 0.5 18.6569 0.5 17V5C0.5 3.34315 1.84315 2 3.5 2Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-reports($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="15" height="18" viewBox="0 0 15 18" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 0H5H10H11.5C13.1569 0 14.5 1.34315 14.5 3V15C14.5 16.6569 13.1569 18 11.5 18H3.5C1.84315 18 0.5 16.6569 0.5 15V3C0.5 1.34315 1.84315 0 3.5 0ZM11.5 1.5C12.3284 1.5 13 2.17157 13 3V15C13 15.8284 12.3284 16.5 11.5 16.5H3.5C2.67157 16.5 2 15.8284 2 15V3C2 2.17157 2.67157 1.5 3.5 1.5V2C3.5 3.10457 4.39543 4 5.5 4H9.5C10.6046 4 11.5 3.10457 11.5 2V1.5ZM11.5147 8.07075L7.01897 12.3797C6.72679 12.6598 6.26514 12.6575 5.97573 12.3746L3.50991 9.96422L4.55845 8.89157L6.50513 10.7945L10.4767 6.98784L11.5147 8.07075ZM5 1.5H10V2C10 2.27614 9.77614 2.5 9.5 2.5H5.5C5.22386 2.5 5 2.27614 5 2V1.5Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-copy($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 6H4C3.44772 6 3 6.44772 3 7V21C3 21.5523 3.44772 22 4 22H15C15.5523 22 16 21.5523 16 21V18H20C20.5523 18 21 17.5523 21 17V5C21 3.34315 19.6569 2 18 2H9C8.44772 2 8 2.44772 8 3V6ZM9.5 6H13C14.6569 6 16 7.34315 16 9V16.5H19.5V5C19.5 4.17157 18.8284 3.5 18 3.5H9.5V6ZM4.5 20.5V7.5H13C13.8284 7.5 14.5 8.17157 14.5 9V20.5H4.5Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-team($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M20 2H4C2.89543 2 2 2.89543 2 4V8C2 9.10457 2.89543 10 4 10H20C21.1046 10 22 9.10457 22 8V4C22 2.89543 21.1046 2 20 2Z" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M20 14H4C2.89543 14 2 14.8954 2 16V20C2 21.1046 2.89543 22 4 22H20C21.1046 22 22 21.1046 22 20V16C22 14.8954 21.1046 14 20 14Z" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M6 6H6.01" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M6 18H6.01" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}
@mixin icon-budget($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M12 8C16.9706 8 21 6.65685 21 5C21 3.34315 16.9706 2 12 2C7.02944 2 3 3.34315 3 5C3 6.65685 7.02944 8 12 8Z" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M21 12C21 13.66 17 15 12 15C7 15 3 13.66 3 12" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M3 5V19C3 20.66 7 22 12 22C17 22 21 20.66 21 19V5" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}
@mixin icon-network($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M14 2V8H20" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 15H15" stroke="#{hexToUrl($color)}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}
@mixin icon-delete($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.99782 5.33184C8.20421 3.9903 9.35852 3 10.7158 3H13.2842C14.6415 3 15.7958 3.99031 16.0022 5.33184L16.105 5.99996H17.4955H19L19 6H20V7.5H18.8833L18.0486 18.2326C17.9271 19.7945 16.6242 21 15.0576 21H8.94239C7.37576 21 6.07291 19.7945 5.95143 18.2326L5.11667 7.5H4V6H5L5 5.99996H6.50453H7.89503L7.99782 5.33184ZM14.5873 5.99996H9.41268L9.48037 5.55993C9.57419 4.95014 10.0989 4.5 10.7158 4.5H13.2842C13.9011 4.5 14.4258 4.95014 14.5196 5.55993L14.5873 5.99996ZM6.6212 7.5L7.44691 18.1163C7.50765 18.8972 8.15908 19.5 8.94239 19.5H15.0576C15.8409 19.5 16.4923 18.8972 16.5531 18.1163L17.3788 7.5L6.6212 7.5Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-restore($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.3459 14.1982C11.374 14.6044 12.5155 14.619 13.5536 14.239C14.5917 13.8591 15.4541 13.1112 15.9772 12.1374C16.3313 11.478 16.5131 10.7461 16.514 10.0083L18.014 10.0101C18.0128 10.993 17.7706 11.9684 17.2986 12.8471C16.6017 14.1448 15.4524 15.1414 14.0691 15.6477C12.6858 16.1539 11.1647 16.1346 9.79471 15.5933C8.88273 15.233 8.15249 14.7412 7.47535 14.0065L7.28837 14.1935L6.59635 14.8855L6.41957 15.0623C6.26208 15.2198 5.9928 15.1082 5.9928 14.8855V14.6355L5.9928 13.6568L5.9928 13.3677L5.9928 12.0064C5.9928 11.7302 6.21666 11.5064 6.4928 11.5064H7.85417H8.14329H9.12195H9.37194C9.59467 11.5064 9.70621 11.7756 9.54872 11.9331L9.37194 12.1099L8.67993 12.8019L8.53696 12.9449C9.09309 13.5606 9.65376 13.9248 10.3459 14.1982ZM8.63015 7.00882C7.89629 7.83551 7.49155 8.90292 7.4928 10.0083L5.9928 10.01C5.99114 8.537 6.53047 7.11462 7.50839 6.01301C8.4863 4.9114 9.83471 4.20725 11.2976 4.03427C12.7604 3.8613 14.2359 4.23154 15.4438 5.07469C15.9092 5.39955 16.3223 5.7859 16.674 6.22008L16.7184 6.17567L17.4104 5.48365L17.5872 5.30688C17.7447 5.14939 18.014 5.26093 18.014 5.48366V5.73366L18.014 6.71231V7.00143L18.014 8.01594C18.014 8.29208 17.7901 8.51594 17.514 8.51594H16.4995H16.2103H15.2317H14.9817C14.759 8.51594 14.6474 8.24666 14.8049 8.08916L14.9817 7.91239L15.6055 7.28853C15.3217 6.91186 14.9782 6.57904 14.5852 6.30467C13.6787 5.67193 12.5715 5.39409 11.4737 5.5239C10.3759 5.6537 9.36402 6.18213 8.63015 7.00882ZM2 18.9999V15.9999H3.5V18.9999C3.5 19.276 3.72386 19.4999 4 19.4999H20C20.2761 19.4999 20.5 19.276 20.5 18.9999V15.9999H22V18.9999C22 20.1044 21.1046 20.9999 20 20.9999H4C2.89543 20.9999 2 20.1044 2 18.9999Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-confirm($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M24.0404 12.0404L14.707 21.3738C14.3165 21.7643 13.6833 21.7643 13.2928 21.3738L7.95947 16.0404L9.37369 14.6262L13.9999 19.2524L22.6261 10.6262L24.0404 12.0404Z" fill="#{hexToUrl($color)}"/></svg>');
}
@mixin icon-link($color) {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="58" height="58" viewBox="0 0 58 58" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M28.7003 32.6958C29.1805 31.7611 29.0705 30.6014 28.3704 29.7697L28.3246 29.7239C26.4935 27.8921 26.4934 24.9221 28.3245 23.0902L31.0483 20.3652C32.8675 18.5452 35.817 18.5451 37.6362 20.365C39.4555 22.185 39.4555 25.1357 37.6364 26.9557L34.7556 29.8378C34.7235 29.8699 34.71 29.9162 34.7194 29.9607C34.8737 30.6923 34.9326 31.44 34.8962 32.1838C34.8901 32.3084 35.0428 32.3791 35.131 32.2908L39.0501 28.37C41.65 25.7689 41.65 21.5517 39.0499 18.9507C36.4499 16.3497 32.2345 16.3498 29.6345 18.9509L26.9107 21.676C24.2989 24.2889 24.299 28.5253 26.9109 31.1382L28.4964 32.7243C28.5578 32.7857 28.6607 32.773 28.7003 32.6958ZM29.2997 25.2968C28.8195 26.2315 28.9295 27.3912 29.6296 28.2229L29.6754 28.2687C31.5065 30.1005 31.5066 33.0705 29.6755 34.9024L26.9517 37.6274C25.1325 39.4474 22.183 39.4475 20.3638 37.6276C18.5445 35.8076 18.5445 32.8569 20.3636 31.0369L23.2444 28.1548C23.2765 28.1227 23.29 28.0764 23.2806 28.0319C23.1263 27.3003 23.0674 26.5526 23.1038 25.8088C23.1099 25.6842 22.9572 25.6135 22.869 25.7017L18.9499 29.6226C16.35 32.2237 16.35 36.4409 18.9501 39.0419C21.5501 41.6429 25.7655 41.6428 28.3655 39.0417L31.0893 36.3166C33.7011 33.7037 33.701 29.4672 31.0891 26.8544L29.5036 25.2682C29.4422 25.2069 29.3393 25.2196 29.2997 25.2968Z" fill="#{hexToUrl($color)}"/></svg>');
}
