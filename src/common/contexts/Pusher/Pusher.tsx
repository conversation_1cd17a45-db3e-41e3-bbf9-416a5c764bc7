import { CRMAPIManager } from '@api/crmApiManager';
import { GlobalConstants } from '@classes/constants';
import { rootStore } from '@store/instanse';
import { message } from 'antd';
import Pusher, { Channel } from 'pusher-js';
import {
    createContext,
    memo,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';

export interface PusherContextValue {
    pusher: Pusher | null;
    socketRegistered: boolean;
    subscribeToChannel: (channelName: string) => Channel | null;
    unsubscribeFromChannel: (channelName: string) => void;
}

const PusherContext = createContext<PusherContextValue>({
    pusher: null,
    socketRegistered: false,
    subscribeToChannel: () => null,
    unsubscribeFromChannel: () => null,
});

interface PusherProviderProps {
    children: React.ReactNode;
    userId: string;
    token: string;
    apiUrl?: string;
}

export const PusherProvider: React.FC<PusherProviderProps> = memo(
    ({ children, userId, token, apiUrl = GlobalConstants.BaseUrl }) => {
        const pusherRef = useRef<Pusher | null>(null);
        const [socketRegistered, setSocketRegistered] = useState<boolean>(false);

        const registerSocket = useCallback(async () => {
            setSocketRegistered(false);
            try {
                const result = await CRMAPIManager.request<{
                    data: { id: string; socket_id: string };
                }>(async (api) => {
                    return await api.registerChatSocket(pusherRef?.current.connection.socket_id);
                });
                if (result.errorMessages) throw result;
                setSocketRegistered(true);
                if (rootStore.socketStore.verbose) {
                    message.info('Pusher: Сокет зарегистрирован');
                }
            } catch (err) {
                setSocketRegistered(false);
                if (
                    rootStore.currentUserStore.getUser?.id != undefined ||
                    rootStore.socketStore.verbose
                ) {
                    message.error('Pusher: Ошибка при регистрации сокета');
                }
                console.log(err);
            }
        }, []);

        async function init() {
            if (userId == null || token == null || !apiUrl) {
                if (pusherRef.current == null) {
                    return undefined;
                } else {
                    setSocketRegistered(false);
                    pusherRef.current.unbind_all();
                    pusherRef.current.disconnect();
                    pusherRef.current = null;
                    return undefined;
                }
            }
            if (rootStore.socketStore.verbose) {
                Pusher.logToConsole = true;
            }
            const pusher = new Pusher('v324h32i4nhu', {
                auth: {
                    headers: { Authorization: `Bearer ${token}` },
                },
                authEndpoint: `${apiUrl}/chats/auth-chat`,
                cluster: '',
                enabledTransports: ['ws'],
                forceTLS: false,
                wsHost: GlobalConstants.SocketUrl,
                wsPort: 6001,
            });

            pusher.connection.bind('connected', () => {
                const sid = pusher.connection.socket_id;
                if (sid) {
                    if (rootStore.socketStore.verbose) {
                        message.info('Pusher: Подключение с socket_id: ' + sid);
                    }
                    registerSocket();
                } else {
                    message.error('Pusher: При подключении отсутствует socket_id');
                }
            });

            pusher.connection.bind('error', () => {
                if (userId != null || rootStore.socketStore.verbose) {
                    message.error('Pusher: Ошибка подключения');
                }
            });

            pusher.connection.bind('disconnected', () => {
                if (
                    rootStore.currentUserStore.getUser?.id != undefined ||
                    rootStore.socketStore.verbose
                ) {
                    message.warning('Pusher: Соединение разорвано');
                }
            });

            pusherRef.current = pusher;
        }

        useEffect(() => {
            init();

            return () => {
                if (pusherRef.current) {
                    pusherRef.current.disconnect();
                }
            };
        }, [
            userId,
            token,
            apiUrl,
        ]);

        const subscribeToChannel = useCallback((channelName: string) => {
            if (!pusherRef.current) return null;

            return pusherRef.current.subscribe(channelName);
        }, []);

        const unsubscribeFromChannel = useCallback((channelName: string) => {
            if (pusherRef.current) {
                pusherRef.current.unsubscribe(channelName);
            }
        }, []);

        const value = useMemo<PusherContextValue>(
            () => ({
                pusher: pusherRef.current,
                socketRegistered,
                subscribeToChannel,
                unsubscribeFromChannel,
            }),
            [
                socketRegistered,
                subscribeToChannel,
                unsubscribeFromChannel,
            ],
        );

        return <PusherContext.Provider value={value}>{children}</PusherContext.Provider>;
    },
);

PusherProvider.displayName = 'PusherProvider';

export const usePusherContext = () => useContext(PusherContext);
