import { createRoot } from 'react-dom/client';
import { App } from '@components/app';
import { GlobalConstants } from '@classes/constants';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import * as Sentry from '@sentry/react';
import {
    createRoutesFromChildren,
    matchRoutes,
    useLocation,
    useNavigationType,
} from 'react-router-dom';
import React from 'react';

import '@styles/style.scss';

export const render = () => {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    const container = document.getElementById(GlobalConstants.MainRoot);
    const root = createRoot(container);
    root.render(<App />);
};

Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_DSN,
    enableLogs: true,

    integrations: [
        Sentry.reactRouterV6BrowserTracingIntegration({
            useEffect: React.useEffect,
            useLocation,
            useNavigationType,
            createRoutesFromChildren,
            matchRoutes,
        }),

        Sentry.captureConsoleIntegration({ levels: ['error', 'warn'] }),
    ],

    tracesSampleRate: 1.0,
});

render();
