import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import './loader.scss';

type TProps = {
    isFullSize?: boolean;
};

const circleIcon = (
    <LoadingOutlined
        style={{ fontSize: 24 }}
        spin
    />
);

const Loader = ({ isFullSize = true }: TProps): JSX.Element => (
    <Spin
        className={`simbios-loader ${isFullSize ? 'full' : ''}`}
        indicator={circleIcon}
        size={'large'}
    />
);

export { Loader };
