import { CRMAPIManager } from '@api/crmApiManager';
import { UserListResp } from '@api/responseModels/users/userListResp';
import { Common, debounce } from '@classes/common';
import { Permissions } from '@classes/permissions';
import { useReactive } from 'ahooks';
import { Col, message, Row, Select } from 'antd';
import { useEffect } from 'react';
import { TMetadata } from 'types/api/metadata';
import { TUser } from 'types/user/user';

import './userSelect.scss';

type TProps = {
    onSelect: (user: TUser['id'] | null) => void;
    placeholder: string;
    selected: TUser['id'] | null;
};

type TState = {
    isLoading: boolean;
    page: number;
    query: string;
    users: TUser[];
    usersMeta: TMetadata | null;
};

const UserSelect = ({
    onSelect,
    placeholder = 'Выберите пользователя',
    selected,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        page: 1,
        query: '',
        users: [],
        usersMeta: null,
    });
    const per_page = 100;
    const [messageApi, contextHolder] = message.useMessage();
    const allowUsage = Permissions.checkPermission(Permissions.UserList);

    async function loadUsers() {
        state.isLoading = true;
        try {
            // TODO (aefl821): переделать на подгрузку через "показать ещё"
            const users = await CRMAPIManager.getAll<UserListResp>(async (api, page, per_page) => {
                return await api.getUserList({
                    query: state.query,
                    page: page, //state.page,
                    per_page: per_page, //per_page,
                    role: null,
                });
            });
            if (users.errorMessages) throw users.errorMessages;
            state.users = users.data.data;
            state.usersMeta = users.data.meta;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка пользователей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        if (!allowUsage) return;
        loadUsers();
    }, []);

    function makeUserSelectOptions() {
        const tempOptions = [];
        tempOptions.push({
            label: (
                <Row className="user-select-row">
                    <Col className="p3">{placeholder}</Col>
                </Row>
            ),
            value: null,
        });
        tempOptions.push(
            ...state.users.map((ui) => {
                return {
                    label: (
                        <Row className="user-select-row">
                            <Col className="p3-strong">{ui.name}</Col>
                            <Col className="p3">{Common.makeRoleName(ui.role)}</Col>
                            {/* TODO (aefl821): поменять роль на сокращённый вид фильтров */}
                            {/*<Col className="user-select-row-filters">
                            <Row>
                                {ui.filters.slice}
                            </Row>
                        </Col>*/}
                        </Row>
                    ),
                    value: ui.id,
                };
            }),
        );
        return tempOptions;
    }

    const searchUsers = debounce(async () => {
        if (state.query.length < 3) return;
        await loadUsers();
    }, 400);

    return allowUsage ? (
        <div className="user-select-wrapper">
            {contextHolder}
            <Select
                className="user-select"
                loading={state.isLoading}
                notFoundContent={
                    <Col
                        className="empty-text p3"
                        flex={1}
                    >
                        <Row style={{ justifyContent: 'center' }}>Таких пользователей нет :)</Row>
                    </Col>
                }
                onDeselect={() => {
                    onSelect(null);
                }}
                onSearch={(value) => {
                    state.query = value;
                    searchUsers();
                }}
                onSelect={(_, opt) => {
                    onSelect(opt.value);
                }}
                options={makeUserSelectOptions()}
                placeholder={placeholder}
                value={selected}
            />
        </div>
    ) : (
        <div className="user-select-disabled" />
    );
};

export { UserSelect };
