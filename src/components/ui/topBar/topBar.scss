@use '../../../styles/icons';
@use '../../../styles/colors';

#uni-layout {
    .top-bar {
        align-items: center;
        align-self: stretch;
        background: colors.$accentW0;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        flex-direction: row;
        height: 96px;
        padding: 24px 48px 24px 48px;
        position: fixed;
        width: calc(100% - 248px);
        z-index: 110;

        &.sbm {
            width: calc(100% - 96px);
        }
        &.left-side {
            justify-content: flex-start;
        }
        &.spaced {
            justify-content: space-between;
            flex-wrap: nowrap;
            overflow-x: hidden;
            overflow-y: hidden;
        }
        h4 {
            margin: 0;
        }
        .button-group {
            max-width: 90%;

            h3 {
                margin: 0;
            }
            .p3 {
                flex-wrap: nowrap;
                overflow-x: scroll;
                overflow-y: hidden;
                scroll-snap-type: x mandatory;

                .ant-btn {
                    flex-shrink: 0;
                }
            }
            .p3::-webkit-scrollbar,
            .p3::-webkit-scrollbar-thumb {
                height: 2px;
                width: 2px;
                background: transparent;
            }
        }
        .search-group,
        .search-group-disabled {
            > .ant-row {
                align-items: center;
                gap: 16px;
            }
            .search {
                .search-input {
                    align-items: center;
                    align-self: stretch;
                    display: flex;
                    height: 48px;
                    padding: 0px 12px;

                    fieldset {
                        border: 2px solid colors.$neutral25;
                    }
                    .ant-input-affix-wrapper {
                        height: 48px;
                    }
                    .ant-input-search-button {
                        height: 48px;
                        width: 48px;
                    }
                }
                /*.search-icon {
                    @include icons.icon-search("#69758E");
                    height: 24px;
                    width: 24px;
                }*/
            }
            .filters {
                justify-content: center;
                .filter-button {
                    background: colors.$neutral25;
                    border-radius: 16px;
                    color: colors.$black1000;
                    height: 32px;
                }
                .filter-button-icon {
                    @include icons.icon-filters('#1A1D24');
                    background-repeat: no-repeat;
                    background-size: contain;
                    height: 16px;
                    width: 16px;
                }
            }
            .view-settings {
            }
        }
        .search-group-disabled {
            display: none;
        }
    }
}
@media only screen and (orientation: portrait) {
    #uni-layout {
        .main-container .top-bar {
            height: 64px;
            padding: 8px 12px 8px 12px;
            width: calc(100% - 40px);

            .button-group {
                max-width: unset;
            }
            .user-name {
                display: none;
            }
        }
    }
}
