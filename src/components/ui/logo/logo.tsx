import { SettingsManager } from '@classes/settingsManager';
import { useNavigate } from 'react-router-dom';

type TProps = {
    showText?: boolean;
    textGap?: string;
    width?: string;
};

const Logo = ({ width = '128px', showText = false, textGap = '14px' }: TProps): JSX.Element => {
    const navigate = useNavigate();

    return (
        <div
            className="logo-container"
            onClick={(e) => {
                e.stopPropagation();
                if (SettingsManager.getConnectionCredentials()?.accessToken == null) return;
                navigate(`/tests/fonts`);
            }}
            style={{ gap: showText ? textGap : 0, width: width }}
        >
            <div
                className="logo-pure"
                style={{ height: `calc(${width} * 1.15)`, width: width }}
            />
            {showText && (
                <div
                    className="logo-text"
                    style={{ display: 'flex' }}
                />
            )}
        </div>
    );
};

export default Logo;
