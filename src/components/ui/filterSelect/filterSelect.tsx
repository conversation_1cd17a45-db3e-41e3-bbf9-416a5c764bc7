import { CRMAPIManager } from '@api/crmApiManager';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { Permissions } from '@classes/permissions';
import { useReactive } from 'ahooks';
import { Col, message, Row, Select } from 'antd';
import { useEffect } from 'react';
import { TFilter } from 'types/filter';
import { FilterButton } from '../filterBtn/filterBtn';

import './filterSelect.scss';

type TProps = {
    fixedTarget: TFilter['target'];
    onSelect: (filters: TFilter['id'][]) => void;
    selected: TFilter['id'][];
};

type TState = {
    filters: TFilter[];
    isLoading: boolean;
    query: string;
};

const FilterSelect = ({ fixedTarget, onSelect, selected }: TProps): JSX.Element => {
    const state = useReactive<TState>({
        filters: [],
        isLoading: false,
        query: '',
    });
    const [messageApi, contextHolder] = message.useMessage();
    const allowUsage = Permissions.checkPermission(Permissions.FilterList);

    async function loadFilters() {
        state.isLoading = true;
        try {
            // TODO (aefl821): переделать на подгрузку через "показать ещё"
            // когда с бэка добавится GET-параметр target
            const filters = await CRMAPIManager.getAll<FilterListResp>(
                async (api, page, per_page) => {
                    return await api.getFilterList({
                        query: null,
                        page: page,
                        per_page: per_page,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: 'null',
                        },
                    });
                },
            );
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data.filter((f) => f.target == fixedTarget);
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        if (!allowUsage) return;
        state.query = '';
        loadFilters();
    }, []);

    function makeFilterSelectOptions() {
        return state.filters.map((fi) => {
            return {
                label: (
                    <FilterButton
                        hex={fi.colorHEX}
                        text={fi.name}
                    />
                ),
                value: fi.id,
            };
        });
    }

    return allowUsage ? (
        <div className="filter-select-wrapper">
            {contextHolder}
            <Select
                className="filter-select"
                filterOption={(
                    input: string,
                    option: {
                        label: React.ReactNode;
                        value: number;
                    },
                ) => {
                    const filter = state.filters.find((fi) => fi.id == option.value);
                    return filter.name.toLocaleLowerCase().includes(input.toLocaleLowerCase());
                }}
                filterSort={(a, b) => {
                    const filterA = state.filters.find((fi) => fi.id == a.value);
                    const filterB = state.filters.find((fi) => fi.id == b.value);
                    return filterA?.name.localeCompare(filterB?.name);
                }}
                loading={state.isLoading}
                mode="multiple"
                notFoundContent={
                    <Col
                        className="empty-text p3"
                        flex={1}
                    >
                        <Row style={{ justifyContent: 'center' }}>Таких фильтров нет :)</Row>
                    </Col>
                }
                onClear={() => onSelect([])}
                onDeselect={(_, opt) => {
                    onSelect(selected.filter((sfi) => sfi != opt.value));
                }}
                onSelect={(_, opt) => {
                    onSelect([...selected, opt.value]);
                }}
                options={makeFilterSelectOptions()}
                placeholder="Выберите фильтр"
                value={selected}
            />
        </div>
    ) : (
        <div className="filter-select-disabled" />
    );
};

export { FilterSelect };
