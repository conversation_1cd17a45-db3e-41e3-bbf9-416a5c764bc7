import { Button, Modal, Row } from 'antd';
import { useBlocker } from 'react-router-dom';
import { useEffect } from 'react';

import './preventLeaving.scss';

type TProps = {
    anyChanges: boolean;
    onSave?: () => Promise<boolean>;
};

const PreventLeaving = ({ anyChanges, onSave }: TProps): JSX.Element => {
    const blocker = useBlocker(
        ({ currentLocation, nextLocation }) =>
            anyChanges && onSave != undefined && currentLocation.pathname !== nextLocation.pathname,
    );

    useEffect(() => {
        const handleBeforeUnload = (event) => {
            if (anyChanges) {
                event.preventDefault();
                const message = 'Остались несохранённые изменения!';
                event.returnValue = message; // For most browsers
                return message; // For some older versions
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [anyChanges]);

    return (
        <>
            {blocker.state == 'blocked' && (
                <Modal
                    footer={
                        <Row className="p2 prompt-dialog-footer">
                            <Button
                                className="proceed-btn"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    blocker.proceed();
                                }}
                            >
                                Пропустить
                            </Button>
                            <Button
                                className="save-btn"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onSave().then((value) => {
                                        if (value) {
                                            blocker.proceed();
                                        } else {
                                            blocker.reset();
                                        }
                                    });
                                }}
                            >
                                Сохранить
                            </Button>
                        </Row>
                    }
                    onCancel={() => blocker.reset()}
                    open
                    title={
                        <div className="prompt-dialog-header">
                            <h4>Несохранённые изменения</h4>
                        </div>
                    }
                    wrapClassName="prompt-dialog"
                >
                    <div className="prompt-dialog-body">
                        <div className="p2">На странице остались несохранённые изменения</div>
                    </div>
                </Modal>
            )}
        </>
    );
};

export { PreventLeaving };
