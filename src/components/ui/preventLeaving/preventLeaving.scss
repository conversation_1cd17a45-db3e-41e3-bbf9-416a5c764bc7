@use '/src/styles/colors';

.prompt-dialog {
    .ant-modal {
        top: 40px;
    }
    .prompt-dialog-header {
        align-items: center;
        display: flex;
        flex-direction: row;

        h4 {
            margin: 0;
        }
    }
    .prompt-dialog-body {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 16px 0;
        row-gap: 12px;
        width: 100%;
    }
    .prompt-dialog-footer {
        column-gap: 16px;
        justify-content: flex-end;
        padding-top: 16px;

        .ant-btn {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral25;
            border-radius: 4px;
            color: colors.$neutral950;
            height: 48px;
            padding: 0 36px;

            &.proceed-btn:hover {
                background: colors.$errorW10;
                border: 2px solid colors.$errorW10;
                color: colors.$errorW500;
            }
            &.save-btn:hover {
                background: colors.$accentW10;
                border: 2px solid colors.$accentW10;
                color: colors.$accentW500;
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .prompt-dialog {
        .ant-modal {
            top: 12px;
        }
    }
}
