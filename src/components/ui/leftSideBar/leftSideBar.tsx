import Logo from '@components/ui/logo/logo';
import { useLocation, useNavigate } from 'react-router-dom';
import { rootStore } from '@store/instanse';
import { observer } from 'mobx-react';
import { Permissions } from '@classes/permissions';

import './leftSideBar.scss';
import { Tooltip } from 'antd';
import { useRef } from 'react';
import useDraggableScroll from 'use-draggable-scroll';

type TMenuEntry = {
    id: string | null;
    disabled: boolean;
    icon: string | null;
    label: string | null;
    active?: (path: string) => boolean;
    onClick: () => void;
};

const LeftSideBar = observer((): JSX.Element => {
    const navigate = useNavigate();
    const location = useLocation();
    const menuGroupRef = useRef<HTMLDivElement>(null);
    const { onMouseDown } = useDraggableScroll(menuGroupRef, {
        direction: 'vertical',
    });

    const menuEntries: TMenuEntry[] = [
        {
            id: 'simulations',
            disabled: !Permissions.checkPermission(Permissions.SimulationList),
            icon: 'icon-simulations',
            label: 'Симуляции',
            active: (path) => path.includes('simulations'),
            onClick: () => {
                navigate('/simulations');
            },
        },
        {
            id: 'constructor',
            disabled: !Permissions.checkPermission(Permissions.SimulationCreate),
            icon: 'icon-constructor',
            label: 'Конструктор',
            active: (path) => path.includes('constructor'),
            onClick: () => {
                navigate('/constructor');
            },
        },
        {
            id: 'management',
            disabled: !Permissions.checkPermission(Permissions.UserList),
            icon: 'icon-management',
            label: 'Менеджмент',
            active: (path) => path.includes('management'),
            onClick: () => {
                navigate('/management/users');
            },
        },
        {
            id: 'controls',
            disabled: !(
                Permissions.checkPermission(Permissions.SessionList) ||
                Permissions.checkPermission(Permissions.SessionAssignmentList)
            ),
            icon: 'icon-controls',
            label: 'Управление',
            active: (path) => path.includes('controls'),
            onClick: () => {
                if (Permissions.checkPermission(Permissions.SessionList)) {
                    navigate('/controls/sessions');
                } else if (Permissions.checkPermission(Permissions.SessionAssignmentList)) {
                    navigate('/controls/assignments');
                }
            },
        },
        {
            id: 'blank1',
            disabled: false,
            icon: null,
            label: null,
            onClick: null,
        },
        {
            id: 'blank2',
            disabled: false,
            icon: null,
            label: null,
            onClick: null,
        },
        {
            id: 'notifications',
            disabled: !Permissions.checkPermission(Permissions.NotificationList),
            icon:
                rootStore.socketStore.newNotificationList.length > 0
                    ? 'icon-notifications'
                    : 'icon-no-notifications',
            label: 'Уведомления',
            active: (path) => path.includes('notifications'),
            onClick: () => {
                navigate('/notifications');
            },
        },
        {
            id: 'chats',
            disabled: !Permissions.checkPermission(Permissions.ChatList),
            icon: 'icon-chats',
            label: 'Чаты',
            active: (path) => path.includes('chats'),
            onClick: () => {
                navigate('/chats');
            },
        },
        {
            id: 'profile',
            disabled: false,
            icon: 'icon-profile',
            label: 'Профиль',
            active: (path) => path.includes('lk'),
            onClick: () => {
                navigate('/lk');
            },
        },
        {
            id: 'blank3',
            disabled: false,
            icon: null,
            label: null,
            onClick: null,
        },
        {
            id: 'minimize',
            disabled: false,
            icon: rootStore.ingameStore.sideBarMinimized ? 'icon-minimize' : 'icon-maximize',
            label: null,
            onClick: () => {
                rootStore.ingameStore.sideBarMinimized = !rootStore.ingameStore.sideBarMinimized;
            },
        },
    ];

    function makeClassName(me: TMenuEntry) {
        if (me.disabled) {
            return 'menu-item-disabled';
        } else {
            return me.onClick == null
                ? me.icon == null
                    ? 'menu-item-blank'
                    : 'menu-item-disabled'
                : me.icon == null
                  ? 'menu-item-blank'
                  : me.active != undefined && me.active(location.pathname)
                    ? 'menu-item-active'
                    : 'menu-item';
        }
    }

    return (
        <div
            className={
                rootStore.ingameStore.sideBarMinimized ? 'left-side-bar-mini' : 'left-side-bar'
            }
        >
            <div className="logo-outer-container">
                <Logo
                    showText={!rootStore.ingameStore.sideBarMinimized}
                    width={rootStore.ingameStore.sideBarMinimized ? '70px' : '96px'}
                />
            </div>
            <div
                className="menu-container"
                ref={menuGroupRef}
                onMouseDown={onMouseDown}
                onScroll={(e) => {
                    rootStore.ingameStore.leftMenuScrollPosition = (
                        e.nativeEvent.target as HTMLDivElement
                    ).scrollTop;
                }}
            >
                {menuEntries.map((me) => (
                    <div
                        key={me.id}
                        className={makeClassName(me)}
                        onClick={() => {
                            if (me.disabled) return;
                            if (me.onClick == null) return;
                            me.onClick();
                        }}
                    >
                        {rootStore.ingameStore.sideBarMinimized && me.label ? (
                            <Tooltip
                                placement="right"
                                title={me.label}
                            >
                                <div className="menu-item-inner">
                                    {me.icon != null && (
                                        <div className={`menu-item-icon ${me.icon}`}></div>
                                    )}
                                    {me.label != null &&
                                        !rootStore.ingameStore.sideBarMinimized && (
                                            <div className="menu-item-label p1">{me.label}</div>
                                        )}
                                </div>
                            </Tooltip>
                        ) : (
                            <div className="menu-item-inner">
                                {me.icon != null && (
                                    <div className={`menu-item-icon ${me.icon}`}></div>
                                )}
                                {me.label != null && !rootStore.ingameStore.sideBarMinimized && (
                                    <div className="menu-item-label p1">{me.label}</div>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
});

export default LeftSideBar;
