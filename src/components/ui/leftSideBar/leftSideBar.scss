@use '../../../styles/icons';
@use '../../../styles/colors';

#uni-layout {
    .left-side-bar,
    .left-side-bar-mini {
        align-items: center;
        background: #fff;
        bottom: 0;
        box-shadow: 4px 0px 4px 0px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        justify-content: space-between;
        padding: 64px 24px 24px 24px;
        position: fixed;
        row-gap: 32px;
        top: 0;
        width: 248px;
        z-index: 100;

        .logo-outer-container {
            height: 140px;
        }
        .menu-container {
            align-items: flex-start;
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
            height: 100%;
            justify-content: center;
            row-gap: 16px;
            overflow-x: hidden;
            overflow-y: scroll;
            scroll-snap-type: y mandatory;
            width: 168px;

            &::-webkit-scrollbar,
            &::-webkit-scrollbar-thumb {
                height: 2px;
                width: 2px;
                background: transparent;
            }
            .menu-item,
            .menu-item-active,
            .menu-item-disabled {
                align-items: center;
                align-self: stretch;
                cursor: pointer;
                display: flex;
                height: 32px;
                justify-content: center;

                .menu-item-inner {
                    align-items: center;
                    display: flex;
                    gap: 8px;

                    .menu-item-icon {
                        background-position: center center;
                        background-repeat: no-repeat;
                        background-size: contain;
                        color: colors.$neutral950;
                        height: 24px;
                        width: 24px;
                    }
                    .menu-item-label {
                        color: colors.$neutral950;
                        display: flex;
                        flex-direction: column;
                        height: 24px;
                        justify-content: center;
                        width: 125px;
                    }
                    .icon-simulations {
                        @include icons.icon-simulations('#272C35'); //neutral950
                    }
                    .icon-constructor {
                        @include icons.icon-constructor('#272C35');
                    }
                    .icon-management {
                        @include icons.icon-management('#272C35');
                    }
                    .icon-controls {
                        @include icons.icon-controls('#272C35');
                    }
                    .icon-notifications {
                        @include icons.icon-notifications('#272C35');
                    }
                    .icon-no-notifications {
                        @include icons.icon-no-notifications('#272C35');
                    }
                    .icon-chats {
                        @include icons.icon-chats('#272C35');
                    }
                    .icon-profile {
                        @include icons.icon-profile('#272C35');
                    }
                    .icon-minimize,
                    .icon-maximize {
                        @include icons.icon-arrow('#272C35');
                    }
                    .icon-minimize {
                        transform: rotate(180deg);
                    }
                }
                &:hover {
                    background-color: colors.$accentW50;
                    .menu-item-inner {
                        .icon-simulations {
                            @include icons.icon-simulations('#1B5680'); //accentW900
                        }
                        .icon-constructor {
                            @include icons.icon-constructor('#1B5680');
                        }
                        .icon-management {
                            @include icons.icon-management('#1B5680');
                        }
                        .icon-controls {
                            @include icons.icon-controls('#1B5680');
                        }
                        .icon-notifications {
                            @include icons.icon-notifications('#1B5680');
                        }
                        .icon-no-notifications {
                            @include icons.icon-no-notifications('#1B5680');
                        }
                        .icon-chats {
                            @include icons.icon-chats('#1B5680');
                        }
                        .icon-profile {
                            @include icons.icon-profile('#1B5680');
                        }
                        .icon-minimize {
                            @include icons.icon-arrow('#1B5680');
                        }
                        .menu-item-label {
                            color: colors.$accentW900;
                        }
                    }
                }
            }
            .menu-item-disabled,
            .menu-item-disabled:hover {
                .menu-item-inner {
                    .menu-item-icon {
                        color: colors.$black600;
                    }
                    .menu-item-label {
                        color: colors.$black600;
                    }
                    .icon-simulations {
                        @include icons.icon-simulations('#A7A7A7'); //neutral950
                    }
                    .icon-constructor {
                        @include icons.icon-constructor('#A7A7A7');
                    }
                    .icon-management {
                        @include icons.icon-management('#A7A7A7');
                    }
                    .icon-controls {
                        @include icons.icon-controls('#A7A7A7');
                    }
                    .icon-notifications {
                        @include icons.icon-notifications('#A7A7A7');
                    }
                    .icon-no-notifications {
                        @include icons.icon-no-notifications('#A7A7A7');
                    }
                    .icon-chats {
                        @include icons.icon-chats('#A7A7A7');
                    }
                    .icon-profile {
                        @include icons.icon-profile('#A7A7A7');
                    }
                    .icon-minimize,
                    .icon-maximize {
                        @include icons.icon-arrow('#A7A7A7');
                    }
                    .icon-minimize {
                        transform: rotate(180deg);
                    }
                }
            }
            .menu-item-blank,
            .menu-item-disabled {
                height: 32px;
            }
            .menu-item-active .menu-item-inner {
                .icon-simulations {
                    @include icons.icon-simulations('#4EB5FF');
                }
                .icon-constructor {
                    @include icons.icon-constructor('#4EB5FF');
                }
                .icon-management {
                    @include icons.icon-management('#4EB5FF');
                }
                .icon-controls {
                    @include icons.icon-controls('#4EB5FF');
                }
                .icon-notifications {
                    @include icons.icon-notifications('#4EB5FF');
                }
                .icon-no-notifications {
                    @include icons.icon-no-notifications('#4EB5FF');
                }
                .icon-chats {
                    @include icons.icon-chats('#4EB5FF');
                }
                .icon-profile {
                    @include icons.icon-profile('#4EB5FF');
                }
                .menu-item-label {
                    color: colors.$accentW500;
                }
            }
        }
    }
    .left-side-bar-mini {
        padding: 72px 8px 24px 8px;
        width: 96px;

        .menu-container {
            width: 80px;
        }
        .logo-outer-container {
            height: 132px;
        }
    }
}
@media only screen and (orientation: portrait) {
    #uni-layout {
        .left-side-bar,
        .left-side-bar-mini {
            gap: 132px;
            min-height: 100%;
            padding: 64px 24px 24px 24px;
            position: fixed;
            width: 100%;
            z-index: 100;

            .logo-outer-container {
                height: 140px;
            }
            .menu-container {
                gap: 8px;
            }
        }
        .left-side-bar-mini {
            padding: 36px 4px 12px 4px;
            width: 40px;

            .menu-container {
                width: 32px;
            }
            .logo-outer-container {
                height: 50px;

                .logo-container {
                    width: 32px !important;

                    .logo-pure {
                        height: calc(32px * 1.15) !important;
                        width: 32px !important;
                    }
                }
            }
        }
    }
}
