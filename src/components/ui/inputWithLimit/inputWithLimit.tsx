import { Col, Input, Row } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React from 'react';

import './inputWithLimit.scss';

type IWLProps = {
    allowClear?: boolean;
    counterText?: string;
    disabled?: boolean;
    inputType: 'input' | 'textarea';
    max?: number;
    maxLength?: number;
    min?: number;
    onChange: React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
    placeholder?: string;
    prefix?: React.ReactNode;
    required?: boolean;
    rows?: number;
    step?: number;
    suffix?: React.ReactNode;
    type?: 'number' | 'date' | 'text';
    value: number | string;
};

const InputWLimit: React.FC<IWLProps> = ({
    allowClear = false,
    counterText = 'Максимальное количество знаков',
    disabled = false,
    inputType,
    max,
    maxLength,
    min,
    onChange,
    placeholder = '',
    prefix,
    required = false,
    rows,
    step,
    suffix,
    type = 'text',
    value,
}): JSX.Element => {
    return (
        <div className="iwl">
            <Col>
                <Row>
                    {inputType == 'input' ? (
                        <Input
                            allowClear={allowClear}
                            className="input-w-limit"
                            disabled={disabled}
                            maxLength={maxLength}
                            onChange={onChange}
                            max={max}
                            min={min}
                            placeholder={placeholder}
                            prefix={prefix}
                            showCount={false}
                            status={required && (value == '' || value == null) ? 'error' : null}
                            step={step}
                            suffix={suffix}
                            type={type}
                            value={value}
                        />
                    ) : (
                        <TextArea
                            allowClear={allowClear}
                            autoSize={{
                                minRows: 2,
                                maxRows: rows,
                            }}
                            className="input-w-limit"
                            disabled={disabled}
                            maxLength={maxLength}
                            onChange={onChange}
                            placeholder={placeholder}
                            showCount={false}
                            status={required && (value == '' || value == null) ? 'error' : null}
                            value={value}
                        />
                    )}
                </Row>
                <Row className="iwl-count desc-l">
                    <span className="iwl-count-prefix">{counterText}</span>
                    {maxLength != null && (
                        <span className="iwl-count-value">
                            {`${('' + value).length}/${maxLength}`}
                        </span>
                    )}
                </Row>
            </Col>
        </div>
    );
};

export default InputWLimit;
