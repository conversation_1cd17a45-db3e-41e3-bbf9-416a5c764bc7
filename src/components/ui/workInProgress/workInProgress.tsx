import { Row } from 'antd';

export const WorkInProgress = (): JSX.Element => {
    return (
        <div
            className="wip-container"
            style={{
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                height: '100%',
                width: '100%',
            }}
        >
            <div
                className="wip-inner"
                style={{
                    alignItems: 'center',
                    background: '#fff',
                    display: 'flex',
                    flexDirection: 'column',
                    height: '350px',
                    justifyContent: 'center',
                    rowGap: '50px',
                    width: '680px',
                }}
            >
                <Row>
                    <h4
                        style={{
                            fontWeight: 600,
                            margin: 0,
                        }}
                    >
                        Мы ещё работаем над этим
                    </h4>
                </Row>
                <Row>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="138"
                        height="138"
                        viewBox="0 0 138 138"
                        fill="none"
                    >
                        <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M59.0811 20.5634L78.9093 20.5634L81.5219 29.6782C83.4297 36.334 90.2678 40.2816 96.9857 38.6052L106.248 36.2939L116.138 53.3685L109.548 60.158C104.709 65.1435 104.709 73.0726 109.548 78.0582L116.138 84.8477L106.248 101.922L96.9857 99.611C90.2679 97.9346 83.4297 101.882 81.5219 108.538L78.9093 117.653L59.0811 117.653L56.4685 108.538C54.5607 101.882 47.7225 97.9347 41.0047 99.6111L31.7421 101.922L21.8523 84.8479L28.4426 78.0582C33.2819 73.0727 33.2819 65.1436 28.4426 60.1581L21.8522 53.3684L31.742 36.2938L41.0046 38.6052C47.7225 40.2815 54.5606 36.3339 56.4684 29.6781L59.0811 20.5634ZM94.9116 30.2934C92.6723 30.8522 90.3929 29.5363 89.757 27.3177L85.9584 14.0654C85.6073 12.8407 84.4874 11.9967 83.2134 11.9967L54.7769 11.9967C53.5029 11.9967 52.383 12.8407 52.0319 14.0654L48.2333 27.3177C47.5974 29.5362 45.318 30.8521 43.0787 30.2933L29.6457 26.9413C28.4109 26.6332 27.1213 27.1794 26.4834 28.2807L12.2733 52.8141C11.6327 53.9201 11.805 55.3171 12.6953 56.2343L22.2956 66.1248C23.9087 67.7866 23.9087 70.4297 22.2956 72.0915L12.6953 81.982C11.8051 82.8991 11.6327 84.2961 12.2733 85.4021L26.4834 109.936C27.1213 111.037 28.411 111.583 29.6458 111.275L43.0788 107.923C45.3181 107.364 47.5975 108.68 48.2334 110.899L52.0319 124.151C52.383 125.375 53.5029 126.219 54.777 126.219L83.2134 126.219C84.4874 126.219 85.6074 125.375 85.9584 124.151L89.757 110.898C90.3929 108.68 92.6723 107.364 94.9116 107.923L108.345 111.275C109.579 111.583 110.869 111.037 111.507 109.935L125.717 85.402C126.358 84.296 126.185 82.899 125.295 81.9819L115.695 72.0914C114.082 70.4296 114.082 67.7865 115.695 66.1247L125.295 56.2343C126.185 55.3172 126.358 53.9202 125.717 52.8142L111.507 28.2808C110.869 27.1795 109.579 26.6333 108.345 26.9414L94.9116 30.2934ZM69.0001 54.8302C76.8855 54.8302 83.2779 61.2226 83.2779 69.1081C83.2779 76.9935 76.8855 83.3859 69.0001 83.3859C61.1146 83.3859 54.7222 76.9935 54.7222 69.1081C54.7222 61.2226 61.1146 54.8302 69.0001 54.8302ZM69.0001 46.2635C81.6168 46.2635 91.8446 56.4914 91.8446 69.1081C91.8446 81.7248 81.6168 91.9526 69.0001 91.9526C56.3834 91.9526 46.1555 81.7248 46.1555 69.1081C46.1555 56.4914 56.3834 46.2635 69.0001 46.2635Z"
                            fill="#ECEEF1"
                        />
                    </svg>
                </Row>
            </div>
        </div>
    );
};
