import { Segmented } from 'antd';

import './listModeSwitch.scss';

type TProps = {
    onChange: (value: 'list' | 'table') => void;
    value: 'list' | 'table';
};

const ListModeSwitch = ({ onChange, value }: TProps): JSX.Element => {
    return (
        <Segmented
            className="list-mode-switch"
            onChange={onChange}
            options={[
                { value: 'list', icon: <div className="icon-list" /> },
                { value: 'table', icon: <div className="icon-table" /> },
            ]}
            shape="round"
            value={value}
        />
    );
};

export { ListModeSwitch };
