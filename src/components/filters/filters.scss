@use '/src/styles/colors';
@use '/src/styles/icons';

.filters-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .filters-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        > .ant-col {
            display: flex;
            flex-direction: column;
            row-gap: 16px;
            width: 100%;

            .header-row {
                align-items: center;
                column-gap: 20px;
                justify-content: space-between;
                width: 100%;

                h4 {
                    color: colors.$neutral950;
                    margin-top: 0;
                }
            }
            .body-row {
                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: 100%;

                    .table-actions {
                        align-items: center;
                        border: 1px solid colors.$neutral10;
                        border-radius: 8px;
                        column-gap: 8px;
                        min-height: 58px;
                        padding: 12px;
                        row-gap: 8px;
                        width: 100%;

                        .target-block-btn {
                            .three-dots-icon {
                                @include icons.icon-three-dots('#1A1D24');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 24px;
                                transform: rotate(90deg);
                                transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                                width: 24px;
                            }
                            &:hover {
                                border: 2px solid colors.$accentW500;

                                .three-dots-icon {
                                    @include icons.icon-three-dots('#35ABFF');
                                }
                            }
                        }
                    }
                    .table-container {
                        width: 100%;

                        .ant-table-wrapper {
                            width: 100%;

                            .ant-table-placeholder .ant-table-cell {
                                background: colors.$accentW0;
                                color: colors.$neutral950;

                                .ant-col {
                                    display: flex;
                                    flex-direction: column;
                                    row-gap: 8px;
                                }
                                .ant-row {
                                    justify-content: center;
                                }
                            }
                            .ant-table-tbody .ant-table-row {
                                .table-id {
                                    .open-btn {
                                        height: 20px;
                                        margin: 0 4px;
                                        width: 20px;

                                        svg {
                                            color: colors.$accentW300;
                                        }
                                        &:disabled {
                                            svg {
                                                color: colors.$neutral300;
                                            }
                                        }
                                    }
                                }
                                .table-user {
                                    align-items: center;
                                    column-gap: 12px;
                                    flex-wrap: nowrap;
                                    width: fit-content;

                                    &:has(.creator-btn) {
                                        cursor: pointer;
                                    }
                                    .user-avatar {
                                        align-items: center;
                                        background-color: colors.$accentC50;
                                        border-radius: 50%;
                                        color: colors.$neutral900;
                                        display: flex;
                                        flex-direction: column;
                                        height: 32px;
                                        justify-content: center;
                                        min-width: 32px;
                                        max-width: 32px;
                                    }
                                    .user-name {
                                        .ant-btn-link {
                                            padding: 0;
                                        }
                                    }
                                }
                                .desc-l {
                                    color: colors.$neutral900;
                                }
                                .table-status {
                                    align-items: center;
                                    column-gap: 8px;
                                    row-gap: 4px;
                                    width: 100%;
                                }
                                .lighter-tone {
                                    color: colors.$neutral700;
                                }
                                .no-filter-name {
                                    color: colors.$errorC200;
                                }
                                .target-block-btn {
                                    .three-dots-icon {
                                        @include icons.icon-three-dots('#1A1D24');
                                        background-repeat: no-repeat;
                                        background-size: contain;
                                        height: 24px;
                                        transform: rotate(90deg);
                                        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                                        width: 24px;
                                    }
                                    &:hover {
                                        border: 2px solid colors.$accentW500;

                                        .three-dots-icon {
                                            @include icons.icon-three-dots('#35ABFF');
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .controls-row {
                column-gap: 24px;
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;

                    > .ant-row {
                        column-gap: 24px;
                        row-gap: 8px;

                        .ant-btn {
                            background: colors.$accentW0;
                            border: 2px solid colors.$neutral25;
                            border-radius: 4px;
                            color: colors.$neutral950;
                            height: 48px;

                            &:hover {
                                background: colors.$accentW10;
                                border: 2px solid colors.$accentW10;
                                color: colors.$accentW500;
                            }
                        }
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .filters-profile {
        min-width: 334px;

        .filters-card {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            > .ant-col {
                max-width: 314px;
                min-width: 314px;
            }
        }
    }
}
