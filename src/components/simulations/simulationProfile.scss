@use '../../styles/colors';
@use '../../styles/icons';

.sim-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .sim-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .id-row {
            h3 {
                margin-top: 0;
            }
        }
        h3,
        .p1 {
            color: colors.$neutral950;
        }
        .desc-l {
            color: colors.$neutral900;
        }
        .info-row {
            column-gap: 20px;
            flex-wrap: wrap;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: calc(50% - 10px);

                > .ant-row {
                    align-items: center;
                    column-gap: 8px;
                    display: flex;
                    flex-direction: row;
                    flex-wrap: nowrap;

                    .labeled-input {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                        width: 100%;

                        .creator-row {
                            align-items: center;
                            column-gap: 12px;
                            cursor: pointer;
                            flex-wrap: nowrap;
                            width: fit-content;

                            .creator-avatar {
                                align-items: center;
                                background-color: colors.$accentC50;
                                border-radius: 50%;
                                color: colors.$neutral900;
                                display: flex;
                                flex-direction: column;
                                height: 40px;
                                justify-content: center;
                                min-width: 40px;
                                max-width: 40px;
                            }
                            .creator-name {
                                .creator-btn {
                                    padding: 0;
                                }
                            }
                        }
                        .lighter-tone {
                            color: colors.$neutral700;
                        }
                    }
                }
            }
        }
        .dropdowns {
            > .ant-col {
                display: flex;
                flex-direction: column;
                width: 100%;

                .ant-collapse {
                    border-radius: 8px;
                    border: 2px solid colors.$neutral25;

                    .ant-collapse-item {
                        border-bottom: 2px solid colors.$neutral25;
                        min-height: 48px;

                        .ant-collapse-header {
                            align-items: center;

                            h6 {
                                margin: 0;
                            }
                            .expand-icon {
                                @include icons.icon-arrow('#272C35');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 32px;
                                width: 32px;
                                transform: rotate(270deg);
                            }
                        }
                        &.ant-collapse-item-active {
                            .ant-collapse-header {
                                .expand-icon {
                                    @include icons.icon-arrow('#272C35');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    height: 32px;
                                    width: 32px;
                                    transform: rotate(90deg);
                                }
                            }
                        }
                        .ant-collapse-content {
                            background: colors.$neutral25;

                            .link-task-card,
                            .link-worker-card,
                            .link-event-card {
                                background: colors.$accentW0;
                                border: 0.5px solid colors.$neutral100;
                                border-radius: 1px;
                                cursor: pointer;
                                display: flex;
                                flex-direction: column;
                                flex-wrap: nowrap;
                                justify-content: space-between;
                                height: 36px;
                                padding: 3px;
                                width: 100px;

                                .grayish {
                                    color: colors.$neutral700;
                                }
                            }
                            .link-worker-card {
                                .desc-s-strong {
                                    -webkit-line-clamp: 1;
                                    -webkit-box-orient: vertical;
                                    display: -webkit-box;
                                    max-height: 12px;
                                    min-height: 12px;
                                    max-width: 60px;
                                    overflow: hidden;
                                    white-space: normal;
                                    word-break: break-word;
                                }
                            }
                            .gray {
                                color: colors.$neutral500;
                            }
                            .ant-collapse-content-box:has(.gantt-container) {
                                padding: 0;
                                .gantt-container > .ant-row {
                                    width: 100%;
                                }
                            }
                            .collapse-gantt-container {
                                height: 100%;
                                overflow-y: auto;

                                .gantt-container .gantt-flow-col {
                                    min-height: unset;

                                    #task-numbering {
                                        margin-bottom: -28px;
                                        padding-bottom: 28px;
                                    }
                                }
                            }
                        }
                    }
                    .ant-collapse-item:last-child {
                        border-bottom: none;
                    }
                }
            }
        }
        .controls {
            display: flex;
            flex-direction: row;
            gap: 24px;

            .ant-btn {
                background: colors.$accentW0;
                border: 2px solid colors.$neutral25;
                border-radius: 4px;
                color: colors.$neutral950;
                height: 48px;

                &:hover {
                    background: colors.$accentW10;
                    border: 2px solid colors.$accentW10;
                    color: colors.$accentW500;
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .sim-profile {
        .sim-card {
            padding: 16px;
        }
    }
}
