import { CRMAPIManager } from '@api/crmApiManager';
import { PermissionCategoryListResp } from '@api/responseModels/permissions/permissionCategoryListResponse';
import { RoleListResp } from '@api/responseModels/roles/roleListResponse';
import { Permissions } from '@classes/permissions';
import { Loader } from '@components/ui/loader/loader';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { Button, Checkbox, Col, Form, Input, message, Modal, Row } from 'antd';
import { observer } from 'mobx-react';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { TPermission, TPermissionCategory } from 'types/user/permissions';
import { TRole } from 'types/user/role';
import { rootStore } from '@store/instanse';
import { RoleResp } from '@api/responseModels/roles/roleResponse';
import _ from 'lodash';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { Common } from '@classes/common';

import './roles.scss';

type TState = {
    isLoading: boolean;
    lastRoleId: number;
    newRoleDialogOpen: boolean;
    newRoleName: string;
    permissionCategories: TPermissionCategory[];
    roles: TRole[];
    selectedRole: TRole | null;
    selectedRoleCurrentSet: TPermission['id'][];
    selectedRoleOriginalSet: TPermission['id'][];
};

const RolesPage = observer((): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        lastRoleId: 0,
        newRoleDialogOpen: false,
        newRoleName: '',
        permissionCategories: [],
        roles: [],
        selectedRole: null,
        selectedRoleCurrentSet: [],
        selectedRoleOriginalSet: [],
    });
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const anyChanges = !_.isEqual(state.selectedRoleCurrentSet, state.selectedRoleOriginalSet);

    function checkPermissions() {
        return (
            Permissions.checkPermission(Permissions.RoleList) &&
            Permissions.checkPermission(Permissions.PermissionCategoryList)
        );
    }

    async function loadRoles() {
        state.isLoading = true;
        try {
            const roles = await CRMAPIManager.getAll<RoleListResp>(async (api) => {
                return await api.getRoleList();
            });
            if (roles.errorMessages) throw roles.errorMessages;
            state.roles = roles.data.data;
            for (let i = 0; i < state.roles.length; i++) {
                if (state.roles[i].id > state.lastRoleId) {
                    state.lastRoleId = state.roles[i].id;
                }
            }
        } catch (errors) {
            messageApi.error('Ошибка при получении списка ролей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadPermissionCategories() {
        state.isLoading = true;
        try {
            const permCatList = await CRMAPIManager.request<PermissionCategoryListResp>(
                async (api) => {
                    return await api.getPermissionCategoryList({
                        category_name: null,
                        page: 1,
                        per_page: 100,
                        sort_by: null,
                        sort_direction: null,
                    });
                },
            );
            if (permCatList.errorMessages) throw permCatList.errorMessages;
            state.permissionCategories = permCatList.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка категорий прав');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadNecessary() {
        if (!checkPermissions()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        await loadRoles();
        await loadPermissionCategories();
    }

    useEffect(() => {
        loadNecessary();
    }, []);

    function validateNewRoleName(): boolean {
        if (state.newRoleName.trim().length == 0) {
            return false;
        }
        if (
            state.roles.find(
                (r) =>
                    r.name.toLocaleLowerCase() == `Roles.${state.newRoleName}`.toLocaleLowerCase(),
            )
        ) {
            return false;
        }
        if (!/^[a-zA-Z]+$/.test(state.newRoleName)) {
            return false;
        }
        if (
            state.newRoleName.toLocaleLowerCase().includes('roles') ||
            Common.isNullOrEmptyString(state.newRoleName)
        ) {
            return false;
        }
        return true;
    }

    async function createNewRole() {
        state.isLoading = true;
        try {
            const newRole = await CRMAPIManager.request<RoleResp>(async (api) => {
                return await api.createRole(
                    state.lastRoleId + 1,
                    state.newRoleName,
                    // Клиент без прохождений
                    [
                        2,
                        4,
                        7,
                        44,
                        45,
                        46,
                        47,
                        52,
                        53,
                        60,
                        61,
                        62,
                    ],
                );
            });
            if (newRole.errorMessages) throw newRole.errorMessages;
            state.newRoleDialogOpen = false;
            state.newRoleName = '';
            await loadRoles();
            messageApi.success(`Роль ${newRole.data.data.name} создана`);
        } catch (errors) {
            messageApi.error('Ошибка при создании роли');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function selectRole(r: TRole) {
        let rolePermSet = [];
        for (let i = 0; i < Object.keys(r.permissions).length; i++) {
            const permCatKey = Object.keys(r.permissions)[i];
            const permCat = r.permissions[permCatKey];
            for (let j = 0; j < permCat.entity_permissions.length; j++) {
                rolePermSet.push(permCat.entity_permissions[j].id);
            }
        }
        rolePermSet = rolePermSet.sort();
        state.selectedRoleCurrentSet = rolePermSet;
        state.selectedRoleOriginalSet = rolePermSet;
        state.selectedRole = r;
    }

    function setRoleEntityPermission(checked: boolean, id: TPermission['id']) {
        let tempSet = [...state.selectedRoleCurrentSet];
        if (checked) {
            tempSet.push(id);
        } else {
            tempSet = tempSet.filter((tsi) => tsi != id);
        }
        state.selectedRoleCurrentSet = tempSet.sort();
    }

    const allowRoleChange = useCallback(() => {
        const curUser = rootStore.currentUserStore.getUser;
        switch (curUser?.role) {
            case 'Roles.Admin': {
                return true;
            }
            case 'Roles.Manager': {
                return state.selectedRole?.name == 'Roles.Client';
            }
            default: {
                return false;
            }
        }
    }, [state.selectedRole, rootStore.currentUserStore.user]);

    async function syncRolePermissions() {
        state.isLoading = true;
        let returnValue = false;
        try {
            const role = await CRMAPIManager.request<RoleResp>(async (api) => {
                return await api.updateRole(
                    state.selectedRole.id,
                    state.selectedRole.name.split('.').find((part) => part != 'Roles'),
                    state.selectedRoleCurrentSet,
                );
            });
            if (role.errorMessages) throw role.errorMessages;
            await loadRoles();
            selectRole(role.data.data);
            message.success(`Роль ${role.data.data.name} обновлена`);
            returnValue = true;
        } catch (errors) {
            messageApi.error('Ошибка при обновлении прав роли');
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    function cancelChanges() {
        state.selectedRoleCurrentSet = state.selectedRoleOriginalSet;
    }

    function onNewRoleClick() {
        if (Permissions.checkPermission(Permissions.RoleCreate)) {
            form.setFieldValue('newRoleName', '');
            state.newRoleName = '';
            state.newRoleDialogOpen = true;
        }
    }

    async function roleNameValidator(value: string) {
        if (
            state.roles.find(
                (r) => r.name.toLocaleLowerCase() == `roles.${value}`.toLocaleLowerCase(),
            )
        ) {
            return Promise.reject(new Error('Данное имя роли уже используется'));
        }
        if ((value as string).toLocaleLowerCase().includes('roles')) {
            return Promise.reject(new Error('Имя роли не должно содержать префикс Roles'));
        }
        return Promise.resolve();
    }

    return (
        <UniLayout
            activeTab="roles"
            additionalClass="profile-min-width"
            tabSet="management"
        >
            <div className="roles-profile">
                {contextHolder}
                {state.isLoading && <Loader />}
                <PreventLeaving
                    anyChanges={anyChanges}
                    onSave={syncRolePermissions}
                />
                <Form
                    className="new-role-form"
                    form={form}
                    onFinish={createNewRole}
                    onValuesChange={(cv) => (state.newRoleName = cv.newRoleName)}
                >
                    <Modal
                        className="new-role-dialog"
                        closeIcon={<div className="close-icon" />}
                        cancelText="Отмена"
                        okButtonProps={{
                            disabled: !validateNewRoleName(),
                            htmlType: 'submit',
                        }}
                        okText="Создать"
                        onCancel={() => {
                            state.newRoleDialogOpen = false;
                        }}
                        onOk={createNewRole}
                        open={state.newRoleDialogOpen}
                    >
                        <Row>
                            <Col className="labeled-input">
                                <Row>
                                    <span className="p2">Введите название роли:</span>
                                </Row>
                                <Row className="p3">
                                    <Form.Item
                                        name="newRoleName"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Название обязательно',
                                            },
                                            {
                                                pattern: /^[a-zA-Z]+$/,
                                                max: 16,
                                                min: 1,
                                                message: 'От 1 до 16 символов латиницы',
                                            },
                                            {
                                                validator: (_, value) => {
                                                    return roleNameValidator(value);
                                                },
                                            },
                                        ]}
                                    >
                                        <Input
                                            allowClear
                                            maxLength={16}
                                            minLength={1}
                                            onChange={(e) => {
                                                state.newRoleName = e.target.value;
                                            }}
                                            prefix="Roles."
                                            showCount
                                            value={state.newRoleName}
                                        />
                                    </Form.Item>
                                </Row>
                            </Col>
                        </Row>
                        <Row>
                            <Col className="labeled-input">
                                <Row>
                                    <span className="p2">Занятые названия:</span>
                                </Row>
                                <Row className="wrap-split p3">
                                    {state.roles.map((r) => {
                                        return <Col key={r.id}>{r.name}</Col>;
                                    })}
                                </Row>
                            </Col>
                        </Row>
                    </Modal>
                </Form>
                <div className="roles-card">
                    <Row className="roles-info-row">
                        <Col>
                            <Row className="header-row">
                                <h4>
                                    Существующие роли (ваша:{' '}
                                    {rootStore.currentUserStore.getUser?.role}):
                                </h4>
                            </Row>
                            <Row className="body-row">
                                <Col>
                                    <Row className="role-card-list">
                                        {Permissions.checkPermission(Permissions.RoleCreate) &&
                                            !anyChanges && (
                                                <Col
                                                    className="role-add-card"
                                                    onClick={onNewRoleClick}
                                                >
                                                    <div className="add-icon" />
                                                </Col>
                                            )}
                                        {state.roles.map((r) => (
                                            <Col
                                                key={r.id}
                                                className={`role-card${
                                                    state.selectedRole?.id == r.id
                                                        ? ' selected'
                                                        : ''
                                                }`}
                                                onClick={() => {
                                                    selectRole(r);
                                                }}
                                            >
                                                <Row className="desc-m-strong">ID {r.id}</Row>
                                                <Row className="desc-m">{r.name}</Row>
                                            </Col>
                                        ))}
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    {state.selectedRole != null && (
                        <Row className="permissions-list-row">
                            <Col>
                                <Row className="header-row">
                                    <h4>Права роли ({state.selectedRole.name}):</h4>
                                </Row>
                                <Row className="body-row">
                                    <Col>
                                        <Row className="permissions-card-list">
                                            {/* RESERVE METHOD, TO BE DELETED */}
                                            {/* Object.keys(state.selectedRole.permissions).map(pckey => { */}
                                            {state.permissionCategories.map((pc) => {
                                                //const pc = state.selectedRole.permissions[pckey];
                                                return (
                                                    <div
                                                        key={pc.name}
                                                        className="permission-card"
                                                    >
                                                        <Row className="p3 permission-category-name">
                                                            {pc.name_locale}
                                                        </Row>
                                                        <Row className="p4 permission-category-body">
                                                            {pc.entity_permissions?.map((ep) => (
                                                                <Row
                                                                    key={ep.id}
                                                                    className="entity-permission"
                                                                >
                                                                    <Checkbox
                                                                        checked={state.selectedRoleCurrentSet.includes(
                                                                            ep.id,
                                                                        )}
                                                                        disabled={
                                                                            !allowRoleChange()
                                                                        }
                                                                        onChange={(e) =>
                                                                            setRoleEntityPermission(
                                                                                e.target.checked,
                                                                                ep.id,
                                                                            )
                                                                        }
                                                                    />
                                                                    {ep.name_locale}
                                                                </Row>
                                                            ))}
                                                            {/* pc.entity_permissions == null 
                                                                && state.selectedRole.permissions
                                                                && Object.keys(state.selectedRole.permissions).find(k => k == pc.name)
                                                                && state.selectedRole.permissions[pc.name].entity_permissions.map(ep => (
                                                                <Row 
                                                                    key={ep.id} 
                                                                    className="entity-permission"
                                                                >
                                                                    <Checkbox
                                                                        checked={state.selectedRoleCurrentSet.includes(ep.id)}
                                                                        disabled={!allowRoleChange()}
                                                                        onChange={(e) => setRoleEntityPermission(e.target.checked, ep.id)}
                                                                    />
                                                                    {ep.name_locale}
                                                                </Row>
                                                            )) */}
                                                        </Row>
                                                    </div>
                                                );
                                            })}
                                        </Row>
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    )}
                    <Row className="p2 controls-row">
                        <Col>
                            <Row>
                                {anyChanges && allowRoleChange() && (
                                    <Button
                                        onClick={() => {
                                            syncRolePermissions();
                                        }}
                                    >
                                        Сохранить
                                    </Button>
                                )}
                                {anyChanges && (
                                    <Button
                                        onClick={() => {
                                            cancelChanges();
                                        }}
                                    >
                                        Отменить
                                    </Button>
                                )}
                            </Row>
                        </Col>
                    </Row>
                </div>
            </div>
        </UniLayout>
    );
});

export default RolesPage;
