@use '/src/styles/colors';
@use '/src/styles/icons';

.new-role-dialog {
    .ant-modal-content {
        color: colors.$neutral950;
        padding: 40px 44px 45px 38px;

        .close-icon {
            @include icons.icon-plus('#272C35');
            background-repeat: no-repeat;
            background-size: contain;
            height: 30px;
            transform: rotate(45deg);
            width: 30px;
        }
        .ant-modal-body {
            display: flex;
            flex-direction: column;
            row-gap: 16px;
            padding-bottom: 40px;

            .labeled-input {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .wrap-split {
                    column-gap: 8px;
                    flex-wrap: wrap;
                    row-gap: 8px;
                }
            }
        }
        .ant-modal-footer {
            column-gap: 24px;
            row-gap: 8px;

            .ant-btn {
                background: colors.$accentW0;
                border: 2px solid colors.$neutral25;
                border-radius: 4px;
                color: colors.$neutral950;
                font-family: 'InterVariable';
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                height: 48px;
                line-height: 24px;

                &:not(:disabled):hover {
                    background: colors.$accentW10;
                    border: 2px solid colors.$accentW10;
                    color: colors.$accentW500;
                }
                &:disabled {
                    background: colors.$neutral25;
                    color: colors.$neutral300;
                }
            }
        }
    }
}
.roles-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .roles-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .roles-info-row,
        .permissions-list-row {
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .header-row {
                    align-items: center;
                    column-gap: 20px;
                    justify-content: space-between;
                    width: 100%;

                    h4 {
                        color: colors.$neutral950;
                        margin-top: 0;
                    }
                }
            }
        }
        .roles-info-row {
            > .ant-col {
                .body-row {
                    > .ant-col {
                        display: flex;
                        flex-direction: column;
                        width: 100%;

                        .role-card-list {
                            background: colors.$neutral25;
                            border-bottom: 2px solid colors.$neutral25;
                            border-radius: 4px;
                            flex-wrap: wrap;
                            gap: 8px;
                            min-height: 48px;
                            padding: 16px;

                            .role-add-card,
                            .role-card {
                                background: colors.$accentW0;
                                border: 0.5px solid colors.$neutral100;
                                border-radius: 1px;
                                cursor: pointer;
                                display: flex;
                                flex-direction: column;
                                flex-wrap: nowrap;
                                justify-content: space-between;
                                height: 40px;
                                padding: 3px;
                                width: 100px;

                                &:hover {
                                    border: 0.5px solid colors.$accentW500;
                                }
                            }
                            .role-add-card {
                                align-items: center;
                                justify-content: center;

                                .add-icon {
                                    @include icons.icon-plus('#272C35');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    height: 30px;
                                    width: 30px;
                                }
                                &:hover .add-icon {
                                    @include icons.icon-plus('#35ABFF');
                                }
                            }
                            .role-card.selected {
                                border: 0.5px solid colors.$accentW700;
                            }
                        }
                    }
                }
            }
        }
        .permissions-list-row {
            > .ant-col {
                .body-row {
                    > .ant-col {
                        display: flex;
                        flex-direction: column;
                        width: 100%;

                        .permissions-card-list {
                            background: colors.$neutral25;
                            border-bottom: 2px solid colors.$neutral25;
                            border-radius: 4px;
                            flex-wrap: wrap;
                            gap: 8px;
                            max-height: 600px;
                            min-height: 48px;
                            overflow-y: auto;
                            padding: 16px;

                            .permission-card {
                                background: colors.$accentW0;
                                border: 2px solid colors.$neutral100;
                                border-radius: 8px;
                                display: flex;
                                flex-direction: column;
                                flex-wrap: nowrap;
                                padding: 3px;
                                row-gap: 12px;
                                width: 240px;

                                .permission-category-name {
                                    border-bottom: 1px solid colors.$neutral50;
                                    padding: 8px;
                                }
                                .permission-category-body {
                                    display: flex;
                                    flex-direction: column;
                                    padding: 8px;
                                    row-gap: 8px;
                                    width: 100%;

                                    .entity-permission {
                                        align-items: center;
                                        column-gap: 8px;
                                        flex-wrap: nowrap;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .controls-row {
            column-gap: 24px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;

                > .ant-row {
                    column-gap: 24px;
                    row-gap: 8px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .roles-profile {
        .roles-card {
            padding: 16px;

            .permissions-list-row {
                > .ant-col {
                    .body-row {
                        > .ant-col {
                            .permissions-card-list {
                                max-height: 400px;
                            }
                        }
                    }
                }
            }
        }
    }
}
