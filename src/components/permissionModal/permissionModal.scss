@use '/src/styles/colors';

.permission-modal-wrapper {
    .modal-header h6 {
        margin: 0;
    }
    .ant-modal-body {
        > .ant-col {
            display: flex;
            flex-direction: column;
            width: 100%;

            .permissions-card-list {
                background: colors.$neutral25;
                border-bottom: 2px solid colors.$neutral25;
                border-radius: 4px;
                flex-wrap: wrap;
                gap: 8px;
                max-height: 600px;
                min-height: 48px;
                overflow-y: auto;
                padding: 16px;

                .permission-card {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral100;
                    border-radius: 8px;
                    display: flex;
                    flex-direction: column;
                    flex-wrap: nowrap;
                    padding: 3px;
                    row-gap: 12px;
                    width: 240px;

                    .permission-category-name {
                        border-bottom: 1px solid colors.$neutral50;
                        padding: 8px;
                    }
                    .permission-category-body {
                        display: flex;
                        flex-direction: column;
                        padding: 8px;
                        row-gap: 8px;
                        width: 100%;

                        .entity-permission {
                            align-items: center;
                            column-gap: 8px;
                            flex-wrap: nowrap;

                            .ant-checkbox-checked .ant-checkbox-inner::after {
                                border-color: colors.$accentC700;
                            }
                        }
                    }
                }
            }
        }
    }
}
@media only screen and (min-width: 599px) {
    .permission-modal-wrapper .ant-modal {
        min-width: 583px;
    }
}
@media only screen and (min-width: 847px) {
    .permission-modal-wrapper .ant-modal {
        min-width: 831px;
    }
}
@media only screen and (min-width: 1097px) {
    .permission-modal-wrapper .ant-modal {
        min-width: 1079px;
    }
}
@media only screen and (max-width: 598px) and (orientation: portrait) {
    .permission-modal-wrapper {
        .ant-modal {
            top: 8px;
        }
        .ant-modal-body {
            > .ant-col {
                .permissions-card-list {
                    max-height: calc(100vh - 108px);

                    .permission-card {
                        width: 100%;
                    }
                }
            }
        }
    }
}
