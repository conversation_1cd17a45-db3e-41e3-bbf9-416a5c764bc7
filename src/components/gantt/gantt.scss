@use '/src/styles/colors';

#uni-layout .main-container:has(.gantt-container),
#ingame-container .main-container:has(.gantt-container) {
    overflow-y: hidden;

    .child-container {
        overflow-y: auto;
    }
}
.gantt-container {
    > .ant-row {
        height: 100%;
    }
    .gantt-table-col {
        background: colors.$accentW0;
        border: 1px solid colors.$neutral25;
        height: 100%;
        min-height: 822px;
        overflow-y: scroll;
        width: 30%;
        z-index: 100;

        .gantt-table {
            .ant-table {
                border-radius: 0;

                .ant-table-container,
                .ant-table-thead .ant-table-cell {
                    border-start-start-radius: 0;
                    border-start-end-radius: 0;
                }
                .ant-table-content table {
                    border-radius: 0;
                }
                .ant-table-tbody .ant-table-cell {
                    padding: 4px;
                }
            }
        }
        .gantt-table .ant-input-number {
            width: 100%;
        }
    }
    .gantt-flow-col {
        height: 100%;
        min-height: 822px;
        width: 70%;

        &.full-width {
            width: 100%;
        }
        .no-sim-or-tasks {
            align-items: center;
            display: flex;
            flex-direction: row;
            height: 100%;
            justify-content: center;
            width: 100%;
        }
        #task-numbering {
            background: colors.$neutral10;
            border-right: 1px solid colors.$neutral25;
            box-shadow: 4px 0px 4px 0px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            height: 100%;
            padding-left: 12px;
            position: relative;
            z-index: 101;

            > .ant-row {
                align-items: center;
                justify-content: flex-start;

                .ant-btn-link {
                    height: auto;
                    padding: 0;
                }
            }
        }
        #week-numbering {
            background-color: colors.$neutral0;
            border-top: 1px solid colors.$neutral25;
            box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: row;
            height: 28px;
            position: relative;
            transform-origin: 0 0;
            z-index: 100;

            > .ant-col {
                align-items: center;
                display: flex;
                flex-direction: column;

                > .ant-row {
                    justify-content: space-between;
                    width: 100%;

                    .left-thingie,
                    .right-thingie {
                        border-bottom: 1px solid colors.$neutral200;
                        height: 18px;
                        margin-bottom: 10px;
                        width: 28px;
                    }
                    .left-thingie {
                        border-left: 1px solid colors.$neutral200;
                        border-bottom-left-radius: 8px;
                    }
                    .right-thingie {
                        border-right: 1px solid colors.$neutral200;
                        border-bottom-right-radius: 8px;
                    }
                }
            }
        }
        #today-marker {
            background-color: colors.$successW300;
            height: calc(100% - 28px);
            opacity: 0.4;
            pointer-events: none;
            position: relative;
            transform-origin: 0 0;
            z-index: 4;

            .today-text {
                bottom: 16px;
                color: colors.$successW800;
                left: 36px;
                position: absolute;
                text-shadow: 1px 1px 1px colors.$successC925;
            }
        }
    }
}
@media screen and (max-width: 1025px) {
    .child-container:has(.gantt-container) {
        height: calc(100vh - 111px);

        .gantt-container {
            height: calc(100vh - 111px);

            .gantt-table-col {
                height: calc(100vh - 111px);
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .child-container:has(.gantt-container) {
        height: calc(100vh - 64px);

        .gantt-container {
            height: calc(100vh - 64px);

            .gantt-table-col {
                height: calc(100vh - 64px);
                max-height: calc(100vh - 64px);
            }
            .gantt-flow-col {
                height: calc(100vh - 64px);
                max-height: calc(100vh - 64px);
            }
        }
    }
}

.react-flow.gantt-flow {
    /* Custom Variables */
    --xy-theme-selected: #f57dbd;
    --xy-theme-hover: #b5c5b5;
    --xy-edge-stroke: #7c869c;
    --xy-edge-stroke-selected: #272c35;
    --xy-theme-edge-hover: #69758e;
    --xy-edge-stroke-width-default: 2px;
    --xy-theme-color-focus: #e8e8e8;

    /* Built-in Variables see https://reactflow.dev/learn/customization/theming */
    --xy-node-border-default: 1px solid #ededed;

    --xy-node-boxshadow-default:
        0px 3.54px 4.55px 0px #00000005, 0px 3.54px 4.55px 0px #0000000d,
        0px 0.51px 1.01px 0px #0000001a;

    --xy-node-border-radius-default: 14px;

    --xy-handle-background-color-default: colors.$accentW0;
    --xy-handle-border-color-default: #aaaaaa;

    --xy-edge-label-color-default: #505050;

    /* Customizing Default Theming */
    .react-flow__node-ganttNode {
        align-items: center;
        //background-color: colors.$accentW200;
        border-radius: var(--xy-node-border-radius-default);
        box-shadow: var(--xy-node-boxshadow-default);
        color: var(--xy-node-color, var(--xy-node-color-default));
        cursor: pointer;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        height: 28px;
        justify-content: center;
        text-align: center;

        .react-flow__handle {
            border-radius: 0;
            cursor: grab;
            max-height: 0;
            max-width: 0;
        }
        .gantt-node-wrapper {
            height: 100%;
            width: 100%;

            .gantt-node-inner {
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                height: 28px;
                width: 100%;

                .main-col {
                    border-radius: var(--xy-node-border-radius-default);
                    padding: 2px 2px;

                    .ant-row {
                        align-items: center;
                    }
                    > .ant-row {
                        border-radius: var(--xy-node-border-radius-default);
                        justify-content: space-between;
                    }
                    .gantt-node-workers > .ant-row > .ant-col > .ant-row,
                    .gantt-node-progress > .ant-row {
                        column-gap: 2px;
                        > .ant-row {
                            column-gap: 2px;
                        }
                    }
                    .worker-counter,
                    .worker-mini {
                        background-color: colors.$accentW0;
                        border: none;
                        border-radius: 50%;
                        color: colors.$neutral950;
                        display: flex;
                        flex-direction: column;
                        height: 24px;
                        justify-content: center;
                        width: 24px;
                    }
                    .add-worker-btn {
                        background-color: colors.$accentW0;
                        border: none;
                        border-radius: 50%;
                        height: 24px;
                        width: 24px;
                    }
                }
                .slack-col {
                    background: rgba(0, 0, 0, 0.07);
                    border: 2px solid colors.$accentW300;
                    border-left: none;
                    border-top-right-radius: var(--xy-node-border-radius-default);
                    border-bottom-right-radius: var(--xy-node-border-radius-default);
                }
            }
        }
    }
    .react-flow__node-ganttPlanNode {
        align-items: center;
        background-color: colors.$neutral700;
        border-radius: none;
        box-shadow: none;
        cursor: default;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        height: 8px;
        justify-content: center;
        opacity: 0.3;
    }

    .react-flow__edge,
    .react-flow__edge:hover,
    .react-flow__edge.selectable:hover {
        cursor: grab;
    }

    .react-flow__handle {
        background-color: var(--xy-handle-background-color-default);
    }

    .react-flow__handle.connectionindicator:hover {
        pointer-events: all;
        border-color: var(--xy-theme-edge-hover);
        background-color: white;
    }

    .react-flow__handle.connectionindicator:focus,
    .react-flow__handle.connectingfrom,
    .react-flow__handle.connectingto {
        border-color: var(--xy-theme-edge-hover);
    }
}
