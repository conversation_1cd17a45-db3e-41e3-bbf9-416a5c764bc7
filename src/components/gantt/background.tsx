import { memo } from 'react';
import { useViewport } from '@xyflow/react';

type TProps = {
    baseColor: string;
    daysInAWeek: number;
    splitColor: string;
    weeksInSimulation: number;
    widthPerDay: number;
};

const CustomBackground = memo(
    ({ baseColor, daysInAWeek, splitColor, weeksInSimulation, widthPerDay }: TProps) => {
        const { x, y, zoom } = useViewport();

        return (
            <svg
                className="custom-background"
                height="100%"
                style={{
                    left: `${widthPerDay * -2}px`,
                    position: 'absolute',
                    top: 0,
                    zIndex: -1,
                }}
                width={`${weeksInSimulation * daysInAWeek * widthPerDay + daysInAWeek * widthPerDay}px`}
            >
                <defs>
                    <pattern
                        id="rowPattern"
                        height="100%"
                        patternUnits="userSpaceOnUse"
                        width={widthPerDay * daysInAWeek * 2}
                    >
                        <rect
                            fill={baseColor}
                            height="100%"
                            width={widthPerDay * daysInAWeek}
                        />
                        <rect
                            fill={splitColor}
                            height="100%"
                            width={widthPerDay * daysInAWeek}
                        />
                    </pattern>
                </defs>
                <rect
                    fill="url(#rowPattern)"
                    height="100%"
                    transform={`translate(${x}, ${y}) scale(${zoom})`}
                    width="100%"
                />
            </svg>
        );
    },
);

export default CustomBackground;
