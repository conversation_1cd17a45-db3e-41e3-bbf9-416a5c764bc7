import { CRMAPIManager } from '@api/crmApiManager';
import { SettingsManager } from '@classes/settingsManager';
import { Loader } from '@components/ui/loader/loader';
import { rootStore } from '@store/instanse';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const Logout = (): JSX.Element => {
    const navigate = useNavigate();

    async function logout() {
        try {
            const result = await CRMAPIManager.request<any>(async (api) => {
                return await api.logout();
            });
        } catch (err) {
            console.log(err);
        }
        SettingsManager.clearConnectionCredentials();
        rootStore.currentUserStore.setUser({});
        rootStore.socketStore.clearStore();
        navigate('/login');
    }

    useEffect(() => {
        logout();
    }, []);

    return <Loader isFullSize={true} />;
};

export default Logout;
