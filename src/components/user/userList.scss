@use '/src/styles/colors';
@use '/src/styles/icons';

.chat-name-modal {
    .modal-header h6 {
        margin: 0;
    }
    .ant-modal-body {
        > .ant-col {
            display: flex;
            flex-direction: column;
            row-gap: 16px;
            width: 100%;
        }
    }
    .ant-modal-footer {
        .ant-btn {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral25;
            border-radius: 4px;
            color: colors.$neutral950;
            height: 48px;

            &:disabled {
                background: colors.$neutral25;
                color: colors.$neutral300;
            }
            &:not(:disabled):hover {
                background: colors.$accentW10;
                border: 2px solid colors.$accentW10;
                color: colors.$accentW500;
            }
        }
    }
}
.user-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    min-width: 556px;
    width: 100%;

    .radio-group {
        column-gap: 12px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 24px 24px 12px 40px;
        row-gap: 8px;

        .ant-radio-group {
            white-space: nowrap;
        }
    }
    .user-list {
        display: flex;
        flex-direction: column;
        min-width: 556px;
        padding: 20px 24px 140px 38px;
        row-gap: 16px;
        width: 100%;

        .ant-list-empty-text {
            background: colors.$accentW0;
            color: colors.$neutral950;

            .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .ant-row {
                justify-content: center;
            }
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            min-width: 480px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .user-list-add,
        .user-list-card {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            height: 230px;
            width: 480px;
        }
        .user-list-add {
            align-items: center;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .add-icon {
                @include icons.icon-plus('#272C35');
                background-repeat: no-repeat;
                background-size: contain;
                height: 32px;
                width: 32px;
            }
            &:hover {
                border: 2px solid colors.$accentW500;
                .add-icon {
                    @include icons.icon-plus('#35ABFF');
                }
            }
        }
        .user-list-card {
            display: flex;
            flex-direction: column;
            padding: 20px;
            row-gap: 8px;

            h3 {
                margin: 0;
            }
            > .ant-row:first-child {
                column-gap: 8px;
                width: 100%;

                > .ant-col:last-child {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    height: 100%;
                    width: calc(100% - 148px);

                    .user-name {
                        width: 100%;
                    }
                    .cut-name {
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        min-height: 24px;
                        max-height: 24px;
                        max-width: 250px;
                        overflow: hidden;
                        white-space: normal;
                        word-break: break-all;
                    }
                    .body-row {
                        gap: 8px 16px;
                    }
                    .user-tag {
                        border: 2px solid colors.$successW50;
                        border-radius: 4px;
                        color: colors.$neutral700;
                        display: flex;
                        height: 20px;
                        padding: 0 4px;
                    }
                }
            }
            .event-card-controls {
                column-gap: 16px;
                padding-bottom: 0;
                row-gap: 8px;

                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;

                    &:hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
            .handsome-client {
                background: url('/src/assets/handsome-client.png');
            }
            .handsome-staff {
                background: url('/src/assets/handsome-staff.png');
            }
            .handsome-client,
            .handsome-staff {
                background-color: colors.$accentW0;
                background-repeat: no-repeat;
                background-size: contain;
                border-radius: 12px;
                height: 140px;
                width: 140px;
            }
        }
        .pagination-row {
            column-gap: 16px;
            justify-content: center;
        }
    }
    .user-table {
        padding: 20px 40px 20px 38px;
        width: 100%;

        > .ant-col {
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
            min-width: 556px;
            row-gap: 16px;
        }
        .header-row {
            align-items: center;
            column-gap: 20px;
            justify-content: space-between;
            width: 100%;

            h4 {
                color: colors.$neutral950;
                margin-top: 0;
            }
        }
        .body-row {
            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .table-actions {
                    align-items: center;
                    border: 1px solid colors.$neutral10;
                    border-radius: 8px;
                    column-gap: 8px;
                    min-height: 58px;
                    padding: 12px;
                    row-gap: 8px;
                    width: 100%;
                }
                .table-container {
                    width: 100%;

                    .ant-table-wrapper {
                        width: 100%;

                        .ant-table-placeholder .ant-table-cell {
                            background: colors.$accentW0;
                            color: colors.$neutral950;

                            .ant-col {
                                display: flex;
                                flex-direction: column;
                                row-gap: 8px;
                            }
                            .ant-row {
                                justify-content: center;
                            }
                        }
                        .ant-table-tbody .ant-table-row {
                            .table-id {
                                .open-btn {
                                    height: 20px;
                                    margin: 0 4px;
                                    width: 20px;

                                    svg {
                                        color: colors.$accentW300;
                                    }
                                    &:disabled {
                                        svg {
                                            color: colors.$neutral300;
                                        }
                                    }
                                }
                            }
                            .table-user {
                                align-items: center;
                                column-gap: 12px;
                                flex-wrap: nowrap;
                                width: fit-content;

                                &:has(.creator-btn) {
                                    cursor: pointer;
                                }
                                .user-avatar {
                                    align-items: center;
                                    background-color: colors.$accentC50;
                                    border-radius: 50%;
                                    color: colors.$neutral900;
                                    display: flex;
                                    flex-direction: column;
                                    height: 32px;
                                    justify-content: center;
                                    min-width: 32px;
                                    max-width: 32px;
                                }
                                .user-name {
                                    .ant-btn-link {
                                        padding: 0;
                                    }
                                }
                            }
                            .desc-l {
                                color: colors.$neutral900;
                            }
                            .table-status {
                                align-items: center;
                                column-gap: 8px;
                                row-gap: 4px;
                                width: 100%;
                            }
                            .lighter-tone {
                                color: colors.$neutral700;
                            }
                            .not-selected {
                                color: colors.$errorC200;
                            }
                            .filter-row {
                                align-items: center;
                                column-gap: 8px;
                                row-gap: 4px;
                                width: 100%;
                            }
                        }
                    }
                }
                .pagination-row {
                    column-gap: 16px;
                    justify-content: center;
                }
            }
        }
        .controls-row {
            column-gap: 24px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;

                > .ant-row {
                    column-gap: 24px;
                    row-gap: 8px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .user-list-container {
        min-height: calc(100vh - 80px);
        min-width: 334px;

        .radio-group {
            flex-wrap: wrap;
            padding: 8px 0 4px 16px;
            row-gap: 8px;

            .ant-radio-group {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                row-gap: 8px;

                .ant-radio-button-wrapper {
                    padding-inline: 8px;
                }
            }
        }
        .user-list {
            min-height: calc(100vh - 80px);
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            .ant-list {
                min-width: 318px;

                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 0;
                    min-width: 318px;
                }
                .user-list-add,
                .user-list-card {
                    width: 318px;
                }
                .user-list-add {
                    height: 80px;
                }
                .user-list-card {
                    height: 230px;
                    justify-content: space-between;

                    .user-image {
                        height: 100px;
                        width: 100px;
                    }
                    > .ant-col > .ant-row {
                        row-gap: 8px;
                    }
                    .event-card-controls {
                        margin-left: 0;

                        > .ant-col > .ant-row {
                            row-gap: 8px;
                        }
                    }
                }
            }
        }
        .user-table {
            min-height: calc(100vh - 80px);
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            > .ant-col {
                max-width: 314px;
                min-width: 314px;
            }
        }
    }
}
