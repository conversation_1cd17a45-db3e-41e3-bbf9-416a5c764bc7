import { CRMAPIManager } from '@api/crmApiManager';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { Loader } from '@components/ui/loader/loader';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { Button, Col, Form, Input, InputNumber, message, Popconfirm, Row, Select } from 'antd';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimEvent } from 'types/simulation/simulationEvent';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';
import { Common } from '@classes/common';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { DefaultTimeSettings } from '@store/ingame/data';
import { Permissions } from '@classes/permissions';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import Colors from '@classes/colors';

const { TextArea } = Input;

import './constructorEventProfile.scss';

type TState = {
    anyChanges: boolean;
    event: TSimEvent;
    isLoading: boolean;
    lastTaskEndDay: number;
    simulation: TSimulation;
    skipPreventLeaving: boolean;
    tasks: TSimTask[];
    workers: TSimWorker[];
    useCase: 'new' | 'edit';
};

type EffectParam =
    | 'motivation'
    | 'team-communication'
    | 'relation-leader'
    | 'relation-organization'
    | 'client-communication'
    | 'team-spirit'
    | 'cost'
    | 'work-hour';

const ConstructorEventProfile = (): JSX.Element => {
    const state = useReactive<TState>({
        anyChanges: false,
        event: null,
        isLoading: false,
        lastTaskEndDay: DefaultTimeSettings.daysInAWeek * 12,
        simulation: null,
        skipPreventLeaving: false,
        tasks: [],
        workers: [],
        useCase: 'new',
    });
    const [messageApi, contextHolder] = message.useMessage();
    const { simId, eventId } = useParams();
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const effectParamOptions: Array<{ label: string; value: EffectParam }> = [
        { label: 'Мотивация', value: 'motivation' },
        { label: 'Отношения с командой', value: 'team-communication' },
        { label: 'Отношения с руководством', value: 'relation-leader' },
        { label: 'Отношения с организацией', value: 'relation-organization' },
        { label: 'Отношения с клиентом', value: 'client-communication' },
        { label: 'Командный дух', value: 'team-spirit' },
        { label: 'Затраты', value: 'cost' },
        { label: 'Трудочасы', value: 'work-hour' },
    ];
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.SimulationEventGet) &&
            Permissions.checkPermission(Permissions.SimulationEventCreate) &&
            Permissions.checkPermission(Permissions.SimulationEventDelete) &&
            Permissions.checkPermission(Permissions.SimulationEventRestore) &&
            Permissions.checkPermission(Permissions.SimulationEventUpdate) &&
            Permissions.checkPermission(Permissions.SimulationGet) &&
            Permissions.checkPermission(Permissions.SimulationWorkerList) &&
            Permissions.checkPermission(Permissions.SimulationTaskList)
        );
    }

    function updateEventFromForm() {
        //state.event = _.merge({ ...state.event }, e);
        state.event = { ...form.getFieldsValue(true) };
        state.anyChanges = true;
    }

    async function loadEvent() {
        state.isLoading = true;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return await api.getSimEvent(+simId, eventId);
            });
            if (event.errorMessages) throw event.errorMessages;
            state.event = {
                ...event.data.data,
                options:
                    event.data.data.options == null
                        ? []
                        : (event.data.data.options.map((eo) => {
                              return {
                                  ...eo,
                                  reactions: eo.reactions ?? [],
                                  effects: eo.effects ?? [],
                              };
                          }) ?? []),
            };
            state.anyChanges = false;
            form.setFieldsValue(state.event);
        } catch (err) {
            if (err?.message?.includes('404')) {
                navigate(`/constructor/${simId}/events`);
            }
            messageApi.error('Ошибка при получении события');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadParentSim() {
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при получении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function setEventTemplate() {
        state.event = {
            uid: 'new',
            simulation_id: +simId,
            id: null,
            sender_id: 'sys',
            title: '',
            description: '',
            options: [],
            trigger_type: 'day',
            trigger_arg: 1,
            deleted_at: null,
            created_at: Common.dateNowString(),
            updated_at: null,
        };
        form.setFieldsValue(state.event);
    }

    async function loadTaskList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.getAll<SimTaskListResp>(async (api, page, per_page) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: page,
                    per_page: per_page,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = tl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка задач');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadWorkerList() {
        state.isLoading = true;
        try {
            const wl = await CRMAPIManager.getAll<SimWorkerListResp>(
                async (api, page, per_page) => {
                    return await api.getSimWorkerList({
                        simulation_id: +simId,
                        page: page,
                        per_page: per_page,
                        sort_by: 'created_at',
                        sort_direction: 'desc',
                        filters: {
                            deleted: 'null',
                        },
                    });
                },
            );
            if (wl.errorMessages) throw wl.errorMessages;
            state.workers = wl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка ПШЕ');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function calcMaxDay() {
        if (state.tasks.length != 0) {
            if (state.simulation?.first_task == null || state.simulation?.last_task == null) {
                state.lastTaskEndDay = DefaultTimeSettings.daysInAWeek * 12;
            } else {
                state.lastTaskEndDay = state.simulation?.weeks * DefaultTimeSettings.daysInAWeek;
            }
        }
    }

    async function initEvent() {
        state.skipPreventLeaving = false;
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        await loadParentSim();
        if (state.simulation == null) return;
        if (state.useCase == 'edit') {
            await loadEvent();
        } else {
            setEventTemplate();
        }
        await loadTaskList();
        await loadWorkerList();
        calcMaxDay();
    }

    useEffect(() => {
        if (eventId != undefined) {
            state.useCase = 'edit';
        }
        initEvent();
    }, [eventId]);

    function validate(): boolean {
        if (state.event.title.trim().length == 0) {
            messageApi.warning('Введите название.');
            return false;
        }
        if (state.event.description.trim().length == 0) {
            messageApi.warning('Введите описание.');
            return false;
        }
        if (state.event.trigger_type == 'day' && state.lastTaskEndDay != -1) {
            if (state.event.trigger_arg > state.lastTaskEndDay) {
                messageApi.warning('Выберите день раньше, чем конец последней задачи');
                return false;
            }
        }
        if (state.event.options.length == 0) {
            messageApi.warning('Добавьте хотя бы один вариант ответа.');
            return false;
        }
        for (let i = 0; i < state.event.options.length; i++) {
            const option = state.event.options[i];
            const reactionSenders = [];
            for (let j = 0; j < option.reactions.length; j++) {
                const reaction = option.reactions[j];
                if (reactionSenders.find((rsi) => rsi == reaction.reaction_sender_id)) {
                    messageApi.warning('Не должно быть реакций с одним отправителем');
                    return false;
                }
                reactionSenders.push(reaction.reaction_sender_id);
            }
            const effectParams = [];
            for (let j = 0; j < option.effects.length; j++) {
                const effect = option.effects[j];
                if (effectParams.find((epi) => epi == effect.param)) {
                    messageApi.warning('Не должно быть последствий с одним параметром');
                    return false;
                }
                effectParams.push(effect.param);
            }
        }
        return true;
    }

    async function saveEvent() {
        if (!validate()) return false;
        state.isLoading = true;
        let returnValue = false;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                if (state.useCase == 'new') {
                    state.skipPreventLeaving = true;
                    return api.createSimEvent(state.event);
                } else {
                    return api.updateSimEvent(state.event);
                }
            });
            if (event.errorMessages) throw event.errorMessages;
            if (state.useCase == 'new') {
                navigate(`/constructor/${simId}/events/${event.data.data.uid}`);
                message.success('Событие создано');
            } else {
                state.event = event.data.data;
                form.setFieldsValue(state.event);
                state.anyChanges = false;
                message.success('Изменения сохранены');
            }
            returnValue = true;
        } catch (errors) {
            state.skipPreventLeaving = false;
            messageApi.error(
                `Ошибка при ${state.useCase == 'new' ? 'создании' : 'сохранении'} события`,
            );
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    async function deleteEvent() {
        state.isLoading = true;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return api.removeSimEvent(+simId, state.event?.uid);
            });
            if (event.errorMessages) throw event.errorMessages;
            state.event = event.data.data;
            form.setFieldsValue(state.event);
            state.anyChanges = false;
            messageApi.success('Событие было удалено');
        } catch (errors) {
            messageApi.error('Ошибка при удалении события');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreEvent() {
        state.isLoading = true;
        try {
            const event = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return api.restoreSimEvent(+simId, state.event?.uid);
            });
            if (event.errorMessages) throw event.errorMessages;
            state.event = event.data.data;
            form.setFieldsValue(state.event);
            state.anyChanges = false;
            messageApi.success('Событие было восстановлено');
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении события');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function cancelCreation() {
        navigate(`/constructor/${simId}/events`);
    }

    function makeOptionTemplate() {
        return {
            uid: `${state.event.uid}-${state.event.options.length + 1}`,
            simulation_id: +simId,
            simulation_event_uid: state.event.uid,
            id: state.event.options.length + 1,
            text: '',
            reply: '',
            reactions: [],
            effects: [],
        };
    }

    function makeOptionReactionTemplate(opt_index: number) {
        const parentOption = state.event?.options[opt_index];
        return {
            reaction_sender_id: state.workers.find(
                (wi) =>
                    parentOption.reactions.find((ri) => ri.reaction_sender_id == wi.uid) ==
                    undefined,
            )?.uid,
            reaction_text: '',
        };
    }

    function makeOptionEffectTemplate(opt_index: number) {
        const parentOption = state.event?.options[opt_index];
        return {
            param: effectParamOptions.find(
                (epi) => parentOption.effects.find((ei) => ei.param == epi.value) == undefined,
            )?.value,
            modifier: 0,
        };
    }

    return (
        <UniLayout
            activeTab="events"
            additionalClass="profile-min-width"
            tabSet="constructor"
            showSearch={false}
        >
            <div className="event-profile-container">
                {state.isLoading && <Loader />}
                {contextHolder}
                <PreventLeaving
                    anyChanges={state.anyChanges && !state.skipPreventLeaving}
                    onSave={saveEvent}
                />
                <div className="event-profile">
                    <Form
                        className="event-form"
                        form={form}
                        onFinish={saveEvent}
                        onValuesChange={() => updateEventFromForm()}
                    >
                        <Col flex={1}>
                            <Row className="top-row">
                                <Col>
                                    <Row>
                                        <h3>ID {state.event?.id ?? 'Новое событие'}</h3>
                                        {state.useCase != 'new' && (
                                            <CopyButton
                                                textToCopy={`Событие #${state.event?.id} симуляции #${simId}`}
                                                textToShow="ID события скопирован"
                                                size={36}
                                            />
                                        )}
                                    </Row>
                                </Col>
                                {state.event?.deleted_at != null && (
                                    <Col>
                                        <FilterButton
                                            hex={Colors.Error.warm[500]}
                                            text="Удалён"
                                        />
                                    </Col>
                                )}
                            </Row>
                            <Row>
                                <Col className="labeled-input">
                                    <span className="p2">Название:</span>
                                    <Form.Item
                                        name="title"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Введите название события',
                                            },
                                            {
                                                max: 50,
                                                message: 'Не больше 50 символов',
                                            },
                                        ]}
                                    >
                                        <Input
                                            allowClear
                                            disabled={disableEdit}
                                            maxLength={50}
                                            placeholder="Введите название события"
                                            showCount
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Row>
                                <Col className="labeled-input">
                                    <span className="p2">Сообщение:</span>
                                    <Form.Item
                                        name="description"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Введите сообщение события',
                                            },
                                            {
                                                max: 300,
                                                message: 'Не больше 300 символов',
                                            },
                                        ]}
                                    >
                                        <TextArea
                                            allowClear
                                            disabled={disableEdit}
                                            maxLength={300}
                                            placeholder="Введите сообщение события"
                                            rows={3}
                                            showCount
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Row>
                                <Col className="labeled-input">
                                    <span className="p2">Отправитель:</span>
                                    <Form.Item name="sender_id">
                                        <Select
                                            disabled={
                                                state.event?.sender_id == undefined || disableEdit
                                            }
                                            options={[
                                                { label: 'Система', value: 'sys' },
                                                { label: 'Клиент', value: 'client' },
                                                { label: 'Команда', value: 'team' },
                                                ...state.workers?.map((wi) => {
                                                    return {
                                                        label: `${wi.id}: ${wi.name}`,
                                                        value: wi.id,
                                                    };
                                                }),
                                            ]}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Row className="split-row">
                                <Col className="labeled-input">
                                    <span className="p2">Тип триггера:</span>
                                    <Form.Item name="trigger_type">
                                        <Select
                                            disabled={disableEdit}
                                            options={[
                                                { label: 'День', value: 'day' },
                                                { label: 'Узел', value: 'task' },
                                            ]}
                                        />
                                    </Form.Item>
                                </Col>
                                {state.event?.trigger_type == 'day' && (
                                    <Col className="labeled-input">
                                        <span className="p2">
                                            Конкретный день
                                            {state.lastTaskEndDay != -1 &&
                                                ` (макс. ${state.lastTaskEndDay})`}
                                            :
                                        </span>
                                        <Form.Item
                                            name="trigger_arg"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: 'Введите день',
                                                },
                                                {
                                                    type: 'integer',
                                                    max: state.lastTaskEndDay,
                                                    min: 1,
                                                    message: `От 1 до ${state.lastTaskEndDay}`,
                                                },
                                            ]}
                                        >
                                            <InputNumber
                                                disabled={disableEdit}
                                                max={state.lastTaskEndDay}
                                                min={1}
                                                step={1}
                                            />
                                        </Form.Item>
                                    </Col>
                                )}
                                {state.event?.trigger_type == 'task' && (
                                    <Col className="labeled-input">
                                        <span className="p2">Конкретный узел:</span>
                                        <Form.Item name="trigger_arg">
                                            <Select
                                                disabled={disableEdit}
                                                options={state.tasks.map((t) => {
                                                    return {
                                                        label: `${t.id}: ${t.name}`,
                                                        value: t.id,
                                                    };
                                                })}
                                            />
                                        </Form.Item>
                                    </Col>
                                )}
                            </Row>
                            <Form.List name="options">
                                {(fields, { add, remove }) => (
                                    <>
                                        {fields.map((field, n) => (
                                            <>
                                                <Row
                                                    className="event-option-card"
                                                    key={`opt-${n}`}
                                                >
                                                    <Col
                                                        flex={1}
                                                        style={{
                                                            display: 'flex',
                                                            flexDirection: 'column',
                                                            rowGap: '10px',
                                                        }}
                                                    >
                                                        <Row
                                                            className="p2"
                                                            style={{
                                                                justifyContent: 'space-between',
                                                            }}
                                                        >
                                                            <Col>
                                                                <span>Вариант ответа №{n + 1}</span>
                                                            </Col>
                                                            <Col>
                                                                <Button
                                                                    danger
                                                                    disabled={disableEdit}
                                                                    onClick={() =>
                                                                        remove(field.name)
                                                                    }
                                                                >
                                                                    Удалить
                                                                </Button>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col className="labeled-input">
                                                                <span className="p2">
                                                                    Мой ответ:
                                                                </span>
                                                                <Form.Item
                                                                    name={[field.name, 'text']}
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                'Введите текст ответа',
                                                                        },
                                                                        {
                                                                            max: 300,
                                                                            message:
                                                                                'Не более 300 символов',
                                                                        },
                                                                    ]}
                                                                >
                                                                    <TextArea
                                                                        allowClear
                                                                        disabled={disableEdit}
                                                                        maxLength={300}
                                                                        rows={3}
                                                                        showCount
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col className="labeled-input">
                                                                <span className="p2">
                                                                    Ответ отправителя:
                                                                </span>
                                                                <Form.Item
                                                                    name={[field.name, 'reply']}
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                'Введите ответ отправителя',
                                                                        },
                                                                        {
                                                                            max: 300,
                                                                            message:
                                                                                'Не более 300 символов',
                                                                        },
                                                                    ]}
                                                                >
                                                                    <TextArea
                                                                        allowClear
                                                                        disabled={disableEdit}
                                                                        maxLength={300}
                                                                        rows={3}
                                                                        showCount
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                        <Row className="p2">
                                                            Реакции:{' '}
                                                            {state.event?.options[n].reactions
                                                                .length == 0 && 'отсутствуют'}
                                                        </Row>
                                                        <Form.Item>
                                                            <Form.List
                                                                name={[field.name, 'reactions']}
                                                            >
                                                                {(reactionFields, reactionOpt) => (
                                                                    <div className="reaction-block-wrapper">
                                                                        {reactionFields.map(
                                                                            (reactionField, nr) => (
                                                                                <Row
                                                                                    className="event-option-card reaction-block"
                                                                                    key={`opt-${n}-r-${nr}`}
                                                                                >
                                                                                    <Col
                                                                                        flex={1}
                                                                                        style={{
                                                                                            display:
                                                                                                'flex',
                                                                                            flexDirection:
                                                                                                'column',
                                                                                            rowGap: '10px',
                                                                                        }}
                                                                                    >
                                                                                        <Row
                                                                                            className="p2"
                                                                                            style={{
                                                                                                justifyContent:
                                                                                                    'space-between',
                                                                                            }}
                                                                                        >
                                                                                            <Col>
                                                                                                <span>
                                                                                                    Реакция
                                                                                                    №
                                                                                                    {nr +
                                                                                                        1}
                                                                                                </span>
                                                                                            </Col>
                                                                                            <Col>
                                                                                                <Button
                                                                                                    danger
                                                                                                    disabled={
                                                                                                        disableEdit
                                                                                                    }
                                                                                                    onClick={() =>
                                                                                                        reactionOpt.remove(
                                                                                                            reactionField.name,
                                                                                                        )
                                                                                                    }
                                                                                                >
                                                                                                    Удалить
                                                                                                </Button>
                                                                                            </Col>
                                                                                        </Row>
                                                                                        <Row>
                                                                                            <Col className="labeled-input">
                                                                                                <span className="p2">
                                                                                                    Отправитель:
                                                                                                </span>
                                                                                                <Form.Item
                                                                                                    name={[
                                                                                                        reactionField.name,
                                                                                                        'reaction_sender_id',
                                                                                                    ]}
                                                                                                >
                                                                                                    <Select
                                                                                                        disabled={
                                                                                                            disableEdit
                                                                                                        }
                                                                                                        options={[
                                                                                                            {
                                                                                                                label: 'Система',
                                                                                                                value: 'sys',
                                                                                                            },
                                                                                                            {
                                                                                                                label: 'Клиент',
                                                                                                                value: 'client',
                                                                                                            },
                                                                                                            {
                                                                                                                label: 'Команда',
                                                                                                                value: 'team',
                                                                                                            },
                                                                                                            ...state.workers?.map(
                                                                                                                (
                                                                                                                    wi,
                                                                                                                ) => {
                                                                                                                    return {
                                                                                                                        label: `${wi.id}: ${wi.name}`,
                                                                                                                        value: wi.uid,
                                                                                                                    };
                                                                                                                },
                                                                                                            ),
                                                                                                        ]}
                                                                                                    />
                                                                                                </Form.Item>
                                                                                            </Col>
                                                                                        </Row>
                                                                                        <Row>
                                                                                            <Col className="labeled-input">
                                                                                                <span className="p2">
                                                                                                    Содержание:
                                                                                                </span>
                                                                                                <Form.Item
                                                                                                    name={[
                                                                                                        reactionField.name,
                                                                                                        'reaction_text',
                                                                                                    ]}
                                                                                                    rules={[
                                                                                                        {
                                                                                                            required: true,
                                                                                                            message:
                                                                                                                'Введите текст реакции',
                                                                                                        },
                                                                                                        {
                                                                                                            max: 300,
                                                                                                            message:
                                                                                                                'Не больше 300 символов',
                                                                                                        },
                                                                                                    ]}
                                                                                                >
                                                                                                    <TextArea
                                                                                                        allowClear
                                                                                                        disabled={
                                                                                                            disableEdit
                                                                                                        }
                                                                                                        maxLength={
                                                                                                            300
                                                                                                        }
                                                                                                        rows={
                                                                                                            3
                                                                                                        }
                                                                                                        showCount
                                                                                                    />
                                                                                                </Form.Item>
                                                                                            </Col>
                                                                                        </Row>
                                                                                    </Col>
                                                                                </Row>
                                                                            ),
                                                                        )}
                                                                        {state.event?.options[n]
                                                                            .reactions.length <
                                                                            state.workers.length &&
                                                                            !disableEdit && (
                                                                                <Row
                                                                                    className="event-option-add"
                                                                                    onClick={() =>
                                                                                        reactionOpt.add(
                                                                                            makeOptionReactionTemplate(
                                                                                                n,
                                                                                            ),
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <Col className="p2">
                                                                                        Добавить
                                                                                        реакцию
                                                                                    </Col>
                                                                                    <Col>
                                                                                        <div className="add-icon" />
                                                                                    </Col>
                                                                                </Row>
                                                                            )}
                                                                    </div>
                                                                )}
                                                            </Form.List>
                                                        </Form.Item>
                                                        <Row className="p2">
                                                            Последствия:{' '}
                                                            {state.event?.options[n].effects
                                                                .length == 0 && 'отсутствуют'}
                                                        </Row>
                                                        <Form.Item>
                                                            <Form.List
                                                                name={[field.name, 'effects']}
                                                            >
                                                                {(effectFields, effectOpt) => (
                                                                    <div className="effect-block-wrapper">
                                                                        {effectFields.map(
                                                                            (effectField, ne) => (
                                                                                <Row
                                                                                    className="event-option-card effect-block"
                                                                                    key={`opt-${n}-e-${ne}`}
                                                                                >
                                                                                    <Col
                                                                                        flex={1}
                                                                                        style={{
                                                                                            display:
                                                                                                'flex',
                                                                                            flexDirection:
                                                                                                'column',
                                                                                            rowGap: '10px',
                                                                                        }}
                                                                                    >
                                                                                        <Row
                                                                                            className="p2"
                                                                                            style={{
                                                                                                justifyContent:
                                                                                                    'space-between',
                                                                                            }}
                                                                                        >
                                                                                            <Col>
                                                                                                <span>
                                                                                                    Последствие
                                                                                                    №
                                                                                                    {ne +
                                                                                                        1}
                                                                                                </span>
                                                                                            </Col>
                                                                                            <Col>
                                                                                                <Button
                                                                                                    danger
                                                                                                    disabled={
                                                                                                        disableEdit
                                                                                                    }
                                                                                                    onClick={() =>
                                                                                                        effectOpt.remove(
                                                                                                            effectField.name,
                                                                                                        )
                                                                                                    }
                                                                                                >
                                                                                                    Удалить
                                                                                                </Button>
                                                                                            </Col>
                                                                                        </Row>
                                                                                        <Row>
                                                                                            <Col className="labeled-input">
                                                                                                <span className="p2">
                                                                                                    Параметр:
                                                                                                </span>
                                                                                                <Form.Item
                                                                                                    name={[
                                                                                                        effectField.name,
                                                                                                        'param',
                                                                                                    ]}
                                                                                                >
                                                                                                    <Select
                                                                                                        disabled={
                                                                                                            disableEdit
                                                                                                        }
                                                                                                        options={
                                                                                                            effectParamOptions
                                                                                                        }
                                                                                                        style={{
                                                                                                            minWidth:
                                                                                                                '260px',
                                                                                                        }}
                                                                                                    />
                                                                                                </Form.Item>
                                                                                            </Col>
                                                                                            <Col className="labeled-input">
                                                                                                <span className="p2">
                                                                                                    Модификатор:
                                                                                                </span>
                                                                                                <Form.Item
                                                                                                    name={[
                                                                                                        effectField.name,
                                                                                                        'modifier',
                                                                                                    ]}
                                                                                                    rules={[
                                                                                                        {
                                                                                                            required: true,
                                                                                                            message:
                                                                                                                'Введите модиификатор',
                                                                                                        },
                                                                                                        {
                                                                                                            type: 'integer',
                                                                                                            max:
                                                                                                                state
                                                                                                                    .event
                                                                                                                    ?.options[
                                                                                                                    n
                                                                                                                ]
                                                                                                                    .effects[
                                                                                                                    ne
                                                                                                                ]
                                                                                                                    .param ==
                                                                                                                'cost'
                                                                                                                    ? 10000
                                                                                                                    : 10,
                                                                                                            min:
                                                                                                                state
                                                                                                                    .event
                                                                                                                    ?.options[
                                                                                                                    n
                                                                                                                ]
                                                                                                                    .effects[
                                                                                                                    ne
                                                                                                                ]
                                                                                                                    .param ==
                                                                                                                'cost'
                                                                                                                    ? -10000
                                                                                                                    : -10,
                                                                                                            message: `От ${
                                                                                                                state
                                                                                                                    .event
                                                                                                                    ?.options[
                                                                                                                    n
                                                                                                                ]
                                                                                                                    .effects[
                                                                                                                    ne
                                                                                                                ]
                                                                                                                    .param ==
                                                                                                                'cost'
                                                                                                            } до ${
                                                                                                                state
                                                                                                                    .event
                                                                                                                    ?.options[
                                                                                                                    n
                                                                                                                ]
                                                                                                                    .effects[
                                                                                                                    ne
                                                                                                                ]
                                                                                                                    .param ==
                                                                                                                'cost'
                                                                                                                    ? 10000
                                                                                                                    : 10
                                                                                                            }`,
                                                                                                        },
                                                                                                    ]}
                                                                                                >
                                                                                                    <InputNumber
                                                                                                        disabled={
                                                                                                            disableEdit
                                                                                                        }
                                                                                                        max={
                                                                                                            state
                                                                                                                .event
                                                                                                                ?.options[
                                                                                                                n
                                                                                                            ]
                                                                                                                .effects[
                                                                                                                ne
                                                                                                            ]
                                                                                                                .param ==
                                                                                                            'cost'
                                                                                                                ? 10000
                                                                                                                : 10
                                                                                                        }
                                                                                                        min={
                                                                                                            state
                                                                                                                .event
                                                                                                                ?.options[
                                                                                                                n
                                                                                                            ]
                                                                                                                .effects[
                                                                                                                ne
                                                                                                            ]
                                                                                                                .param ==
                                                                                                            'cost'
                                                                                                                ? -10000
                                                                                                                : -10
                                                                                                        }
                                                                                                        step={
                                                                                                            1
                                                                                                        }
                                                                                                    />
                                                                                                </Form.Item>
                                                                                            </Col>
                                                                                        </Row>
                                                                                    </Col>
                                                                                </Row>
                                                                            ),
                                                                        )}
                                                                        {state.event?.options[n]
                                                                            .effects.length <
                                                                            effectParamOptions.length &&
                                                                            !disableEdit && (
                                                                                <Row
                                                                                    className="event-option-add"
                                                                                    onClick={() =>
                                                                                        effectOpt.add(
                                                                                            makeOptionEffectTemplate(
                                                                                                n,
                                                                                            ),
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <Col className="p2">
                                                                                        Добавить
                                                                                        последствие
                                                                                    </Col>
                                                                                    <Col>
                                                                                        <div className="add-icon" />
                                                                                    </Col>
                                                                                </Row>
                                                                            )}
                                                                    </div>
                                                                )}
                                                            </Form.List>
                                                        </Form.Item>
                                                    </Col>
                                                </Row>
                                            </>
                                        ))}
                                        {!disableEdit && (
                                            <Row
                                                className="event-option-add"
                                                onClick={() => add(makeOptionTemplate())}
                                            >
                                                <Col className="p2">Добавить вариант ответа</Col>
                                                <Col>
                                                    <div className="add-icon" />
                                                </Col>
                                            </Row>
                                        )}
                                    </>
                                )}
                            </Form.List>
                            {state.useCase == 'new' ? (
                                <Row className="controls-row p2">
                                    <Button
                                        disabled={state.isLoading || disableEdit}
                                        htmlType="submit"
                                    >
                                        Сохранить
                                    </Button>
                                    <Button
                                        disabled={state.isLoading || disableEdit}
                                        onClick={cancelCreation}
                                    >
                                        Отмена
                                    </Button>
                                </Row>
                            ) : (
                                <Row className="controls-row p2">
                                    {state.anyChanges && (
                                        <Button
                                            disabled={state.isLoading || disableEdit}
                                            htmlType="submit"
                                        >
                                            Сохранить
                                        </Button>
                                    )}
                                    {state.anyChanges && (
                                        <Button
                                            disabled={state.isLoading || disableEdit}
                                            onClick={cancelCreation}
                                        >
                                            Отмена
                                        </Button>
                                    )}
                                    {state.event?.deleted_at == null ? (
                                        <Popconfirm
                                            cancelText="Отмена"
                                            disabled={disableEdit}
                                            okText="Подтвердить"
                                            onConfirm={deleteEvent}
                                            title="Событие будет удалено"
                                        >
                                            <Button disabled={disableEdit || state.isLoading}>
                                                Удалить
                                            </Button>
                                        </Popconfirm>
                                    ) : (
                                        <Button
                                            disabled={disableEdit || state.isLoading}
                                            onClick={restoreEvent}
                                        >
                                            Восстановить
                                        </Button>
                                    )}
                                </Row>
                            )}
                        </Col>
                    </Form>
                </div>
            </div>
        </UniLayout>
    );
};

export default ConstructorEventProfile;
