import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { Button, Col, ConfigProvider, List, message, Pagination, Radio, Row, Tooltip } from 'antd';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimEvent } from 'types/simulation/simulationEvent';
import { Permissions } from '@classes/permissions';
import { CRMAPIManager } from '@api/crmApiManager';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';
import { GlobalConstants } from '@classes/constants';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import Colors from '@classes/colors';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { TMetadata } from 'types/api/metadata';

import './constructorEventList.scss';

type TState = {
    events: TSimEvent[];
    eventsMeta: TMetadata | null;
    isLoading: boolean;
    mode: 'current' | 'deleted' | 'all';
    page: number;
    perPage: number;
    simulation: TSimulation | null;
};

const ConstructorEventList = (): JSX.Element => {
    const state = useReactive<TState>({
        events: [],
        eventsMeta: null,
        isLoading: false,
        mode: 'current',
        page: 1,
        perPage: 10,
        simulation: null,
    });
    const { simId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.SimulationGet) &&
            Permissions.checkPermission(Permissions.SimulationEventList)
        );
    }

    async function loadParentSim() {
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке симуляции!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadEventList(page: number = state.page, addition: boolean = false) {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimEventListResp>(async (api) => {
                return await api.getSimEventList({
                    simulation_id: +simId,
                    query: '',
                    page: page,
                    per_page: state.perPage,
                    sort_by: 'created_at',
                    sort_direction: 'desc',
                    filters: {
                        deleted:
                            state.mode == 'current'
                                ? 'null'
                                : state.mode == 'deleted'
                                  ? 'only'
                                  : 'all',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.events = addition ? [...state.events, ...tl.data.data] : tl.data.data;
            state.eventsMeta = tl.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка событий');
            console.log(err);
        }
        state.isLoading = false;
    }

    function makeListItems() {
        const arr = [];
        if (
            Permissions.checkPermission(Permissions.SimulationEventCreate) &&
            state.mode == 'current' &&
            !disableEdit
        ) {
            arr.push('add');
        }
        arr.push(...state.events);
        return arr;
    }

    async function initEventList() {
        if (state.simulation == null) await loadParentSim();
        if (state.simulation == null) return;
        await loadEventList(1);
        state.page = 1;
    }

    useEffect(() => {
        initEventList();
    }, [state.mode]);

    async function restoreEvent(event: TSimEvent) {
        messageApi.open({
            type: 'loading',
            content: 'Восстанавливаем...',
            duration: 0,
        });
        try {
            const result = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return await api.restoreSimEvent(event.simulation_id, event.uid);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadEventList();
            messageApi.destroy();
            messageApi.success('Событие восстановлено');
        } catch (errors) {
            messageApi.destroy();
            messageApi.error('Ошибка при восстановлении');
            console.log(errors);
        }
    }

    async function onPageOrPerPageChange(page: number, pageSize: number) {
        if (page != state.page) {
            await loadEventList(page);
            state.page = page;
        }
        if (pageSize != state.perPage) {
            if (state.eventsMeta?.total == null || state.eventsMeta?.total == 0) return;
            state.perPage = pageSize;
            state.page = Math.ceil(state.eventsMeta?.from_ / pageSize);
            await loadEventList();
        }
    }

    async function loadMore() {
        state.page += 1;
        await loadEventList(state.page, true);
    }

    return (
        <UniLayout
            activeTab="events"
            additionalClass="list-min-width"
            tabSet="constructor"
        >
            <div className="event-list-container">
                {contextHolder}
                <div className="radio-group">
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.mode = e.target.value;
                        }}
                        options={[
                            { label: 'Текущие', value: 'current' },
                            { label: 'Корзина', value: 'deleted' },
                            { label: 'Все', value: 'all' },
                        ]}
                        optionType="button"
                        value={state.mode}
                    />
                </div>
                <div className="event-list">
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            itemLayout="horizontal"
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких событий нет :)</Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item: 'add' | TSimEvent) => {
                                if (item == 'add')
                                    return (
                                        <div
                                            className="event-list-add"
                                            onClick={() => {
                                                navigate(`/constructor/${simId}/events/new`);
                                            }}
                                        >
                                            <div className="add-icon" />
                                        </div>
                                    );
                                else
                                    return (
                                        <div
                                            className={`event-list-card${item.deleted_at != null ? ' del-options' : ''}`}
                                        >
                                            <Row className="header-row">
                                                <Col>
                                                    <Row>
                                                        <div className="p2-strong">
                                                            ID {item.id}
                                                        </div>
                                                        <CopyButton
                                                            textToCopy={`Событие #${item.id} симуляции #${simId}`}
                                                            textToShow="ID события скопирован"
                                                            size={24}
                                                        />
                                                    </Row>
                                                </Col>
                                                {item.deleted_at != null && (
                                                    <Col>
                                                        <FilterButton
                                                            hex={Colors.Error.warm[500]}
                                                            text="Удалён"
                                                        />
                                                    </Col>
                                                )}
                                            </Row>
                                            <Row className="body-row">
                                                <Col>
                                                    <Row>
                                                        <div className="p2-strong">
                                                            {item.title}
                                                        </div>
                                                        <CopyButton
                                                            textToCopy={`Событие \'${item.title}\' симуляции #${simId}`}
                                                            textToShow="Название события скопировано"
                                                            size={24}
                                                        />
                                                    </Row>
                                                </Col>
                                            </Row>
                                            <Row className="p2 split-row">
                                                <Col>
                                                    <Tooltip title="Условие срабатывания события">
                                                        Связь:{' '}
                                                        {item.trigger_type == 'day'
                                                            ? 'день'
                                                            : 'узел'}{' '}
                                                        №{item.trigger_arg}
                                                    </Tooltip>
                                                </Col>
                                                <Col>Опций: {item.options.length}</Col>
                                            </Row>
                                            <Row className="event-card-controls p3">
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(
                                                            `/constructor/${simId}/events/${item.uid}`,
                                                        );
                                                    }}
                                                >
                                                    Открыть
                                                </Button>
                                                {item.deleted_at != null && !disableEdit && (
                                                    <Button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            restoreEvent(item);
                                                        }}
                                                    >
                                                        Восстановить
                                                    </Button>
                                                )}
                                            </Row>
                                        </div>
                                    );
                            }}
                        />
                    </ConfigProvider>
                    <Row className="pagination-row">
                        {state.eventsMeta?.total != null &&
                            state.page != state.eventsMeta?.last_page && (
                                <Button
                                    className="p3"
                                    onClick={loadMore}
                                    type="primary"
                                >
                                    Показать ещё
                                </Button>
                            )}
                        <Pagination
                            align="end"
                            current={state.page}
                            pageSize={state.perPage}
                            pageSizeOptions={[
                                10,
                                20,
                                50,
                                100,
                            ]}
                            total={state.eventsMeta?.total ?? 0}
                            onChange={onPageOrPerPageChange}
                            showSizeChanger
                        />
                    </Row>
                </div>
            </div>
        </UniLayout>
    );
};

export default ConstructorEventList;
