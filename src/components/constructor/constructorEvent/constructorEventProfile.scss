@use '/src/styles/colors';
@use '/src/styles/icons';

.event-profile-container {
    width: 100%;

    .event-profile {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 40px;
        width: 100%;

        .event-form {
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 4px;

                .ant-form-item {
                    width: 100%;
                }
            }
        }
        .labeled-input {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
            width: 100%;
        }
        .top-row {
            align-items: center;
            justify-content: space-between;

            h3 {
                margin: 0;
            }
        }
        .split-row {
            column-gap: 20px;
            flex-wrap: nowrap;

            > .ant-col {
                width: calc(50% - 10px);
            }
        }
        .ant-select {
            width: auto;
        }
        .reaction-block-wrapper,
        .effect-block-wrapper {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
        }
        .event-option-card,
        .event-option-add {
            border: 2px solid colors.$neutral25;
            padding: 10px;
        }
        .event-option-add {
            align-items: center;
            display: flex;
            flex-direction: row;
            justify-content: center;
            min-height: 48px;
            width: 100%;
            .add-icon {
                @include icons.icon-plus('#272C35');
                background-repeat: no-repeat;
                background-size: contain;
                height: 32px;
                width: 32px;
            }
            &:hover {
                border: 2px solid #35abff;
                .add-icon {
                    @include icons.icon-plus('#35ABFF');
                }
            }
        }
        .reaction-block .ant-row:last-child {
            padding-bottom: 12px;
        }
        .controls-row {
            column-gap: 32px;
            padding-top: 16px;
            row-gap: 8px;

            .ant-btn {
                background: colors.$accentW0;
                border: 2px solid colors.$neutral25;
                border-radius: 4px;
                color: colors.$neutral950;
                height: 48px;

                &:disabled {
                    background: colors.$neutral25;
                    color: colors.$neutral300;
                }
                &:not(:disabled):hover {
                    background: colors.$accentW10;
                    border: 2px solid colors.$accentW10;
                    color: colors.$accentW500;
                }
            }
        }
        .ant-input-affix-wrapper-disabled,
        textarea.ant-input-disabled,
        .ant-input-number-disabled,
        .ant-select-disabled .ant-select-selector {
            background-color: colors.$neutral10;
            color: colors.$neutral800;
        }
        .ant-input-textarea-affix-wrapper .ant-input-suffix .ant-input-data-count {
            bottom: -25px;
        }
    }
}
@media only screen and (orientation: portrait) {
    .event-profile-container {
        .event-profile {
            padding: 16px;
        }
    }
}
