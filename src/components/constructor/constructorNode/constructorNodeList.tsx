import { CRMAPIManager } from '@api/crmApiManager';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimTask } from 'types/simulation/simulationTask';
import { Button, Col, ConfigProvider, List, message, Pagination, Radio, Row, Tooltip } from 'antd';
import { Permissions } from '@classes/permissions';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { SimTaskResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskResponse';
import { DefaultTimeSettings } from '@store/ingame/data';
import { TSimulation } from 'types/simulation/simulation';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { GlobalConstants } from '@classes/constants';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import Colors from '@classes/colors';
import { TMetadata } from 'types/api/metadata';

import './constructorNodeList.scss';

type TState = {
    isLoading: boolean;
    mode: 'current' | 'deleted' | 'all';
    page: number;
    perPage: number;
    simulation: TSimulation | null;
    tasks: TSimTask[];
    tasksMeta: TMetadata | null;
};

const ConstructorNodeList = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        mode: 'current',
        page: 1,
        perPage: 10,
        simulation: null,
        tasks: [],
        tasksMeta: null,
    });
    const { simId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.SimulationGet) &&
            Permissions.checkPermission(Permissions.SimulationTaskList)
        );
    }

    async function loadParentSim() {
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке симуляции!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadTaskList(page: number = state.page, addition: boolean = false) {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.request<SimTaskListResp>(async (api) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: page,
                    per_page: state.perPage,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted:
                            state.mode == 'current'
                                ? 'null'
                                : state.mode == 'deleted'
                                  ? 'only'
                                  : 'all',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = addition ? [...state.tasks, ...tl.data.data] : tl.data.data;
            state.tasksMeta = tl.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка узлов');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function initNodeList() {
        if (state.simulation == null) await loadParentSim();
        if (state.simulation == null) return;
        await loadTaskList(1);
        state.page = 1;
    }

    useEffect(() => {
        initNodeList();
    }, [state.mode]);

    function makeListItems() {
        const arr = [];
        if (
            Permissions.checkPermission(Permissions.SimulationTaskCreate) &&
            state.mode == 'current' &&
            !disableEdit
        ) {
            arr.push('add');
        }
        arr.push(...state.tasks);
        return arr;
    }

    async function restoreTask(task: TSimTask) {
        messageApi.open({
            type: 'loading',
            content: 'Восстанавливаем...',
            duration: 0,
        });
        try {
            const result = await CRMAPIManager.request<SimTaskResp>(async (api) => {
                return await api.restoreSimTask(task.simulation_id, task.uid);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadTaskList();
            messageApi.destroy();
            messageApi.success('Узел восстановлен');
        } catch (errors) {
            messageApi.destroy();
            messageApi.error('Ошибка при восстановлении');
            console.log(errors);
        }
    }

    function calcWeight(task: TSimTask) {
        if (!task.est_duration || !task.est_workers) {
            return '-';
        } else {
            return (
                task.est_duration *
                task.est_workers *
                (DefaultTimeSettings.workDayHours - +DefaultTimeSettings.workDayLunchSkip)
            );
        }
    }

    async function onPageOrPerPageChange(page: number, pageSize: number) {
        if (page != state.page) {
            await loadTaskList(page);
            state.page = page;
        }
        if (pageSize != state.perPage) {
            if (state.tasksMeta?.total == null || state.tasksMeta?.total == 0) return;
            state.perPage = pageSize;
            state.page = Math.ceil(state.tasksMeta?.from_ / pageSize);
            await loadTaskList();
        }
    }

    async function loadMore() {
        state.page += 1;
        await loadTaskList(state.page, true);
    }

    return (
        <UniLayout
            activeTab="nodes"
            additionalClass="list-min-width"
            tabSet="constructor"
        >
            <div className="task-list-container">
                {contextHolder}
                <div className="radio-group">
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.mode = e.target.value;
                        }}
                        options={[
                            { label: 'Текущие', value: 'current' },
                            { label: 'Корзина', value: 'deleted' },
                            { label: 'Все', value: 'all' },
                        ]}
                        optionType="button"
                        value={state.mode}
                    />
                </div>
                <div className="task-list">
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            itemLayout="horizontal"
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких узлов нет :)</Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item: 'add' | TSimTask) => {
                                if (item == 'add')
                                    return (
                                        <div
                                            className="task-list-add"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                navigate(`/constructor/${simId}/nodes/new`);
                                            }}
                                        >
                                            <div className="add-icon" />
                                        </div>
                                    );
                                else
                                    return (
                                        <div
                                            className={`task-list-card${item.deleted_at != null ? ' del-options' : ''}`}
                                        >
                                            <Row className="split-row">
                                                <Col>
                                                    <Row>
                                                        <div className="p2-strong">
                                                            ID {item.id}
                                                        </div>
                                                        <CopyButton
                                                            textToCopy={`Узел #${item.id} симуляции #${simId}`}
                                                            textToShow="ID узла скопирован"
                                                            size={24}
                                                        />
                                                    </Row>
                                                </Col>
                                                <Col>
                                                    <Tooltip title="Базовая продолжительность, исходя из плановых дней и ПШЕ">
                                                        <div
                                                            className={`p2-strong ${item.milestone ? 'crit' : ''}`}
                                                        >
                                                            {calcWeight(item)} часов
                                                        </div>
                                                    </Tooltip>
                                                </Col>
                                            </Row>
                                            <Row className="split-row">
                                                <Col>
                                                    <Row>
                                                        <div className="p2-strong cut-name">
                                                            {item.name}
                                                        </div>
                                                        <CopyButton
                                                            textToCopy={`Узел \'${item.name}\' симуляции #${simId}`}
                                                            textToShow="Название узла скопировано"
                                                            size={24}
                                                        />
                                                    </Row>
                                                </Col>
                                                <Col>
                                                    <div className="p3">
                                                        {item.est_budget / 1000} т.р.
                                                    </div>
                                                </Col>
                                            </Row>
                                            <Row className="split-row">
                                                <Col>
                                                    <div className="p3">
                                                        Входящих узлов: {item.previous.length}
                                                    </div>
                                                    <div className="p3">
                                                        Исходящих узлов:{' '}
                                                        {
                                                            state.tasks.filter((t) =>
                                                                t.previous.includes(item.id),
                                                            ).length
                                                        }
                                                    </div>
                                                </Col>
                                                <Col>
                                                    <div className="p3">
                                                        {`${item.est_duration} дней`}
                                                    </div>
                                                    <div className="p3">
                                                        {`${item.est_workers} ПШЕ`}
                                                    </div>
                                                </Col>
                                            </Row>
                                            <Row className="event-card-controls split-row p3">
                                                <Col>
                                                    <Row>
                                                        <Button
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                navigate(
                                                                    `/constructor/${simId}/nodes/${item.id}`,
                                                                );
                                                            }}
                                                        >
                                                            Открыть
                                                        </Button>
                                                        {item.deleted_at != null &&
                                                            !disableEdit && (
                                                                <Button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        restoreTask(item);
                                                                    }}
                                                                >
                                                                    Восстановить
                                                                </Button>
                                                            )}
                                                    </Row>
                                                </Col>
                                                {item.deleted_at != null && (
                                                    <Col>
                                                        <FilterButton
                                                            hex={Colors.Error.warm[500]}
                                                            text="Удалён"
                                                        />
                                                    </Col>
                                                )}
                                            </Row>
                                        </div>
                                    );
                            }}
                        />
                    </ConfigProvider>
                    <Row className="pagination-row">
                        {state.tasksMeta?.total != null &&
                            state.page != state.tasksMeta?.last_page && (
                                <Button
                                    className="p3"
                                    onClick={loadMore}
                                    type="primary"
                                >
                                    Показать ещё
                                </Button>
                            )}
                        <Pagination
                            align="end"
                            current={state.page}
                            pageSize={state.perPage}
                            pageSizeOptions={[
                                10,
                                20,
                                50,
                                100,
                            ]}
                            total={state.tasksMeta?.total ?? 0}
                            onChange={onPageOrPerPageChange}
                            showSizeChanger
                        />
                    </Row>
                </div>
            </div>
        </UniLayout>
    );
};

export default ConstructorNodeList;
