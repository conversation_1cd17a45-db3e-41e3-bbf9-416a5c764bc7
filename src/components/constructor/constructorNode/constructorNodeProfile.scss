@use '../../../styles/colors';
@use '../../../styles/icons';

.task-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .task-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 32px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .id-row {
            align-items: center;
            justify-content: space-between;
            row-gap: 8px;

            h3 {
                margin: 0;
            }
        }
        .node-form {
            display: flex;
            flex-direction: column;
            row-gap: 16px;

            .inputs-row {
                align-content: center;
                align-items: flex-start;
                column-gap: 20px;
                flex-wrap: wrap;
                justify-content: space-evenly;
                width: 100%;

                .ant-form-item {
                    width: 100%;
                }
                .ant-col {
                    width: calc(50% - 10px);
                }
            }
            .multi-input-row {
                column-gap: 20px;

                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: calc(50% - 10px);

                    > .ant-row {
                        align-items: center;
                        column-gap: 8px;
                        display: flex;
                        flex-direction: row;
                        flex-wrap: nowrap;

                        .labeled-input {
                            display: flex;
                            flex-direction: column;
                            row-gap: 8px;
                            width: 100%;

                            .ant-form-item:has(.ant-slider) {
                                margin-bottom: 0;
                            }
                            .ant-slider {
                                margin: 0 12px 12px 12px;
                                width: calc(100% - 20px);
                            }
                        }
                        .labeled-input .ant-input-affix-wrapper,
                        .labeled-input .ant-input-number,
                        .labeled-input .ant-input-number-affix-wrapper,
                        .ant-form-item {
                            width: 100%;
                        }
                    }
                }
            }
            .info-row {
                .labeled-input {
                    .lighter-tone {
                        color: colors.$neutral700;
                    }
                }
            }
            .controls-row {
                column-gap: 32px;
                row-gap: 8px;

                .ant-form-item {
                    margin-bottom: 0;
                }
                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;

                    &:disabled {
                        background: colors.$neutral25;
                        color: colors.$neutral300;
                    }
                    &:not(:disabled):hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
        }
        .ant-collapse {
            border-radius: 8px;
            border: 2px solid colors.$neutral25;

            .ant-collapse-item {
                border-bottom: 2px solid colors.$neutral25;
                min-height: 48px;

                .ant-collapse-header {
                    align-items: center;

                    h6 {
                        margin: 0;
                    }
                    .expand-icon {
                        @include icons.icon-arrow('#272C35');
                        background-repeat: no-repeat;
                        background-size: contain;
                        height: 32px;
                        width: 32px;
                        transform: rotate(270deg);
                    }
                }
                &.ant-collapse-item-active {
                    .ant-collapse-header {
                        .expand-icon {
                            @include icons.icon-arrow('#272C35');
                            background-repeat: no-repeat;
                            background-size: contain;
                            height: 32px;
                            width: 32px;
                            transform: rotate(90deg);
                        }
                    }
                }
                .ant-collapse-content {
                    background: colors.$neutral25;

                    .link-task-card {
                        background: colors.$accentW0;
                        border: 0.5px solid colors.$neutral100;
                        border-radius: 1px;
                        cursor: pointer;
                        display: flex;
                        flex-direction: column;
                        flex-wrap: nowrap;
                        height: 36px;
                        padding: 4px;
                        width: 100px;
                    }
                    .gray {
                        color: colors.$neutral500;
                    }
                }
            }
            .ant-collapse-item:last-child {
                border-bottom: none;
            }
        }
        .ant-input-affix-wrapper-disabled,
        textarea.ant-input-disabled,
        .ant-input-number-disabled {
            background-color: colors.$neutral10;
            color: colors.$neutral800;
        }
    }
}
@media screen and (orientation: portrait) {
    .task-profile .task-card {
        padding: 16px;

        .inputs-row {
            row-gap: 32px;
            width: 100%;

            > .ant-col {
                width: 100% !important;
            }
        }
        .multi-input-row {
            row-gap: 32px;
            width: 100%;

            > .ant-col {
                width: 100% !important;
            }
        }
    }
}
