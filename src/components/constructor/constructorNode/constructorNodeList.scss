@use '../../../styles/colors';
@use '../../../styles/icons';

.task-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    min-width: 556px;
    width: 100%;

    .radio-group {
        padding: 24px 24px 12px 40px;
    }
    .task-list {
        display: flex;
        flex-direction: column;
        min-width: 556px;
        padding: 20px 40px 140px 38px;
        row-gap: 16px;
        width: 100%;

        .ant-list-empty-text {
            background: colors.$accentW0;
            color: colors.$neutral950;

            .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .ant-row {
                justify-content: center;
            }
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            min-width: 480px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .task-list-add,
        .task-list-card {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            height: 184px;
            width: 480px;
        }
        .task-list-add {
            align-items: center;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .add-icon {
                @include icons.icon-plus('#272C35');
                background-repeat: no-repeat;
                background-size: contain;
                height: 32px;
                width: 32px;
            }
            &:hover {
                border: 2px solid colors.$accentW500;

                .add-icon {
                    @include icons.icon-plus('#35ABFF');
                }
            }
        }
        .task-list-card {
            align-items: flex-start;
            display: flex;
            flex-direction: column;
            row-gap: 8px;
            padding: 12px;

            .crit {
                color: colors.$errorC700;
            }
            .split-row {
                flex-wrap: nowrap;
                justify-content: space-between;
                width: 100%;
            }
            .cut-name {
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                min-height: 24px;
                max-height: 24px;
                max-width: 350px;
                overflow: hidden;
                white-space: normal;
                word-break: break-all;
            }
            .event-card-controls {
                align-items: center;
                padding-bottom: 0;

                .ant-col:first-child .ant-row {
                    column-gap: 24px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                    }
                }
            }
        }
        .pagination-row {
            column-gap: 16px;
            justify-content: center;
        }
    }
}
@media only screen and (orientation: portrait) {
    .task-list-container {
        min-width: 334px;

        .radio-group {
            padding: 8px 0 4px 16px;
        }
        .task-list {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            .ant-list {
                min-width: 318px;

                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 0;
                    min-width: 318px;
                }
                .task-list-add,
                .task-list-card {
                    width: 318px;

                    .cut-name {
                        max-width: 200px;
                    }
                }
                .task-list-add {
                    height: 80px;
                }
                .task-list-card {
                    height: 184px;
                }
                .task-list-card.del-options {
                    height: 244px;

                    .event-card-controls > .ant-col > .ant-row {
                        row-gap: 8px;
                    }
                }
            }
        }
    }
}
