import React, { useCallback, memo, useEffect } from 'react';
import {
    Node,
    Edge,
    ReactFlowProvider,
    useReactFlow,
    NodeTypes,
    Position,
    MarkerType,
    Background,
    NodeProps,
    Handle,
    ReactFlow,
} from '@xyflow/react';
import { TaskNode } from './constructorGraph';
import Colors from '@classes/colors';
import { Common } from '@classes/common';

// Определяем компонент узла
const CustomNode = memo(function customNode({ data }: NodeProps<TaskNode>) {
    return (
        <div className="custom-node">
            <Handle
                type="target"
                position={Position.Top}
            />
            <div className="node-header">
                <span>{Common.isNullOrEmptyString(data.name) ? '-Без названия-' : data.name}</span>
            </div>
            <Handle
                type="source"
                position={Position.Bottom}
            />
        </div>
    );
});

// Определяем типы узлов
const nodeTypes: NodeTypes = {
    custom: CustomNode,
};

interface NodePreviewProps {
    node: Node | null;
    nodes: Node[];
    edges: Edge[];
    visible: boolean;
    setSelectedNodeById: (id: string) => Promise<void>;
}

const NodePreview: React.FC<NodePreviewProps> = ({
    node,
    nodes,
    edges,
    visible,
    setSelectedNodeById,
}) => {
    const { fitView } = useReactFlow();

    // Получаем связанные узлы и рёбра
    const getRelatedElements = useCallback(() => {
        if (!node) return { nodes: [], edges: [] };

        const relatedNodeIds = new Set<string>();
        const incomingNodes = new Set<string>();
        const outgoingNodes = new Set<string>();

        // Определяем входящие и исходящие узлы
        edges.forEach((edge) => {
            if (edge.target === node.id) {
                incomingNodes.add(edge.source);
                relatedNodeIds.add(edge.source);
            }
            if (edge.source === node.id) {
                outgoingNodes.add(edge.target);
                relatedNodeIds.add(edge.target);
            }
        });

        // Фильтруем узлы
        const relatedNodes = nodes.filter((n) => relatedNodeIds.has(n.id));

        // Центрируем выбранный узел
        const centerNode = { ...node, position: { x: 0, y: 0 } };

        // Располагаем входящие узлы сверху
        const topNodes = relatedNodes
            .filter((n) => incomingNodes.has(n.id))
            .map((n, i, arr) => {
                const width = 120 * (arr.length - 1);
                const x = -width / 2 + i * 120;
                return {
                    ...n,
                    type: 'custom',
                    position: { x, y: -100 },
                    style: {
                        opacity: 1,
                        background: '#2a2a2a',
                        border: '1px solid #ff69b4',
                        borderRadius: '4px',
                        color: '#fff',
                        fontSize: '10px',
                        padding: '4px',
                        minWidth: '80px',
                        boxShadow: '0 0 10px rgba(255, 105, 180, 0.3)',
                        textAlign: 'center' as const,
                    },
                };
            });

        // Располагаем исходящие узлы снизу
        const bottomNodes = relatedNodes
            .filter((n) => outgoingNodes.has(n.id))
            .map((n, i, arr) => {
                const width = 120 * (arr.length - 1);
                const x = -width / 2 + i * 120;
                return {
                    ...n,
                    type: 'custom',
                    position: { x, y: 100 },
                    style: {
                        opacity: 1,
                        background: '#2a2a2a',
                        border: '1px solid #00bfff',
                        borderRadius: '4px',
                        color: '#fff',
                        fontSize: '10px',
                        padding: '4px',
                        minWidth: '80px',
                        boxShadow: '0 0 10px rgba(0, 191, 255, 0.3)',
                        textAlign: 'center' as const,
                    },
                };
            });

        // Создаем новые связи для миниатюры
        const previewEdges: Edge[] = [];

        // Добавляем связи от верхних узлов к центральному
        topNodes.forEach((n, index) => {
            previewEdges.push({
                id: `preview-top-${index}`,
                source: n.id,
                target: centerNode.id,
                type: 'straight',
                style: {
                    stroke: '#ff69b4',
                    strokeWidth: 1.5,
                    filter: 'drop-shadow(0 0 2px #ff69b4)',
                },
                markerEnd: {
                    type: MarkerType.ArrowClosed,
                    color: '#ff69b4',
                    width: 12,
                    height: 12,
                },
            });
        });

        // Добавляем связи от центрального узла к нижним
        bottomNodes.forEach((n, index) => {
            previewEdges.push({
                id: `preview-bottom-${index}`,
                source: centerNode.id,
                target: n.id,
                type: 'straight',
                style: {
                    stroke: '#00bfff',
                    strokeWidth: 1.5,
                    filter: 'drop-shadow(0 0 2px #00bfff)',
                },
                markerEnd: {
                    type: MarkerType.ArrowClosed,
                    color: '#00bfff',
                    width: 12,
                    height: 12,
                },
            });
        });

        return {
            nodes: [
                {
                    ...centerNode,
                    type: 'custom',
                    style: {
                        opacity: 1,
                        background: '#2a2a2a',
                        border: '1px solid #fff',
                        borderRadius: '4px',
                        color: '#fff',
                        fontSize: '11px',
                        fontWeight: 500,
                        padding: '4px',
                        minWidth: '70px',
                        boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)',
                        textAlign: 'center' as const,
                    },
                },
                ...topNodes,
                ...bottomNodes,
            ],
            edges: previewEdges,
        };
    }, [
        node,
        edges,
        nodes,
    ]);

    const { nodes: previewNodes, edges: previewEdges } = getRelatedElements();

    useEffect(() => {
        if (!visible) return;
        setTimeout(() => fitView({ minZoom: 0.1, maxZoom: 0.8 }), 0);
    }, [
        fitView,
        node,
        visible,
    ]);

    const handleNodeClick = useCallback(
        (event: React.MouseEvent, node: Node) => {
            event.preventDefault();
            setSelectedNodeById(node.id);
        },
        [node],
    );

    if (!node) return null;

    return (
        <ReactFlowProvider>
            <div className="node-preview">
                {previewNodes.length > 0 && (
                    <ReactFlow
                        nodes={previewNodes}
                        edges={previewEdges}
                        onNodeClick={handleNodeClick}
                        nodeTypes={nodeTypes}
                        defaultEdgeOptions={{
                            type: 'straight',
                            animated: true,
                            style: {
                                strokeWidth: 1.5,
                                stroke: '#00bfff',
                                strokeDasharray: '5,5',
                                animation: 'dashdraw 1s linear infinite',
                            },
                            markerEnd: {
                                type: MarkerType.ArrowClosed,
                                color: '#00bfff',
                                width: 10,
                                height: 10,
                            },
                        }}
                        fitView={true}
                        defaultViewport={{ x: 0, y: 0, zoom: 0.7 }}
                        minZoom={0.1}
                        maxZoom={1.5}
                        nodesDraggable={false}
                        nodesConnectable={false}
                        elementsSelectable={false}
                        proOptions={{ hideAttribution: true }}
                    >
                        <Background
                            gap={12}
                            size={1}
                            color={Colors.Black[950]}
                        />
                    </ReactFlow>
                )}
            </div>
        </ReactFlowProvider>
    );
};

export default NodePreview;
