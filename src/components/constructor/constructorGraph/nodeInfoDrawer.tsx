import { Edge, Node } from '@xyflow/react';
import {
    Drawer,
    Col,
    Row,
    InputNumber,
    Checkbox,
    Divider,
    Button,
    Slider,
    Form,
    Input,
} from 'antd';
import { TSimulation } from 'types/simulation/simulation';
import { TaskNode } from './constructorGraph';
import NodePreview from './nodePreview';
import { statColors } from '../constructorWorker/constructorWorkerList';
import { FormInstance } from 'antd/lib';

type TProps = {
    simulation: TSimulation | null;
    firstTaskId: number;
    setFirstTaskId: () => void;
    lastTaskId: number;
    setLastTaskId: () => void;
    form: FormInstance;
    selectedNode: TaskNode | null;
    deleteSelectedNode: () => void;
    setSelectedNodeById: (id: string) => Promise<void>;
    isOpen: boolean;
    onClose: () => void;
    nodes: Node[];
    edges: Edge[];
};

const NodeInfoDrawer = ({
    simulation,
    firstTaskId,
    setFirstTaskId,
    lastTaskId,
    setLastTaskId,
    selectedNode,
    form,
    deleteSelectedNode,
    setSelectedNodeById,
    isOpen,
    onClose,
    nodes,
    edges,
}: TProps): JSX.Element => {
    const disableEdit = simulation?.archived || simulation?.published || simulation?.finished;

    return (
        <Drawer
            className="task-drawer"
            getContainer={false}
            mask={false}
            maskClosable={false}
            onClose={onClose}
            open={isOpen}
            placement="right"
            title={
                <Row
                    style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                    }}
                >
                    <Col className="p2-strong">
                        {isOpen == null ? 'Закрытие...' : `ID ${selectedNode?.id}`}
                    </Col>
                    <Col>
                        <Button
                            danger
                            disabled={disableEdit}
                            onClick={() => deleteSelectedNode()}
                            type="text"
                        >
                            Удалить узел
                        </Button>
                    </Col>
                </Row>
            }
        >
            <Col>
                <Row className="scroll-row">
                    <Form
                        form={form}
                        style={{ display: 'flex', flexDirection: 'column', rowGap: '8px' }}
                    >
                        <Row>
                            <Form.Item
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Пожалуйста, введите название',
                                    },
                                    {
                                        max: 50,
                                        message: 'Не больше 50 символов',
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    disabled={disableEdit}
                                    maxLength={50}
                                    placeholder="Введите название узла"
                                    showCount
                                />
                            </Form.Item>
                        </Row>
                        <Row>
                            <Form.Item
                                name="description"
                                rules={[
                                    {
                                        max: 390,
                                        message: 'Не больше 390 символов',
                                    },
                                ]}
                            >
                                <Input.TextArea
                                    allowClear
                                    disabled={disableEdit}
                                    maxLength={390}
                                    placeholder="Введите описание"
                                    rows={3}
                                    showCount
                                />
                            </Form.Item>
                        </Row>
                        <Row>
                            <Col>
                                <div className="p3">Кол-во людей (план)</div>
                                <Form.Item
                                    name="est_workers"
                                    rules={[
                                        {
                                            type: 'integer',
                                            required: true,
                                            message: 'Пожалуйста, введите кол-во ПШЕ',
                                        },
                                    ]}
                                >
                                    <InputNumber
                                        disabled={disableEdit}
                                        max={16}
                                        min={1}
                                        suffix={<div className="duration-suffix">чел.</div>}
                                        type="number"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <div className="p3">Продолжительность (план)</div>
                                <Form.Item
                                    name="est_duration"
                                    rules={[
                                        {
                                            type: 'integer',
                                            required: true,
                                            message: 'Пожалуйста, введите кол-во дней',
                                        },
                                    ]}
                                >
                                    <InputNumber
                                        disabled={disableEdit}
                                        max={50}
                                        min={1}
                                        suffix={<div className="duration-suffix">дней</div>}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <div className="p3">Бюджет (план)</div>
                                <Form.Item
                                    name="est_budget"
                                    rules={[
                                        {
                                            type: 'integer',
                                            required: true,
                                            message: 'Пожалуйста, введите бюджет узла',
                                        },
                                    ]}
                                >
                                    <InputNumber
                                        disabled={disableEdit}
                                        max={10e5}
                                        min={10e2}
                                        step={10e2}
                                        suffix={<div className="duration-suffix">руб</div>}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row wrap={false}>
                            <Col>
                                <Checkbox
                                    disabled={disableEdit}
                                    onChange={() => setFirstTaskId()}
                                    checked={selectedNode?.data.id == firstTaskId}
                                >
                                    Первый
                                </Checkbox>
                            </Col>
                            <Col>
                                <Checkbox
                                    disabled={disableEdit}
                                    onChange={() => setLastTaskId()}
                                    checked={selectedNode?.data.id == lastTaskId}
                                >
                                    Последний
                                </Checkbox>
                            </Col>
                        </Row>
                        <Divider>Требования к ПШЕ</Divider>
                        {simulation?.skills.map((k, i) => {
                            {
                                return (
                                    <Row
                                        key={i + 1}
                                        style={{
                                            alignItems: 'center',
                                            flexWrap: 'nowrap',
                                            justifyContent: 'space-between',
                                        }}
                                    >
                                        <Col
                                            style={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                rowGap: '2px',
                                            }}
                                        >
                                            <Row>
                                                <span className="p3">{k}:</span>
                                            </Row>
                                            <Row>
                                                {/*<InputNumber
                                                    max={6}
                                                    min={0}
                                                    onChange={(v) => {
                                                        const st = selectedNode?.data;
                                                        updateSelectedNodeData({
                                                            ...st, 
                                                            stats_req: st?.stats_req.map((value, ti) => 
                                                                ti == i ? v : value
                                                            )
                                                        });
                                                    }}
                                                    placeholder="-"
                                                    required
                                                    type="number"
                                                    value={selectedNode?.data.stats_req[i]}
                                                />*/}
                                                <Form.Item
                                                    name={`stats_req_${i + 1}`}
                                                    rules={[
                                                        {
                                                            type: 'integer',
                                                            required: true,
                                                            message:
                                                                'Пожалуйста, выберите требование',
                                                        },
                                                    ]}
                                                >
                                                    <Slider
                                                        disabled={disableEdit}
                                                        marks={{
                                                            0: 0,
                                                            1: 1,
                                                            2: 2,
                                                            3: 3,
                                                            4: 4,
                                                            5: 5,
                                                        }}
                                                        max={5}
                                                        min={0}
                                                        step={1}
                                                        styles={{
                                                            track: {
                                                                background: statColors[i],
                                                            },
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Row>
                                        </Col>
                                    </Row>
                                );
                            }
                        })}
                    </Form>
                </Row>
                <Row className="fixed-row">
                    <Col flex={1}>
                        <NodePreview
                            node={selectedNode}
                            nodes={nodes}
                            edges={edges}
                            visible={isOpen}
                            setSelectedNodeById={setSelectedNodeById}
                        />
                    </Col>
                </Row>
            </Col>
        </Drawer>
    );
};

export { NodeInfoDrawer };
