import { faCircleExclamation, faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    <PERSON><PERSON>,
    Col,
    Drawer,
    Form,
    GetRef,
    Input,
    InputNumber,
    InputRef,
    Popconfirm,
    Row,
    Table,
    TableProps,
    Tooltip,
} from 'antd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { TSimulation } from 'types/simulation/simulation';
import { TSimTask } from 'types/simulation/simulationTask';
import type { InputNumberRef } from 'rc-input-number';
import Colors from '@classes/colors';

type FormInstance<T> = GetRef<typeof Form<T>>;

const EditableContext = React.createContext<FormInstance<any> | null>(null);

export interface Item extends TSimTask {
    key: string;
    failedValidation: boolean;
}

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form
            form={form}
            component={false}
        >
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    dataIndex: keyof Item;
    record: Item;
    handleSave: (record: Item) => void;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<InputRef>(null);
    const inputNumberRef = useRef<InputNumberRef>(null);
    const form = useContext(EditableContext)!;

    useEffect(() => {
        if (editing) {
            inputRef?.current?.focus();
            inputNumberRef?.current?.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: record[dataIndex] });
    };

    const save = async () => {
        try {
            const values = await form.validateFields();

            toggleEdit();
            handleSave({ ...record, ...values, failedValidation: false });
        } catch (errInfo) {
            toggleEdit();
            handleSave({ ...record, ...errInfo.values, failedValidation: true });
        }
    };

    let childNode = children;

    if (editable) {
        childNode = editing ? (
            <>
                {dataIndex == 'name' && (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            { required: true, message: `${title} обязательное поле.` },
                            { max: 50, message: 'От 1 до 50 символов' },
                        ]}
                    >
                        <Input
                            ref={inputRef}
                            maxLength={50}
                            onPressEnter={save}
                            onBlur={save}
                            required
                        />
                    </Form.Item>
                )}
                {dataIndex == 'est_budget' && (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            { required: true, message: `${title} обязательное поле.` },
                            {
                                type: 'integer',
                                min: 10e2,
                                max: 10e5,
                                message: 'От 1 тыс. до 1 млн.',
                            },
                        ]}
                    >
                        <InputNumber
                            ref={inputNumberRef}
                            max={10e5}
                            min={10e2}
                            onPressEnter={save}
                            onBlur={save}
                            required
                            step={10e2}
                            controls={false}
                        />
                    </Form.Item>
                )}
                {dataIndex == 'est_duration' && (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            { required: true, message: `${title} обязательное поле.` },
                            {
                                type: 'integer',
                                min: 1,
                                max: 50,
                                message: 'От 1 до 50',
                            },
                        ]}
                    >
                        <InputNumber
                            ref={inputNumberRef}
                            max={50}
                            min={1}
                            onPressEnter={save}
                            onBlur={save}
                            required
                            controls={false}
                        />
                    </Form.Item>
                )}
                {dataIndex == 'est_workers' && (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            { required: true, message: `${title} обязательное поле.` },
                            {
                                type: 'integer',
                                min: 1,
                                max: 5,
                                message: 'От 1 до 5',
                            },
                        ]}
                    >
                        <InputNumber
                            ref={inputNumberRef}
                            max={5}
                            min={1}
                            onPressEnter={save}
                            onBlur={save}
                            required
                            controls={false}
                        />
                    </Form.Item>
                )}
            </>
        ) : (
            <div
                className="editable-cell-value-wrap"
                style={{ paddingInlineEnd: 4 }}
                onClick={toggleEdit}
            >
                {children}
            </div>
        );
    }

    return <td {...restProps}>{childNode}</td>;
};

type ColumnTypes = Exclude<TableProps<Item>['columns'], undefined>;

type TProps = {
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    addTableTask: () => void;
    deleteTask: (id: number) => void;
    updateTableTask: (record: Item) => void;
    dataSource: Item[];
    simulation?: TSimulation;
    budgetSum: string;
};

const NodeTableDrawer = ({
    isOpen,
    setIsOpen,
    addTableTask,
    deleteTask,
    updateTableTask,
    dataSource,
    simulation,
    budgetSum,
}: TProps): JSX.Element => {
    const disableEdit = simulation?.archived || simulation?.published || simulation?.finished;

    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
        {
            title: 'ID',
            dataIndex: 'id',
            width: 40,
            render: (value, record) => (
                <Row style={{ columnGap: '4px' }}>
                    <span>{value}</span>
                    {record.failedValidation && (
                        <Tooltip
                            placement="topRight"
                            title="Данные не прошли валидацию"
                        >
                            <FontAwesomeIcon
                                className="validation-warning"
                                icon={faCircleExclamation}
                            />
                        </Tooltip>
                    )}
                </Row>
            ),
        },
        {
            title: 'Название',
            dataIndex: 'name',
            editable: !disableEdit,
            width: 140,
        },
        {
            title: 'Бюджет',
            dataIndex: 'est_budget',
            editable: !disableEdit,
            width: 80,
        },
        {
            title: 'Дни',
            dataIndex: 'est_duration',
            editable: !disableEdit,
            width: 55,
        },
        {
            title: 'ПШЕ',
            dataIndex: 'est_workers',
            editable: !disableEdit,
            width: 55,
        },
        {
            title: '',
            dataIndex: 'operation',
            width: 40,
            render: (_, record) =>
                dataSource.length >= 1 ? (
                    <Popconfirm
                        title="Удалить узел?"
                        disabled={disableEdit}
                        onConfirm={() => deleteTask(record.id)}
                        okText="Да"
                        cancelText="Нет"
                    >
                        <Button
                            danger
                            disabled={disableEdit}
                            icon={
                                <FontAwesomeIcon
                                    color={Colors.Accent.warm[0]}
                                    icon={faTrash}
                                />
                            }
                            size="small"
                        />
                    </Popconfirm>
                ) : null,
        },
    ];

    const columns = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: Item) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateTableTask,
            }),
        };
    });

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    return (
        <Drawer
            className="table-drawer"
            extra={
                <Button
                    className="table-add"
                    disabled={disableEdit}
                    icon={<FontAwesomeIcon icon={faPlus} />}
                    onClick={() => addTableTask()}
                    size="small"
                >
                    Добавить
                </Button>
            }
            getContainer={false}
            mask={false}
            onClose={() => setIsOpen(false)}
            open={isOpen}
            placement="left"
            title="Узлы"
            width={560}
        >
            <Table<Item>
                bordered
                className="node-table"
                columns={columns as ColumnTypes}
                components={components}
                dataSource={dataSource}
                footer={() => (
                    <Col>
                        <Row className="desc-l">
                            На оборудование: {simulation?.hardware_budget.toLocaleString()} руб
                        </Row>
                        <Row className="desc-l">
                            Прочие расходы: {simulation?.other_budget.toLocaleString()} руб
                        </Row>
                        <Row
                            className="desc-l-strong"
                            style={{ justifyContent: 'space-between' }}
                        >
                            <Col>{`Итого: ${budgetSum} руб`}</Col>
                            <Col>
                                {`Бюджет симуляции: ${simulation?.total_budget.toLocaleString()} руб`}
                            </Col>
                        </Row>
                    </Col>
                )}
                rowClassName={() => 'editable-row'}
                pagination={false}
                scroll={{ y: 41 * 18 - 30 }}
                size="small"
            />
        </Drawer>
    );
};

export { NodeTableDrawer };
