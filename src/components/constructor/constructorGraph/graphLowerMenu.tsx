import {
    faTable,
    faShuffle,
    faPlus,
    faSearchPlus,
    faSearchMinus,
    faExpand,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Col, Row, Tooltip } from 'antd';
import { TSimTask } from 'types/simulation/simulationTask';

type TProps = {
    addTableTask: () => void | null;
    fitView: (task_id?: TSimTask['id']) => void;
    flipTableOpen: () => void | null;
    saveChanges: () => Promise<boolean> | null;
    shuffleHorizontal: () => void | null;
    shuffleVertical: () => void | null;
    tableIsOpen: boolean;
    tasksWithValidationErrors: TSimTask['id'][];
    zoomIn: () => void;
    zoomOut: () => void;
};

const GraphLowerMenu = ({
    addTableTask,
    fitView,
    flipTableOpen,
    saveChanges,
    shuffleHorizontal,
    shuffleVertical,
    tableIsOpen,
    tasksWithValidationErrors,
    zoomIn,
    zoomOut,
}: TProps): JSX.Element => {
    return (
        <Row
            className="bottom-menu"
            justify={'center'}
        >
            <Col>
                <Row
                    className="bottom-controls p2"
                    style={{ columnGap: '8px' }}
                >
                    {saveChanges != null && tasksWithValidationErrors.length == 0 && (
                        <Button
                            onClick={() => saveChanges()}
                            type="default"
                        >
                            Сохранить
                        </Button>
                    )}
                    {tasksWithValidationErrors.length != 0 && (
                        <Tooltip
                            title={`Сохранение заблокировано: ${tasksWithValidationErrors.length} узлов требуют исправления`}
                        >
                            <Button
                                onClick={() => fitView(tasksWithValidationErrors[0])}
                                type="default"
                            >
                                {tasksWithValidationErrors.length} ошибок
                            </Button>
                        </Tooltip>
                    )}
                    {flipTableOpen != null && (
                        <Tooltip title={`${tableIsOpen ? 'Закрыть' : 'Открыть'} таблицу узлов`}>
                            <Button
                                className={tableIsOpen ? 'icon-btn-active' : 'icon-btn-default'}
                                icon={<FontAwesomeIcon icon={faTable} />}
                                onClick={() => flipTableOpen()}
                                type="default"
                            />
                        </Tooltip>
                    )}
                    <Tooltip title="Горизонтальное выравнивание дерева узлов">
                        <Button
                            className="shuffle-horizontal"
                            disabled={shuffleHorizontal == null}
                            icon={<FontAwesomeIcon icon={faShuffle} />}
                            onClick={() => shuffleHorizontal()}
                            type="default"
                        />
                    </Tooltip>
                    <Tooltip title="Вертикальное выравнивание дерева узлов">
                        <Button
                            className="shuffle-vertical"
                            disabled={shuffleVertical == null}
                            icon={<FontAwesomeIcon icon={faShuffle} />}
                            onClick={() => shuffleVertical()}
                            type="default"
                        />
                    </Tooltip>
                    {addTableTask != null && (
                        <Tooltip title="Создать узел">
                            <Button
                                className="add-node"
                                icon={<FontAwesomeIcon icon={faPlus} />}
                                onClick={() => addTableTask()}
                                type="default"
                            />
                        </Tooltip>
                    )}
                    <Tooltip title="Увеличить масштаб">
                        <Button
                            className="zoom-in"
                            icon={<FontAwesomeIcon icon={faSearchPlus} />}
                            onClick={zoomIn}
                            type="default"
                        />
                    </Tooltip>
                    <Tooltip title="Уменьшить масштаб">
                        <Button
                            className="zoom-out"
                            icon={<FontAwesomeIcon icon={faSearchMinus} />}
                            onClick={zoomOut}
                            type="default"
                        />
                    </Tooltip>
                    <Tooltip title="Центрировать">
                        <Button
                            className="fit-view"
                            icon={<FontAwesomeIcon icon={faExpand} />}
                            onClick={() => fitView()}
                            type="default"
                        />
                    </Tooltip>
                </Row>
            </Col>
        </Row>
    );
};

export { GraphLowerMenu };
