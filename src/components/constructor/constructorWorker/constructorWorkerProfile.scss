@use '/src/styles/colors';

.worker-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .worker-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 32px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .worker-image {
            background: url('/src/assets/worker_image.png');
            background-color: colors.$accentW0;
            background-repeat: no-repeat;
            background-size: contain;
            border-radius: 5px;
            height: 200px;
            margin-right: 24px;
            margin-bottom: 16px;
            width: 200px;
        }
        .img-text {
            color: colors.$neutral500;
        }
        .id-row {
            align-items: center;
            row-gap: 8px;

            h3 {
                margin: 0;
            }
        }
        .worker-form {
            display: flex;
            flex-direction: column;
            row-gap: 16px;

            .inputs-row {
                align-content: center;
                align-items: flex-start;
                column-gap: 20px;
                flex-wrap: wrap;
                justify-content: space-evenly;
                width: 100%;

                .upper-fields {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;

                    .ant-form-item {
                        width: 100%;
                    }
                }
            }
            .multi-input-row {
                column-gap: 20px;

                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: calc(50% - 10px);

                    > .ant-row {
                        align-items: center;
                        column-gap: 8px;
                        display: flex;
                        flex-direction: row;
                        flex-wrap: nowrap;

                        .labeled-input {
                            display: flex;
                            flex-direction: column;
                            row-gap: 8px;
                            width: 100%;

                            .ant-form-item:has(.ant-slider) {
                                margin-bottom: 0;
                            }
                            .ant-slider {
                                margin: 0 12px 12px 12px;
                                width: calc(100% - 20px);
                            }
                        }
                        .labeled-input .ant-input-affix-wrapper,
                        .labeled-input .ant-input-number,
                        .labeled-input .ant-input-number-affix-wrapper,
                        .ant-form-item {
                            width: 100%;
                        }
                    }
                }
            }
            .info-row {
                .labeled-input {
                    .lighter-tone {
                        color: colors.$neutral700;
                    }
                }
            }
            .controls-row {
                column-gap: 32px;
                row-gap: 8px;

                .ant-form-item {
                    margin-bottom: 0;
                }
                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;

                    &:disabled {
                        background: colors.$neutral25;
                        color: colors.$neutral300;
                    }
                    &:not(:disabled):hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
        }
        .ant-input-affix-wrapper-disabled,
        textarea.ant-input-disabled,
        .ant-input-number-disabled {
            background-color: colors.$neutral10;
            color: colors.$neutral800;
        }
    }
}
@media screen and (orientation: portrait) {
    .worker-profile .worker-card {
        padding: 16px;

        .inputs-row {
            row-gap: 32px;
            width: 100%;

            > .ant-col {
                width: 100% !important;
            }
        }
        .multi-input-row {
            row-gap: 32px;
            width: 100%;

            > .ant-col {
                width: 100% !important;
            }
        }
    }
}
