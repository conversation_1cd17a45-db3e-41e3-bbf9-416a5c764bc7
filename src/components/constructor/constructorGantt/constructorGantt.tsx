import { TSimulation } from 'types/simulation/simulation';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { useReactive } from 'ahooks';
import { useNavigate, useParams } from 'react-router-dom';
import { Col, message, Row } from 'antd';
import { CRMAPIManager } from '@api/crmApiManager';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { useEffect } from 'react';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { Gantt, GanttUseCases, TSimTaskGanttExtend } from '@components/gantt/gantt';
import { Loader } from '@components/ui/loader/loader';
import { Item } from '@components/gantt/ganttTable';
import { DefaultTimeSettings } from '@store/ingame/data';
import { Permissions } from '@classes/permissions';

import '@xyflow/react/dist/style.css';
import './constructorGantt.scss';

type TState = {
    isLoading: boolean;
    simulation: TSimulation | null;
    tasks: TSimTaskGanttExtend[];
    workers: TSimWorker[];
};

const ConstructorGantt = () => {
    const state = useReactive<TState>({
        isLoading: false,
        simulation: null,
        tasks: [],
        workers: [],
    });
    const { simId } = useParams();
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();

    function checkPermission() {
        return (
            Permissions.checkPermission(Permissions.SimulationGet) &&
            Permissions.checkPermission(Permissions.SimulationTaskList) &&
            Permissions.checkPermission(Permissions.SimulationWorkerList)
        );
    }

    async function loadParentSim() {
        if (!checkPermission()) {
            navigate('/lk');
            message.error('Недостаточно прав для работы на этой странице');
            return;
        }
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            if (sim.errorMessages) throw sim.errorMessages;
            state.simulation = sim.data.data;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при загрузке симуляции!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadTaskList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.getAll<SimTaskListResp>(async (api, page, per_page) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: page,
                    per_page: per_page,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = tl.data.data
                .sort((a, b) => a.id - b.id)
                .map((ti) => {
                    return { ...ti, curDuration: ti.est_duration, workers: [] };
                });
        } catch (errors) {
            messageApi.error('Ошибка при загрузке узлов!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadWorkerList() {
        state.isLoading = true;
        try {
            const wl = await CRMAPIManager.getAll<SimWorkerListResp>(
                async (api, page, per_page) => {
                    return await api.getSimWorkerList({
                        simulation_id: +simId,
                        page: page,
                        per_page: per_page,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: 'null',
                        },
                    });
                },
            );
            if (wl.errorMessages) throw wl.errorMessages;
            state.workers = wl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке ПШЕ!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        loadParentSim().then(() => {
            if (state.simulation == null) return;
            loadTaskList().then(() => {
                loadWorkerList();
            });
        });
    }, []);

    function assignWorkerToSelTask(task_uid: TSimTask['uid'], worker_uid: TSimWorker['uid']) {
        const selectedTask = state.tasks.find((ti) => ti.uid == task_uid);
        if (selectedTask == undefined) return;
        const selectedWorker = state.workers.find((wi) => wi.uid == worker_uid);
        // Обновляем набор задач
        state.tasks = state.tasks.map((tti) => {
            if (tti.id == selectedTask.id) {
                const tempWorkers = tti.workers.concat(selectedWorker);
                const updTask = {
                    ...tti,
                    // TODO (aefl821): вынести отдельно и сделать формулу ближе к прохождению
                    curDuration:
                        tempWorkers.length > 0
                            ? Math.ceil((tti.est_workers * tti.est_duration) / tempWorkers.length)
                            : tti.est_duration,
                    workers: tempWorkers,
                };
                return updTask;
            } else {
                return tti;
            }
        });
    }

    function unassignWorkerFromSelTask(task_uid: TSimTask['uid'], worker_uid: TSimWorker['uid']) {
        const selectedTask = state.tasks.find((ti) => ti.uid == task_uid);
        if (selectedTask == undefined) return;
        // Обновляем набор задач
        state.tasks = state.tasks.map((tti) => {
            if (tti.id == selectedTask.id) {
                const tempWorkers = tti.workers.filter((ttiw) => ttiw.uid != worker_uid);
                const updTask = {
                    ...tti,
                    // TODO (aefl821): вынести отдельно и сделать формулу ближе к прохождению
                    curDuration:
                        tempWorkers.length > 0
                            ? Math.ceil((tti.est_workers * tti.est_duration) / tempWorkers.length)
                            : tti.est_duration,
                    workers: tempWorkers,
                };
                return updTask;
            } else {
                return tti;
            }
        });
    }

    function updTableTask(rec: Item) {
        const tempTask = state.tasks.find((tti) => tti.id == rec.id);
        if (tempTask == undefined) return;
        // Обновляем задачу по таблице
        state.tasks = state.tasks.map((tti) => {
            if (tti.id == rec.id) {
                return {
                    ...tti,
                    // TODO (aefl821): вынести отдельно и сделать формулу ближе к прохождению
                    curDuration:
                        tti.workers.length > 0
                            ? Math.ceil((tti.est_workers * rec.estDuration) / tti.workers.length)
                            : rec.estDuration,
                    est_budget: rec.estBudget,
                    est_duration: rec.estDuration,
                };
            } else {
                return tti;
            }
        });
    }

    function onTaskOpen(task_uid: TSimTask['uid']) {
        const task = state.tasks.find((ti) => ti.uid == task_uid);
        if (task == undefined) {
            messageApi.error('Не удалось найти задачу для перехода');
            return;
        }
        navigate(`/constructor/${simId}/nodes/${task.id}`);
    }

    return (
        <UniLayout
            activeTab="gantt"
            additionalClass="gantt-min-width"
            tabSet="constructor"
            paddingNone={true}
        >
            {contextHolder}
            {state.isLoading && <Loader />}
            {state.simulation != null &&
                state.simulation.first_task != null &&
                state.simulation.last_task != null &&
                state.tasks.length > 0 &&
                state.workers.length > 0 && (
                    <Gantt
                        className="constructor"
                        daysInAWeek={DefaultTimeSettings.daysInAWeek}
                        onTableEdit={updTableTask}
                        onTaskOpen={onTaskOpen}
                        onWorkerAssign={assignWorkerToSelTask}
                        onWorkerUnassign={unassignWorkerFromSelTask}
                        simulation={state.simulation}
                        SWAlinks={null}
                        tasks={state.tasks}
                        useCase={GanttUseCases.Constructor}
                        workers={state.workers}
                    />
                )}
            {state.simulation != null &&
                (state.simulation.first_task == null || state.simulation.last_task == null) && (
                    <Col
                        flex={1}
                        style={{ width: '100%' }}
                    >
                        <Row
                            style={{
                                alignItems: 'center',
                                height: '100%',
                                justifyContent: 'center',
                            }}
                        >
                            <span className="p2-strong">
                                Задайте симуляции первую и последнюю задачу
                            </span>
                        </Row>
                    </Col>
                )}
            {state.simulation != null && state.workers.length == 0 && (
                <Col
                    flex={1}
                    style={{ width: '100%' }}
                >
                    <Row
                        style={{
                            alignItems: 'center',
                            height: '100%',
                            justifyContent: 'center',
                        }}
                    >
                        <span className="p2-strong">Добавьте симуляции ПШЕ</span>
                    </Row>
                </Col>
            )}
        </UniLayout>
    );
};

export default ConstructorGantt;
