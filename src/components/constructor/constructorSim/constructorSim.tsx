import { CRMAPIManager } from '@api/crmApiManager';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimulation } from 'types/simulation/simulation';
import {
    Row,
    Col,
    Input,
    Button,
    message,
    InputNumber,
    Popconfirm,
    Tooltip,
    Form,
    Dropdown,
} from 'antd';
import { rootStore } from '@store/instanse';
import { TUser } from 'types/user/user';
import { SettingsManager } from '@classes/settingsManager';
import { UserResp } from '@api/responseModels/users/userResp';
import { Common } from '@classes/common';
import { Permissions } from '@classes/permissions';
import { observer } from 'mobx-react';
import { TSimTask } from 'types/simulation/simulationTask';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { TaskUtils } from '@classes/taskUtitlty';
import { DefaultTimeSettings } from '@store/ingame/data';
import { GanttUseCases } from '@components/gantt/gantt';
import { Loader } from '@components/ui/loader/loader';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { TFilter } from 'types/filter';
import { TMetadata } from 'types/api/metadata';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterResp } from '@api/responseModels/filters/filterResponse';
import { FilterDrawer } from '@components/drawers/filterDrawer';
import _ from 'lodash';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';
import { makeSimulationDuplicate, makeSimulationTemplate } from './data';
import { SimScheduleEventListResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventListResponse';
import { SimScheduleEventResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventResponse';
import Colors from '@classes/colors';

import './constructorSim.scss';

type TState = {
    creator: TUser | null;
    filterPickerOpen: boolean;
    filters: TFilter[];
    filtersMeta: TMetadata | null;
    isLoading: boolean;
    lastTaskEndDay: number;
    filterSearchValue: string;
    minTotalBudget: number;
    minWeeks: number;
    originalSim: TSimulation | null;
    simulation: TSimulation | null;
    skipPreventLeaving: boolean;
    tasks: TSimTask[];
    useCase: 'create' | 'edit';
};

const ConstructorSim = observer((): JSX.Element => {
    const state = useReactive<TState>({
        creator: null,
        filterPickerOpen: false,
        filters: [],
        filtersMeta: null,
        isLoading: false,
        lastTaskEndDay: -1,
        filterSearchValue: '',
        minTotalBudget: 10000,
        minWeeks: 1,
        originalSim: null,
        simulation: null,
        skipPreventLeaving: false,
        tasks: [],
        useCase: 'create',
    });
    const [form] = Form.useForm();
    const { simId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;
    const anyChanges = !_.isEqual(state.simulation, state.originalSim);

    function updateSim(s: Partial<TSimulation>, notFromForm: boolean = false) {
        if (notFromForm) {
            state.simulation = { ...state.simulation, ...s };
        } else {
            state.simulation = { ...form.getFieldsValue(true) };
        }
    }

    function updateStateSim(s: TSimulation) {
        state.simulation = {
            ...s,
            filters: s.filters.map((uf) => {
                return { ...uf, target: 'simulations' };
            }),
        };
        state.originalSim = { ...state.simulation };

        form.setFieldsValue(state.simulation);
    }

    async function loadExistingSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            updateStateSim(sim.data.data);
            if (sim.data.data?.creator == SettingsManager.getConnectionCredentials()?.user_id) {
                state.creator = rootStore.currentUserStore.getUser;
            } else {
                const loadCreator = await CRMAPIManager.request<UserResp>(async (api) => {
                    return await api.getUser(sim.data.data?.creator);
                });
                if (loadCreator.errorMessages) throw loadCreator.errorMessages;
                state.creator = loadCreator.data.data;
            }
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при загрузке симуляции или её создателя');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadTaskList() {
        state.isLoading = true;
        try {
            const tl = await CRMAPIManager.getAll<SimTaskListResp>(async (api, page, per_page) => {
                return await api.getSimTaskList({
                    simulation_id: +simId,
                    page: page,
                    per_page: per_page,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (tl.errorMessages) throw tl.errorMessages;
            state.tasks = tl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка :(');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.getAll<FilterListResp>(
                async (api, page, per_page) => {
                    return await api.getFilterList({
                        query: null,
                        page: page,
                        per_page: per_page,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: 'null',
                        },
                    });
                },
            );
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.filtersMeta = filters.data.meta;
            updateStateSim({
                ...state.simulation,
                filters: state.simulation.filters.map((uf) => {
                    return { ...uf, target: 'simulations' };
                }),
            });
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
        }
        state.isLoading = false;
    }

    function checkPermission() {
        if (
            !(
                Permissions.checkPermission(Permissions.SimulationGet) &&
                Permissions.checkPermission(Permissions.SimulationCreate) &&
                Permissions.checkPermission(Permissions.SimulationUpdate) &&
                Permissions.checkPermission(Permissions.SimulationRestore) &&
                Permissions.checkPermission(Permissions.SimulationDelete) &&
                Permissions.checkPermission(Permissions.SimulationTest)
            )
        ) {
            message.error('Недостаточно прав для работы над симуляцией');
            navigate('/lk');
            return false;
        }
        return true;
    }

    async function initConSim() {
        if (!checkPermission()) return;
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        if (simId == undefined) {
            state.useCase = 'create';
            updateStateSim(makeSimulationTemplate());
            state.creator = rootStore.currentUserStore.getUser;
        } else {
            state.useCase = 'edit';
            await loadExistingSim();
            if (state.simulation == null) return;
            await loadTaskList();
            state.minTotalBudget = calcMinBudget();
            state.minWeeks = calcMinWeeks();
        }
        await loadFilters();
    }

    useEffect(() => {
        state.skipPreventLeaving = false;
        initConSim();
    }, [simId]);

    async function saveSim() {
        state.isLoading = true;
        let returnValue = false;
        try {
            const result = await CRMAPIManager.request<SimulationResp>(async (api) => {
                if (state.useCase == 'create') {
                    state.skipPreventLeaving = true;
                    return api.createSimulation(state.simulation);
                } else {
                    return api.updateSimulation(state.simulation);
                }
            });
            if (result.errorMessages) throw result.errorMessages;
            if (state.useCase == 'create') {
                navigate(`/constructor/${result.data.data.id}`);
                message.success('Симуляция создана');
            } else {
                updateStateSim(result.data.data);
                message.success('Изменения сохранены');
            }
            returnValue = true;
        } catch (errors) {
            state.skipPreventLeaving = false;
            messageApi.error(
                `Ошибка при ${state.useCase == 'create' ? 'создании' : 'сохранении'} симуляции`,
            );
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    function cancelCreation() {
        navigate('/simulations');
    }

    async function finishSim() {
        updateSim({ tested: true, finished: true }, true);
        await saveSim();
    }

    async function unfinishSim() {
        updateSim({ tested: true, finished: false }, true);
        await saveSim();
    }

    async function publishSim() {
        updateSim({ published: true }, true);
        await saveSim();
    }

    async function archivateSim() {
        updateSim({ archived: true }, true);
        await saveSim();
    }

    async function unarchivateSim() {
        updateSim({ archived: false }, true);
        await saveSim();
    }

    async function deleteSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return api.removeSimulation(state.simulation?.id);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            navigate(`/simulations/${sim.data.data.id}`);
            message.success('Симуляция была удалена');
        } catch (errors) {
            messageApi.error('Ошибка при удалении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return api.restoreSimulation(state.simulation?.id);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            updateStateSim(sim.data.data);
            messageApi.success('Симуляция была восстановлена');
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function getCreatorName() {
        return state.creator?.name == null || state.creator?.name.length == 0
            ? '-'
            : state.creator?.name;
    }

    function calcMinBudget() {
        let minBudget = 0;
        const formHardwareBudget = form.getFieldValue('hardware_budget');
        if (!Common.isNullOrUndefined(formHardwareBudget)) {
            minBudget += formHardwareBudget;
        }
        const formOtherBudget = form.getFieldValue('other_budget');
        if (!Common.isNullOrUndefined(formOtherBudget)) {
            minBudget += formOtherBudget;
        }
        if (state.tasks.length == 0) {
            minBudget += 10000;
        } else {
            minBudget += state.tasks.reduce((acc, task) => acc + task.est_budget, 0);
        }
        return minBudget;
    }

    function calcMinWeeks() {
        if (state.tasks.length == 0) {
            return 1;
        } else {
            if (state.simulation?.first_task == null || state.simulation?.last_task == null) {
                state.lastTaskEndDay = -1;
                return 1;
            } else {
                state.lastTaskEndDay = TaskUtils.totalLengthOfTaskTree({
                    hoursInADay:
                        DefaultTimeSettings.workDayHours - +DefaultTimeSettings.workDayLunchSkip,
                    simulation: state.simulation,
                    tasks: state.tasks
                        .sort((a, b) => a.id - b.id)
                        .map((ti) => {
                            return { ...ti, curDuration: ti.est_duration, workers: [] };
                        }),
                    useCase: GanttUseCases.Constructor,
                });
                return Math.ceil((state.lastTaskEndDay + 1) / DefaultTimeSettings.daysInAWeek) + 1;
            }
        }
    }

    function genLastTaskString() {
        if (state.lastTaskEndDay == -1) {
            if (state.simulation?.first_task == null && state.simulation?.last_task == null) {
                return 'Не заданы первая и последняя задачи';
            }
            if (state.simulation?.first_task == null && state.simulation?.last_task != null) {
                return 'Не задана первая задача';
            }
            if (state.simulation?.first_task != null && state.simulation?.last_task == null) {
                return 'Не задана последняя задача';
            }
            return 'Мин. 1 нед.';
        } else {
            const tv = state.lastTaskEndDay;
            const week = Math.floor(tv / 5);
            const day = tv - week * 5;
            return `Задачи кончаются ${week + 1}н ${day + 1}д`;
        }
    }

    async function onFilterAdd(filter: TFilter) {
        if (!Permissions.checkPermission(Permissions.FilterCreate)) {
            messageApi.error('Запрещено создание фильтров');
            return;
        }
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<FilterResp>(async (api) => {
                return await api.createFilter(filter);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadFilters();
        } catch (errors) {
            messageApi.error('Ошибка при создании фильтра');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updateSimTempFilters(filter: TFilter) {
        if (state.simulation?.filters.find((f) => f.id == filter.id)) {
            state.simulation.filters = state.simulation.filters.filter((f) => f.id != filter.id);
        } else {
            state.simulation.filters = [...state.simulation.filters, filter];
        }
    }

    useEffect(() => {
        state.minTotalBudget = calcMinBudget();
    }, [state.simulation?.other_budget, state.simulation?.hardware_budget]);

    async function duplicateWholeSim() {
        state.isLoading = true;
        const newSimId = await makeSimulationDuplicate(simId);
        if (newSimId != null) {
            state.skipPreventLeaving = false;
            navigate(`/constructor/${newSimId}`);
            message.success('Дубликат симуляции создан');
        } else {
            message.error('Ошибка при создании дубликата');
        }
        state.isLoading = false;
    }

    return (
        <UniLayout
            activeTab={state.useCase == 'create' ? 'new-simulation' : 'simulation'}
            additionalClass="profile-min-width"
            tabSet={state.useCase == 'create' ? 'constructor-new' : 'constructor'}
            showSearch={false}
        >
            <div className="sim-profile">
                {state.isLoading && <Loader />}
                {contextHolder}
                <PreventLeaving
                    anyChanges={anyChanges && !state.skipPreventLeaving}
                    onSave={saveSim}
                />
                {state.filtersMeta != null && (
                    <FilterDrawer
                        filterList={state.filters.filter((f) => !f.is_protected)}
                        filterListMeta={state.filtersMeta}
                        fixedTarget="simulations"
                        isOpen={state.filterPickerOpen}
                        onFilterAdd={
                            Permissions.checkPermission(Permissions.FilterCreate)
                                ? onFilterAdd
                                : undefined
                        }
                        onSelect={updateSimTempFilters}
                        selected={state.simulation?.filters.map((f) => f.id)}
                        setIsOpen={(isOpen) => (state.filterPickerOpen = isOpen)}
                    />
                )}
                <Col
                    flex={1}
                    className="sim-card"
                >
                    <Row className="id-row">
                        <Col>
                            <Row>
                                <h3>
                                    ID {state.useCase == 'create' ? 'НОВАЯ' : state.simulation?.id}
                                </h3>
                                {state.useCase != 'create' && (
                                    <CopyButton
                                        textToCopy={`ID симуляции: ${state.simulation?.id}`}
                                        textToShow="ID симуляции скопирован"
                                        size={36}
                                    />
                                )}
                            </Row>
                        </Col>
                        <Col className="filters">
                            <Row>
                                {state.simulation?.filters.slice(0, 5).map((f) => {
                                    return (
                                        <FilterButton
                                            key={`filter-${state.simulation?.id}-${f.id}`}
                                            hex={f.color_hex}
                                            text={f.name}
                                        />
                                    );
                                })}
                                {state.simulation?.filters.length > 5 && (
                                    <Dropdown
                                        menu={{
                                            items: state.simulation?.filters
                                                .slice(5, state.simulation?.filters.length)
                                                .map((f) => {
                                                    return {
                                                        key: f.id,
                                                        label: (
                                                            <FilterButton
                                                                key={`filter-${state.simulation?.id}-${f.id}`}
                                                                hex={f.color_hex}
                                                                text={f.name}
                                                            />
                                                        ),
                                                    };
                                                }),
                                        }}
                                        trigger={['hover', 'click']}
                                    >
                                        <Row>
                                            <FilterButton
                                                key="more-filters"
                                                hex={Colors.Neutral[50]}
                                                text={`+${state.simulation?.filters.length - 5}`}
                                            />
                                        </Row>
                                    </Dropdown>
                                )}
                                {state.simulation?.filters.length == 0 && (
                                    <span className="p3">Нет фильтров</span>
                                )}
                                {!disableEdit && (
                                    <Button
                                        className="filter-change-btn"
                                        disabled={state.isLoading}
                                        icon={<div className="plus-icon" />}
                                        onClick={() => {
                                            if (!state.isLoading) {
                                                state.filterPickerOpen = true;
                                            }
                                        }}
                                    />
                                )}
                            </Row>
                        </Col>
                    </Row>
                    <Form
                        className="sim-form"
                        form={form}
                        onFinish={saveSim}
                        onValuesChange={(cv) => updateSim(cv)}
                    >
                        <Row className="inputs-row">
                            <Form.Item
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Пожалуйста, введите название',
                                    },
                                    {
                                        max: 36,
                                        message: 'Не больше 36 символов',
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    disabled={disableEdit}
                                    maxLength={36}
                                    placeholder="Введите название"
                                    showCount
                                />
                            </Form.Item>
                        </Row>
                        <Row className="inputs-row">
                            <Form.Item
                                name="description"
                                rules={[
                                    {
                                        max: 900,
                                        message: 'Не больше 900 символов',
                                    },
                                ]}
                            >
                                <Input.TextArea
                                    allowClear
                                    disabled={disableEdit}
                                    maxLength={900}
                                    placeholder="Введите описание"
                                    rows={6}
                                    showCount
                                />
                            </Form.Item>
                        </Row>
                        <Row className="multi-input-row">
                            <Col>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Категория:</span>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="category"
                                                rules={[
                                                    {
                                                        max: 36,
                                                        message: 'Не больше 36 символов',
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    allowClear
                                                    disabled={disableEdit}
                                                    maxLength={36}
                                                    placeholder="Категория"
                                                    showCount
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                                <Row>
                                    <span className="p3">Названия характеристик:</span>
                                </Row>
                                {state.simulation?.skills.map((_, i) => (
                                    <Row
                                        className="sks-row"
                                        key={`sks-${i}`}
                                    >
                                        <Col>
                                            <span className="p3">{i + 1}.</span>
                                        </Col>
                                        <Col flex={1}>
                                            <Form.Item
                                                name={['skills', i]}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: 'Введите название характеристики',
                                                    },
                                                    {
                                                        max: 20,
                                                        message: 'Не больше 20 символов',
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    allowClear
                                                    disabled={disableEdit}
                                                    maxLength={20}
                                                    placeholder="Введите название"
                                                    showCount
                                                />
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                ))}
                            </Col>
                            <Col>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row className="split-label">
                                            <Tooltip title="Ограничение количества календарных недель симуляции">
                                                <span className="p3">Календарных недель:</span>
                                            </Tooltip>
                                            <Tooltip title="Плановое завершение последней задачи">
                                                <span className="p3">{genLastTaskString()}</span>
                                            </Tooltip>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="weeks"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Пожалуйста, введите плановое кол-во недель',
                                                    },
                                                    {
                                                        type: 'integer',
                                                        max: 50,
                                                        min: state.minWeeks,
                                                        message: `От ${state.minWeeks} до 50 недель`,
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={disableEdit}
                                                    max={50}
                                                    min={state.minWeeks}
                                                    step={1}
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row className="split-label">
                                            <Tooltip title="Ограничение общего бюджета симуляции - плановые бюджеты узлов, бюджет на оборудование и прочие">
                                                <span className="p3">Общий бюджет:</span>
                                            </Tooltip>
                                            <Tooltip title="Сумма плановых бюджетов узлов, бюджета на оборудование и прочие">
                                                <span className="p3">
                                                    Мин. {state.minTotalBudget / 1000} тыс.
                                                </span>
                                            </Tooltip>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="total_budget"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Пожалуйста, введите плановый бюджет',
                                                    },
                                                    {
                                                        type: 'integer',
                                                        max: 2000000,
                                                        min: state.minTotalBudget,
                                                        message: `От ${
                                                            state.minTotalBudget / 1000
                                                        } тыс. до 2 млн.`,
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={disableEdit}
                                                    max={2000000}
                                                    min={state.minTotalBudget}
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <Tooltip title="Используется в сценарных событиях">
                                                <span className="p3">Бюджет на оборудование:</span>
                                            </Tooltip>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="hardware_budget"
                                                rules={[
                                                    {
                                                        type: 'integer',
                                                        max: 300000,
                                                        min: 0,
                                                        message: 'От 0 до 300 тыс.',
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={disableEdit}
                                                    max={300000}
                                                    min={0}
                                                    step={1000}
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <Tooltip title="Используется в сценарных событиях">
                                                <span className="p3">
                                                    Бюджет на прочие расходы:
                                                </span>
                                            </Tooltip>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="other_budget"
                                                rules={[
                                                    {
                                                        type: 'integer',
                                                        max: 100000,
                                                        min: 0,
                                                        message: 'От 0 до 100 тыс.',
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={disableEdit}
                                                    max={100000}
                                                    min={0}
                                                    step={1000}
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                        <Row className="info-row">
                            <Col>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Создатель:</span>
                                        </Row>
                                        <Row
                                            className="creator-row"
                                            onClick={() => {
                                                navigate(`/management/users/${state.creator?.id}`);
                                            }}
                                        >
                                            <Col className="creator-avatar">
                                                <div className="p1-strong">
                                                    {getCreatorName()[0]}
                                                </div>
                                            </Col>
                                            <Col className="p3 creator-name">
                                                <Button
                                                    className="creator-btn"
                                                    type="link"
                                                >
                                                    {getCreatorName()}
                                                </Button>
                                            </Col>
                                        </Row>
                                    </Col>
                                </Row>
                            </Col>
                            <Col>
                                {state.useCase != 'create' && (
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Была создана:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {state.useCase == 'edit'
                                                        ? Common.formatDateString(
                                                              state.simulation?.created_at,
                                                          )
                                                        : '-'}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                )}
                                {state.useCase != 'create' && (
                                    <Row>
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Последнее изменение:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {state.useCase == 'edit'
                                                        ? Common.formatDateString(
                                                              state.simulation?.updated_at,
                                                          )
                                                        : '-'}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                )}
                            </Col>
                        </Row>
                        {state.useCase == 'create' ? (
                            <Row className="controls-row p2">
                                <Form.Item>
                                    <Button
                                        disabled={state.isLoading}
                                        htmlType="submit"
                                    >
                                        Сохранить
                                    </Button>
                                </Form.Item>
                                <Button onClick={cancelCreation}>Отмена</Button>
                            </Row>
                        ) : (
                            <Row className="controls-row p2">
                                {anyChanges && (
                                    <Form.Item>
                                        <Button
                                            disabled={state.isLoading}
                                            htmlType="submit"
                                        >
                                            Сохранить
                                        </Button>
                                    </Form.Item>
                                )}
                                {anyChanges && (
                                    <Popconfirm
                                        cancelText="Отмена"
                                        okText="Подтвердить"
                                        onConfirm={cancelCreation}
                                        title="Не сохранённые изменения будут потеряны"
                                    >
                                        <Button>Отмена</Button>
                                    </Popconfirm>
                                )}
                                {!state.simulation?.finished && !state.simulation?.archived && (
                                    <Tooltip title="Пометка симуляции как готовой, вы сможете вернуть её в разработку">
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={finishSim}
                                        >
                                            Готова
                                        </Button>
                                    </Tooltip>
                                )}
                                {state.simulation?.finished && !state.simulation?.published && (
                                    <Tooltip title="Вернуть симуляцию в разработку">
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={unfinishSim}
                                        >
                                            Не готова
                                        </Button>
                                    </Tooltip>
                                )}
                                {state.simulation?.finished && !state.simulation?.published && (
                                    <Tooltip title="Разрешить создавать сессии с этой симуляцией; редактирование станет невозможно">
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={publishSim}
                                        >
                                            Опубликовать
                                        </Button>
                                    </Tooltip>
                                )}
                                {!state.simulation?.archived ? (
                                    <Tooltip title="Скрыть из выдачи до лучших времён">
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={archivateSim}
                                        >
                                            В архив
                                        </Button>
                                    </Tooltip>
                                ) : (
                                    <Tooltip title="Вернуть в общий список">
                                        <Button
                                            disabled={state.isLoading}
                                            onClick={unarchivateSim}
                                        >
                                            Из архива
                                        </Button>
                                    </Tooltip>
                                )}
                                {state.simulation?.deleted_at == null &&
                                    !state.simulation?.published && (
                                        <Popconfirm
                                            cancelText="Отмена"
                                            okText="Подтвердить"
                                            onConfirm={deleteSim}
                                            title="Будут удалены и остальные части симуляции"
                                        >
                                            <Button>Удалить</Button>
                                        </Popconfirm>
                                    )}
                                {state.simulation?.deleted_at != null && (
                                    <Button
                                        disabled={state.isLoading}
                                        onClick={restoreSim}
                                    >
                                        Восстановить
                                    </Button>
                                )}
                                {state.simulation?.deleted_at == null && (
                                    <Button
                                        disabled={state.isLoading}
                                        onClick={duplicateWholeSim}
                                    >
                                        Дублировать
                                    </Button>
                                )}
                            </Row>
                        )}
                    </Form>
                </Col>
            </div>
        </UniLayout>
    );
});

export default ConstructorSim;
