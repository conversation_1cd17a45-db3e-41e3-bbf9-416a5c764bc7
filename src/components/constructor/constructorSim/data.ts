import { CRMAPIManager } from '@api/crmApiManager';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { SimScheduleEventListResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventListResponse';
import { SimScheduleEventResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventResponse';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { Common } from '@classes/common';
import { rootStore } from '@store/instanse';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';

export const makeSimulationTemplate = () => {
    return {
        id: null,
        name: '',
        description: '',
        creator: rootStore.currentUserStore.getUser?.id,
        category: 'Тестовая',
        filters: [],
        skills: [
            'Веб-дизайн',
            'Базы данных',
            'Программирование',
            'Бизнес',
            'Продажи + маркетинг',
        ],
        first_task: null,
        last_task: null,
        hardware_budget: 20000,
        other_budget: 5000,
        total_budget: 1000000,
        weeks: 12,
        finished: false,
        tested: false,
        published: false,
        template: false,
        archived: false,
        deleted_at: null,
        created_at: Common.dateNowString(),
        updated_at: Common.dateNowString(),
    };
};

export const makeSimulationDuplicate = async (simId: string): Promise<number | null> => {
    try {
        const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
            return await api.getSimulation(+simId);
        });
        if (sim.errorMessages) throw sim.errorMessages;
        const newSim = await CRMAPIManager.request<SimulationResp>(async (api) => {
            return await api.createSimulation({
                ...sim.data.data,
                archived: false,
                deleted_at: null,
                finished: false,
                published: false,
                template: false,
                tested: false,
                filters: [
                    {
                        colorHEX: '#cdf6ff',
                        id: 13,
                        is_protected: true,
                        name: 'В разработке',
                        target: 'simulations',
                    },
                    ...sim.data.data.filters.filter((f) => !f.is_protected),
                ],
            });
        });
        if (newSim.errorMessages) throw newSim.errorMessages;

        const tasks = await CRMAPIManager.getAll<SimTaskListResp>(async (api, page, per_page) => {
            return await api.getSimTaskList({
                simulation_id: +simId,
                page: page,
                per_page: per_page,
                sort_by: null,
                sort_direction: null,
                filters: {
                    deleted: 'all',
                },
            });
        });
        if (tasks.errorMessages) throw tasks.errorMessages;
        const taskBulkBody = tasks.data.data.map((t, i) => {
            return {
                action: 'add' as const,
                index: i,
                value: { ...t, simulation_id: newSim.data.data.id },
            };
        });
        const newTasks = await CRMAPIManager.bulkRequest<TSimTask>(
            taskBulkBody,
            async (api, _bulkBody) => {
                return await api.bulkSimTask(_bulkBody);
            },
            async (api, task_id) => {
                return await api.bulkResultSimTask(task_id);
            },
        );
        if (newTasks.errorMessages) throw newTasks.errorMessages;

        const workers = await CRMAPIManager.getAll<SimWorkerListResp>(
            async (api, page, per_page) => {
                return await api.getSimWorkerList({
                    simulation_id: +simId,
                    page: page,
                    per_page: per_page,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            },
        );
        if (workers.errorMessages) throw tasks.errorMessages;
        const workerBulkBody = workers.data.data.map((t, i) => {
            return {
                action: 'add' as const,
                index: i,
                value: { ...t, simulation_id: newSim.data.data.id },
            };
        });
        const newWorkers = await CRMAPIManager.bulkRequest<TSimWorker>(
            workerBulkBody,
            async (api, _bulkBody) => {
                return await api.bulkSimWorker(_bulkBody);
            },
            async (api, task_id) => {
                return await api.bulkResultSimWorker(task_id);
            },
        );
        if (newWorkers.errorMessages) throw newWorkers.errorMessages;

        const events = await CRMAPIManager.getAll<SimEventListResp>(async (api, page, per_page) => {
            return await api.getSimEventList({
                simulation_id: +simId,
                query: '',
                page: page,
                per_page: per_page,
                sort_by: 'created_at',
                sort_direction: 'desc',
                filters: {
                    deleted: 'null',
                },
            });
        });
        if (events.errorMessages) throw events.errorMessages;
        for (let i = 0; i < events.data.data.length; i++) {
            const newEvent = await CRMAPIManager.request<SimEventResp>(async (api) => {
                return await api.createSimEvent({
                    ...events.data.data[i],
                    simulation_id: +newSim.data.data.id,
                });
            });
            if (newEvent.errorMessages) throw newEvent.errorMessages;
        }

        try {
            const scheduleEvents = await CRMAPIManager.request<SimScheduleEventListResp>(
                async (api) => {
                    return await api.getSimScheduleEventList({
                        simulation_id: +simId,
                        week: null,
                    });
                },
            );
            if (scheduleEvents.errorMessages) throw scheduleEvents.errorMessages;
            for (let i = 0; i < scheduleEvents.data.data.length; i++) {
                const newScheduleEvent = await CRMAPIManager.request<SimScheduleEventResp>(
                    async (api) => {
                        return await api.createSimScheduleEvent({
                            ...scheduleEvents.data.data[i],
                            simulation_id: +newSim.data.data.id,
                        });
                    },
                );
                if (newScheduleEvent.errorMessages) throw newScheduleEvent.errorMessages;
            }
        } catch (err) {
            if (!err?.includes('Объект не найден')) {
                throw err;
            }
        }
        const updSim = await CRMAPIManager.request<SimulationResp>(async (api) => {
            return await api.updateSimulation({
                ...newSim.data.data,
                first_task: sim.data.data.first_task,
                last_task: sim.data.data.last_task,
            });
        });
        if (updSim.errorMessages) throw updSim.errorMessages;
        return updSim.data.data.id;
    } catch (err) {
        console.log(err);
        return null;
    }
};
