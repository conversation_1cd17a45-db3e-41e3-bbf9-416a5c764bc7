@use '../../../styles/colors';
@use '../../../styles/icons';

.sim-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .sim-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 32px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .id-row {
            align-items: center;
            column-gap: 20px;
            justify-content: space-between;
            row-gap: 8px;

            h3 {
                margin: 0;
            }
            .filters > .ant-row {
                align-items: center;
                column-gap: 16px;
                row-gap: 8px;

                .filter-change-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 32px;
                    width: 32px;

                    .plus-icon {
                        @include icons.icon-plus('#1A1D24');
                        background-repeat: no-repeat;
                        background-size: contain;
                        height: 24px;
                        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                        width: 24px;
                    }
                    &:not(:disabled):hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;

                        .plus-icon {
                            @include icons.icon-plus('#35ABFF');
                        }
                    }
                    &:disabled {
                        background: colors.$neutral25;

                        .plus-icon {
                            @include icons.icon-plus('#8F98AA');
                        }
                    }
                }
            }
        }
        .sim-form {
            display: flex;
            flex-direction: column;
            row-gap: 16px;

            .inputs-row {
                align-content: center;
                align-items: flex-start;
                column-gap: 20px;
                flex-wrap: wrap;
                justify-content: space-evenly;
                width: 100%;

                .ant-form-item {
                    width: 100%;
                }
                .ant-col {
                    width: calc(50% - 10px);
                }
            }
            .multi-input-row,
            .info-row {
                column-gap: 20px;

                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: calc(50% - 10px);

                    > .ant-row {
                        align-items: center;
                        column-gap: 8px;
                        display: flex;
                        flex-direction: row;
                        flex-wrap: nowrap;

                        .labeled-input {
                            display: flex;
                            flex-direction: column;
                            row-gap: 8px;
                            width: 100%;

                            .split-label {
                                flex-wrap: nowrap;
                                justify-content: space-between;
                            }
                        }
                        .labeled-input .ant-input-affix-wrapper,
                        .labeled-input .ant-input-number,
                        .ant-form-item {
                            width: 100%;
                        }
                    }
                    .sks-row {
                        > .ant-col:first-child {
                            align-items: flex-start;
                            display: flex;
                            flex-direction: column;
                            margin-bottom: 24px;
                            width: 14px;
                        }
                        .ant-input {
                            width: 100%;
                        }
                    }
                }
            }
            .info-row {
                .labeled-input {
                    .creator-row {
                        align-items: center;
                        column-gap: 12px;
                        cursor: pointer;
                        flex-wrap: nowrap;
                        width: fit-content;

                        .creator-avatar {
                            align-items: center;
                            background-color: colors.$accentC50;
                            border-radius: 50%;
                            color: colors.$neutral900;
                            display: flex;
                            flex-direction: column;
                            height: 40px;
                            justify-content: center;
                            min-width: 40px;
                            max-width: 40px;
                        }
                        .creator-name {
                            .creator-btn {
                                padding: 0;
                            }
                        }
                    }
                    .lighter-tone {
                        color: colors.$neutral700;
                    }
                }
            }
            .controls-row {
                column-gap: 32px;
                row-gap: 8px;

                .ant-form-item {
                    margin-bottom: 0;
                }
                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;

                    &:disabled {
                        background: colors.$neutral25;
                        color: colors.$neutral300;
                    }
                    &:not(:disabled):hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
            .ant-input-affix-wrapper-disabled,
            textarea.ant-input-disabled,
            .ant-input-number-disabled {
                background-color: colors.$neutral10;
                color: colors.$neutral800;
            }
        }
    }
}

@media screen and (orientation: portrait) {
    .sim-profile .sim-card {
        padding: 16px;

        .inputs-row {
            row-gap: 32px;
            width: 100%;

            > .ant-col {
                width: 100% !important;
            }
        }
        .multi-input-row {
            row-gap: 32px;
            width: 100%;

            > .ant-col {
                width: 100% !important;
            }
        }
    }
}
