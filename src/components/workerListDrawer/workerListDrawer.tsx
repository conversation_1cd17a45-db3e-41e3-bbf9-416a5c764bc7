import { TSim<PERSON>or<PERSON> } from 'types/simulation/simulationWorker';
import { <PERSON><PERSON>, Col, Drawer, List, Row, Table, Tooltip } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faXmark } from '@fortawesome/free-solid-svg-icons';
import { TSimulation } from 'types/simulation/simulation';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { GanttNodeInner, GanttUseCases } from '@components/gantt/gantt';
import { TSimTask } from 'types/simulation/simulationTask';
import { IngameTaskSWAlinks, SWAlinkStatus } from 'types/ingame';

import '@components/drawers/drawers.scss';

type TProps = {
    isOpen: boolean;
    onClose: () => void;
    onDeselect: (id: number) => void;
    onTaskOpen?: (task_uid: TSimTask['uid']) => void;
    onSelect: (id: number) => void;
    simulation: TSimulation | null;
    SWAlinks: IngameTaskSWAlinks | null;
    task: GanttNodeInner | null;
    workers: TSimWorker[] | TSessionWorkerExtended[];
    useCase: GanttUseCases;
};

const WorkerListDrawer = ({
    isOpen,
    onClose,
    onDeselect,
    onTaskOpen,
    onSelect,
    simulation,
    SWAlinks,
    task,
    workers,
    useCase,
}: TProps): JSX.Element => {
    function prepareData() {
        const ds = [...workers].map((wi) => {
            const wLink =
                SWAlinks == null
                    ? null
                    : Object.values(SWAlinks).find(
                          (swali) =>
                              swali != null &&
                              swali.worker_uid == (wi as TSessionWorkerExtended).worker_uid,
                      );
            return { ...wi, link: wLink };
        });
        return ds.sort((a, b) => {
            const incA = task?.workers.find((wi) => wi.id == a.id);
            const incB = task?.workers.find((wi) => wi.id == b.id);
            if (incA && incB) return 0;
            else if (incA && !incB) return -1;
            else if (!incA && incB) return 1;
            else return 0;
        });
    }

    function genCardCols() {
        const cols = [];
        for (let i = 0; i < simulation?.skills.length; i++) {
            cols.push({
                title: simulation.skills[i],
                dataIndex: `skills${i}`,
                key: `skill-${i}`,
            });
        }
        return cols;
    }

    function genCardRows(item: TSimWorker | TSessionWorkerExtended) {
        const row = {
            key: `worker-skills-${item.id}`,
        };
        if (useCase == GanttUseCases.Constructor) {
            for (let i = 0; i < (item as TSimWorker).base_stats.length; i++) {
                row[`skills${i}`] = (item as TSimWorker).base_stats[i];
            }
        } else {
            for (let i = 0; i < (item as TSessionWorkerExtended).current_stats.length; i++) {
                row[`skills${i}`] = (item as TSessionWorkerExtended).current_stats[i];
            }
        }
        return [row];
    }

    function genTaskRows() {
        const row = {
            key: `task-requirements`,
        };
        for (let i = 0; i < task?.statsReq.length; i++) {
            row[`skills${i}`] = task.statsReq[i];
        }
        return [row];
    }

    return (
        <Drawer
            className="template-drawer sim-worker-drawer"
            closeIcon={null}
            onClose={onClose}
            open={isOpen}
            placement="right"
            title={
                <Row className="drawer-header">
                    <Col>
                        <h4>Назначить ПШЕ</h4>
                    </Col>
                    <Col>
                        <Button
                            icon={<FontAwesomeIcon icon={faXmark} />}
                            onClick={onClose}
                        />
                    </Col>
                </Row>
            }
            width={'auto'}
        >
            <Col>
                <Row className="task-info">
                    <Col>
                        <Row className="task-name">
                            <Col className="p2-strong">{`#${task?.row + 1}. ${task?.name}`}</Col>
                            {onTaskOpen != undefined && (
                                <Col>
                                    <Button
                                        onClick={() => onTaskOpen(task?.uid)}
                                        size="small"
                                        type="default"
                                    >
                                        Перейти
                                    </Button>
                                </Col>
                            )}
                        </Row>
                        <Row className="task-dur-workers">
                            <Col className="labeled-input">
                                <Row>
                                    <span className="p3">Кол-во людей (факт / план):</span>
                                </Row>
                                <Row>
                                    <span className="p3 lighter-tone">
                                        {`${task?.workers.length} чел. / ${task?.estWorkers} чел.`}
                                    </span>
                                </Row>
                            </Col>
                            <Col className="labeled-input">
                                <Row>
                                    <span className="p3">Продолжительность (факт / план):</span>
                                </Row>
                                <Row>
                                    <span className="p3 lighter-tone">
                                        {`${task != null ? Math.ceil(task?.curDuration) : '-'} дн. / ${task?.estDuration} дн.`}
                                    </span>
                                </Row>
                            </Col>
                        </Row>
                        <Row className="task-requirements">
                            <Table
                                bordered
                                columns={genCardCols()}
                                dataSource={genTaskRows()}
                                pagination={false}
                                size="small"
                            />
                        </Row>
                    </Col>
                </Row>
                <Row className="drawer-list">
                    <List
                        dataSource={prepareData()}
                        itemLayout="vertical"
                        renderItem={(item) => (
                            <Row className="drawer-card sim-worker-card">
                                <Col>
                                    <div className="drawer-card-avatar" />
                                </Col>
                                <Col className="sim-worker-card-main-col">
                                    <Row>
                                        <Col>
                                            <Row className="sim-worker-name">
                                                <h5 className="bold">{item.name ?? 'Без имени'}</h5>
                                                <div className="desc-l">
                                                    ID:{' '}
                                                    {useCase == GanttUseCases.Constructor
                                                        ? item.id
                                                        : (item as TSessionWorkerExtended).order_id}
                                                </div>
                                            </Row>
                                        </Col>
                                        <Col>
                                            {useCase == GanttUseCases.Ingame &&
                                            task?.allowActions ? (
                                                item.link != null ? (
                                                    item.link.status ==
                                                        SWAlinkStatus.AwaitingAssign ||
                                                    item.link.status == SWAlinkStatus.Current ? (
                                                        <Button
                                                            danger
                                                            disabled={onDeselect == null}
                                                            onClick={() => onDeselect(item.id)}
                                                            size="small"
                                                            type="default"
                                                        >
                                                            {item.link.status ==
                                                            SWAlinkStatus.AwaitingAssign
                                                                ? 'Отменить'
                                                                : 'Убрать'}
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            disabled={onSelect == null}
                                                            onClick={() => onSelect(item.id)}
                                                            size="small"
                                                            type="default"
                                                        >
                                                            {item.link.status ==
                                                            SWAlinkStatus.AwaitingCancel
                                                                ? 'Вернуть'
                                                                : 'Назначить'}
                                                        </Button>
                                                    )
                                                ) : task?.workers.find((wi) => wi.id == item.id) ? (
                                                    <Button
                                                        danger
                                                        disabled={onDeselect == null}
                                                        onClick={() => onDeselect(item.id)}
                                                        size="small"
                                                        type="default"
                                                    >
                                                        Убрать
                                                    </Button>
                                                ) : (
                                                    <Button
                                                        disabled={onSelect == null}
                                                        onClick={() => onSelect(item.id)}
                                                        size="small"
                                                        type="default"
                                                    >
                                                        Назначить
                                                    </Button>
                                                )
                                            ) : task?.allowActions ? (
                                                task?.workers.find((wi) => wi.id == item.id) ? (
                                                    <Button
                                                        danger
                                                        disabled={onDeselect == null}
                                                        onClick={() => onDeselect(item.id)}
                                                        size="small"
                                                        type="default"
                                                    >
                                                        Убрать
                                                    </Button>
                                                ) : (
                                                    <Button
                                                        disabled={onSelect == null}
                                                        onClick={() => onSelect(item.id)}
                                                        size="small"
                                                        type="default"
                                                    >
                                                        Назначить
                                                    </Button>
                                                )
                                            ) : (
                                                <Tooltip title="Задача завершена">
                                                    <Button
                                                        disabled
                                                        size="small"
                                                        type="default"
                                                    >
                                                        {task?.workers.find(
                                                            (wi) => wi.id == item.id,
                                                        )
                                                            ? 'Убрать'
                                                            : 'Назначить'}
                                                    </Button>
                                                </Tooltip>
                                            )}
                                            {item.link != null &&
                                                item.link.status ==
                                                    SWAlinkStatus.AwaitingAssign && (
                                                    <div className="desc-l">Со след. дня</div>
                                                )}
                                            {item.link != null &&
                                                item.link.status ==
                                                    SWAlinkStatus.AwaitingCancel && (
                                                    <div className="desc-l">До конца дня</div>
                                                )}
                                        </Col>
                                    </Row>
                                    <Row className="skills-table">
                                        <Table
                                            bordered
                                            columns={genCardCols()}
                                            dataSource={genCardRows(item)}
                                            pagination={false}
                                            size="small"
                                        />
                                    </Row>
                                </Col>
                            </Row>
                        )}
                        style={{
                            maxHeight: '100%',
                            minHeight: `${146 * 5 - 8}px`,
                        }}
                    />
                </Row>
            </Col>
        </Drawer>
    );
};

export { WorkerListDrawer };
