import { Col, Row, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { rootStore } from '@store/instanse';
import { useEffect } from 'react';

import './sessionDesktop.scss';

const SessionDesktop = (): JSX.Element => {
    const navigate = useNavigate();

    useEffect(() => {
        const handleBeforeUnload = (event) => {
            if (rootStore.ingameStore.SAI?.config?.state != 'finished') {
                event.preventDefault();
                const message = 'Остались несохранённые изменения!';
                event.returnValue = message; // For most browsers
                return message; // For some older versions
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [rootStore.ingameStore.SAI?.config?.state]);

    const menuEntries = [
        {
            id: 'dashboard',
            icon: <div className="menu-item-icon dashboard-icon" />,
            label: 'Дашборд',
            onClick: () => {
                navigate(`/session/dashboard`);
            },
        },
        {
            id: 'gantt',
            icon: <div className="menu-item-icon gantt-icon" />,
            label: 'Гант',
            onClick: () => {
                navigate(`/session/gantt`);
            },
        },
        {
            id: 'calendar',
            icon: <div className="menu-item-icon calendar-icon" />,
            label: 'Календарь',
            onClick: () => {
                navigate(`/session/calendar`);
            },
        },
        {
            id: 'budget',
            icon: <div className="menu-item-icon budget-icon" />,
            label: 'Бюджет',
            onClick: () => {
                navigate(`/session/budget`);
            },
        },
        {
            id: 'workers',
            icon: <div className="menu-item-icon workers-icon" />,
            label: 'Команда',
            onClick: () => {
                navigate(`/session/workers`);
            },
        },
        {
            id: 'graph',
            icon: <div className="menu-item-icon graph-icon" />,
            label: 'Сеть',
            onClick: () => {
                navigate(`/session/graph`);
            },
        },
        {
            id: 'charts',
            icon: <div className="menu-item-icon charts-icon" />,
            label: 'Отчёты',
            onClick: () => {
                navigate(`/session/charts`);
            },
        },
        {
            id: 'chats',
            icon: <div className="menu-item-icon chats-icon" />,
            label: 'Чаты',
            onClick: () => {
                navigate(`/session/chats`);
            },
        },
    ];

    return (
        <div className="ingame-desktop">
            <Row className="logo-row">
                <Col>
                    <Row onClick={() => navigate(`/session/dashboard`)}>
                        <div className="logo-white" />
                    </Row>
                    <Row
                        className="p1-strong"
                        onClick={() => navigate(`/session/dashboard`)}
                    >
                        Simbios
                    </Row>
                </Col>
            </Row>
            <div className="nav-row">
                <Row className="nav-row-inner">
                    {menuEntries.map((me) => (
                        <Tooltip
                            key={me.id}
                            placement="top"
                            title={me.label}
                        >
                            <Col
                                key={me.id}
                                className="menu-entry"
                                onClick={me.onClick}
                            >
                                <Row>{me.icon}</Row>
                                {/*<Row className="desc-l">
                                    {me.label}
                                </Row>*/}
                            </Col>
                        </Tooltip>
                    ))}
                </Row>
                <div
                    className="backdrop"
                    style={{
                        filter: 'url(#glass-distortion)',
                        WebkitFilter: 'url(#glass-distortion)',
                    }}
                />
            </div>
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="0"
                height="0"
                style={{ position: 'absolute', overflow: 'hidden' }}
            >
                <defs>
                    <filter
                        id="glass-distortion"
                        x="0%"
                        y="0%"
                        width="100%"
                        height="100%"
                    >
                        <feTurbulence
                            type="fractalNoise"
                            baseFrequency="0.008 0.008"
                            numOctaves="2"
                            seed="92"
                            result="noise"
                        />
                        <feGaussianBlur
                            in="noise"
                            stdDeviation="2"
                            result="blurred"
                        />
                        <feDisplacementMap
                            in="SourceGraphic"
                            in2="blurred"
                            scale="77"
                            xChannelSelector="R"
                            yChannelSelector="G"
                        />
                    </filter>
                </defs>
            </svg>
        </div>
    );
};

export default SessionDesktop;
