@use '/src/styles/colors';
@use '/src/styles/icons';

.ingame-desktop {
    align-items: center;
    background: url('/src/assets/listiki.jpg');
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: cover;
    display: flex;
    flex-direction: column;
    height: 100vh;
    justify-content: flex-end;
    width: 100vw;

    .logo-row {
        align-items: center;
        flex: 1;
        justify-content: center;

        > .ant-col {
            align-items: center;
            display: flex;
            flex-direction: column;
            row-gap: 8px;

            .logo-white {
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="69" height="80" viewBox="0 0 69 80" fill="none"><path d="M7.83962 15.706L0 20.1562V64.1652L18.4817 53.6602C21.2487 52.0874 22.958 49.1497 22.958 45.9669V20.8941C22.958 17.7307 24.6466 14.8079 27.3872 13.2278L34.437 9.16326L42.1021 13.4632L35.1472 17.5855C32.4587 19.179 30.8101 22.0726 30.8101 25.1979V50.2152C30.8101 53.3656 29.1351 56.2785 26.4123 57.8632L11.6659 66.4461L34.437 79.7572L49.2116 71.2776C51.9606 69.6999 53.6559 66.7723 53.6559 63.6027V38.4752C53.6559 35.2878 55.37 32.3467 58.1433 30.7756L61.2462 29.0178V64.24L68.874 59.9401V15.7814L50.5202 26.2822C47.7653 27.8583 46.0655 30.7892 46.0655 33.9631V59.124C46.0655 62.3291 44.3324 65.2838 41.5348 66.8479L34.437 70.8162L26.9588 66.5582L34.0115 62.396C36.7082 60.8045 38.363 57.9063 38.363 54.775V29.6663C38.363 26.4907 40.0646 23.5586 42.8218 21.983L57.4698 13.6128L34.437 0.189453L19.7731 8.67905C17.0349 10.2643 15.3515 13.1909 15.3577 16.3549L15.4076 41.4925C15.4137 44.5949 13.7947 47.474 11.1406 49.0805L7.83962 51.0784V15.706Z" fill="white"/></svg>');
                background-repeat: no-repeat;
                background-size: contain;
                height: 80px;
                width: 70px;
            }
            .p1-strong {
                color: colors.$accentW0;
            }
        }
    }
    .nav-row {
        //background-color: colors.$black900;
        border-radius: 1.5rem;
        //box-shadow: 0px 0px 2px 2px colors.$black700;
        box-shadow: 0px 6px 24px rgba(0, 0, 0, 0.2);
        margin-bottom: 32px;

        .nav-row-inner {
            background-color: rgba(255, 255, 255, 0.04);
            border-radius: 1.5rem;
            box-shadow: inset 0 0 20px -5px rgba(255, 255, 255, 0.7);
            column-gap: 8px;
            flex-wrap: nowrap;
            inset: 0;
            padding: 16px;
            z-index: 10;
        }
        .backdrop {
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            border-radius: 1.5rem;
            inset: 0;
            overflow: hidden;
        }
        .menu-entry {
            align-items: center;
            background-color: colors.$black900;
            border-radius: 8px;
            color: colors.$accentW0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            max-width: 88px;
            min-width: 88px;
            padding: 12px;
            row-gap: 8px;

            .menu-item-icon {
                background-position: center center;
                background-repeat: no-repeat;
                background-size: contain;
                height: 64px;
                width: 64px;
            }
            .dashboard-icon {
                @include icons.icon-dashboard('#FFFFFF');
            }
            .gantt-icon {
                @include icons.icon-gantt('#FFFFFF');
            }
            .calendar-icon {
                @include icons.icon-calendar('#FFFFFF');
            }
            .budget-icon {
                @include icons.icon-budget('#FFFFFF');
            }
            .workers-icon {
                @include icons.icon-team('#FFFFFF');
            }
            .graph-icon {
                @include icons.icon-network('#FFFFFF');
            }
            .charts-icon {
                @include icons.icon-reports('#FFFFFF');
            }
            .chats-icon {
                @include icons.icon-chats('#FFFFFF');
            }
            &:hover {
                background-color: colors.$black700;
                color: colors.$accentW100;

                .dashboard-icon {
                    @include icons.icon-dashboard('#9AD5FF');
                }
                .gantt-icon {
                    @include icons.icon-gantt('#9AD5FF');
                }
                .calendar-icon {
                    @include icons.icon-calendar('#9AD5FF');
                }
                .budget-icon {
                    @include icons.icon-budget('#9AD5FF');
                }
                .workers-icon {
                    @include icons.icon-team('#9AD5FF');
                }
                .graph-icon {
                    @include icons.icon-network('#9AD5FF');
                }
                .charts-icon {
                    @include icons.icon-reports('#9AD5FF');
                }
                .chats-icon {
                    @include icons.icon-chats('#9AD5FF');
                }
            }
        }
    }
}
