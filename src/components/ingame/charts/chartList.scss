@use '/src/styles/colors';

.chart-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    min-width: 556px;
    row-gap: 16px;
    width: 100%;

    .radio-group {
        column-gap: 12px;
        display: flex;
        flex-direction: row;
        padding: 24px 0 12px 40px;

        &.and-tag {
            padding-bottom: 0;
        }
    }
    .chart-list {
        display: flex;
        flex-direction: column;
        min-width: 556px;
        padding: 24px 40px 140px 38px;
        row-gap: 16px;
        width: 100%;

        .ant-list-empty-text {
            background: colors.$accentW0;
            color: colors.$neutral950;

            .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .ant-row {
                justify-content: center;
            }
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            min-width: 480px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .chart-list-card {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            cursor: pointer;
            //height: 286px;
            display: flex;
            flex-direction: column;
            padding: 16px 16px;
            row-gap: 24px;
            width: 480px;

            &:hover {
                border: 2px solid colors.$accentW300;
            }
            .chart-name {
                align-items: center;
                justify-content: space-between;
                width: 100%;

                .description-btn {
                    .ant-btn-icon {
                        color: colors.$neutral500;
                        height: 100%;
                        width: 100%;
                        padding: 3px 0;
                        text-align: center;
                        vertical-align: middle;

                        h5 {
                            margin: 0;
                        }
                    }
                    &:hover .ant-btn-icon {
                        color: colors.$accentW500;
                    }
                }
            }
            .chart-tags {
            }
        }
    }
}
