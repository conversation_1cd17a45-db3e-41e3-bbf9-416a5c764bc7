import { SettingsManager } from '@classes/settingsManager';
import { IngameLayout } from '@components/ingame/ingameLayout/ingameLayout';
import { Loader } from '@components/ui/loader/loader';
import { rootStore } from '@store/instanse';
import { useReactive } from 'ahooks';
import { message } from 'antd';
import { observer } from 'mobx-react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ComplexTaskChart } from './complexTaskChart';

import './complexTaskChartPage.scss';

type TState = {
    isLoading: boolean;
};

const ComplexTaskChartPage = observer((): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: true,
    });
    const navigate = useNavigate();

    async function initChartPage() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.isLoading = false;
    }

    useEffect(() => {
        initChartPage();
    }, []);

    return (
        <IngameLayout
            showTopBar={true}
            topBarText="План и затраты по задачам"
        >
            {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
            <div className="complex-task-chart-container">
                <div className="chart-container-wrapper">
                    {rootStore.ingameStore.getTimeSettings() != null &&
                        rootStore.ingameStore.tasks.length > 0 &&
                        rootStore.ingameStore.workers.length > 0 && (
                            <ComplexTaskChart useCase="Page" />
                        )}
                </div>
            </div>
        </IngameLayout>
    );
});

export default ComplexTaskChartPage;
