@use '/src/styles/colors';

.complex-task-chart,
.complex-task-chart-mini {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 100%;
    justify-content: center;
    width: 100%;
}

.react-flow.complex-task-flow {
    .react-flow__node-candleNode {
        padding: 0 4px;

        .candle-node-wrapper {
            height: 100%;
            width: 100%;
        }
        &:hover .candle-node-wrapper {
            background: rgba(0, 0, 0, 0.05);
        }
    }
    .react-flow__node-lineNode {
        background-color: rgba(0, 0, 0, 0);

        .line-node-wrapper .react-flow__handle {
            background-color: rgba(0, 0, 0, 0);
            height: 4px;
            min-height: 4px;
            min-width: 4px;
            width: 4px;
        }
    }
}
