import { memo } from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';

export default memo(() => {
    return (
        <div className="line-node-wrapper">
            <Handle
                id="in"
                type="target"
                position={Position.Left}
            />
            <Handle
                id="out"
                type="source"
                position={Position.Left}
            />
        </div>
    );
});
