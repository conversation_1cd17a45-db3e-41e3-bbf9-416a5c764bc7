import { SettingsManager } from '@classes/settingsManager';
import { rootStore } from '@store/instanse';
import { useReactive } from 'ahooks';
import { Button, Col, ConfigProvider, List, message, Radio, Row, Tooltip } from 'antd';
import { observer } from 'mobx-react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { IngameLayout } from '../ingameLayout/ingameLayout';
import { GlobalConstants } from '@classes/constants';

import './chartList.scss';

type TState = {
    isLoading: boolean;
    tagFilter: 'all' | 'simulation' | 'tasks' | 'workers';
};

type ChartListItem = {
    key: string;
    description: string;
    name: string;
    route: string;
    tags: string;
};

const SessionChartList = observer((): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        tagFilter: 'all',
    });
    const navigate = useNavigate();
    const chartList: ChartListItem[] = [
        {
            key: 'tasks-budget-duration',
            description:
                'Показывает плановые и фактические показатели задач по трудочасам и бюджету',
            name: 'План и затраты по задачам',
            route: '/sessions/charts/complex-task-chart',
            tags: 'tasks',
        },
    ];

    async function initChartList() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.isLoading = false;
    }

    useEffect(() => {
        initChartList();
    }, []);

    useEffect(() => {
        const handleBeforeUnload = (event) => {
            if (rootStore.ingameStore.SAI?.config?.state != 'finished') {
                event.preventDefault();
                const message = 'Остались несохранённые изменения!';
                event.returnValue = message; // For most browsers
                return message; // For some older versions
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [rootStore.ingameStore.SAI?.config?.state]);

    function filterChartList() {
        if (state.tagFilter == 'all') {
            return chartList;
        } else {
            return chartList.filter((cli) => cli.tags.includes(state.tagFilter));
        }
    }

    function localizeTags(tags: string) {
        const localeTags = [];
        if (tags.includes('simulation')) {
            localeTags.push('Симуляции');
        }
        if (tags.includes('tasks')) {
            localeTags.push('Задачам');
        }
        if (tags.includes('workers')) {
            localeTags.push('ПШЕ');
        }
        return `Относится к: ${localeTags.join(', ')}`;
    }

    return (
        <IngameLayout
            showTopBar={true}
            topBarText="Отчёты"
        >
            <div className="chart-list-container">
                <div className="radio-group">
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.tagFilter = e.target.value;
                        }}
                        options={[
                            { label: 'Все', value: 'all' },
                            { label: 'ПШЕ', value: 'workers' },
                            { label: 'Задачи', value: 'tasks' },
                            { label: 'Симуляция', value: 'simulation' },
                        ]}
                        optionType="button"
                        value={state.tagFilter}
                    />
                </div>
                <div className="chart-list">
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={filterChartList()}
                            grid={GlobalConstants.ListGridCols}
                            loading={
                                state.isLoading || rootStore.ingameStore.httpState == 'loading'
                            }
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких отчётов нет :)</Row>
                                    </Col>
                                ),
                            }}
                            renderItem={(item) => (
                                <div
                                    key={item.key}
                                    className="chart-list-card"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        navigate(item.route);
                                    }}
                                >
                                    <Row className="chart-name">
                                        <div className="p2-strong">{item.name}</div>
                                        <Tooltip
                                            placement="left"
                                            title={item.description}
                                        >
                                            <Button
                                                className="description-btn"
                                                icon={<h5>?</h5>}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                }}
                                                type="text"
                                            />
                                        </Tooltip>
                                    </Row>
                                    <Row className="chart-tags">
                                        <div className="p3">{localizeTags(item.tags)}</div>
                                    </Row>
                                </div>
                            )}
                        />
                    </ConfigProvider>
                </div>
            </div>
        </IngameLayout>
    );
});

export default SessionChartList;
