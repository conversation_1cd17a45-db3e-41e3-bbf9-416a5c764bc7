import { Button, Checkbox, Col, message, Row, Table, TableProps } from 'antd';
import { IngameLayout } from '../ingameLayout/ingameLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { TSessionBudget, TSessionBudgetRecord } from 'types/session/sessionBudget';
import { rootStore } from '@store/instanse';
import { Loader } from '@components/ui/loader/loader';
import { SettingsManager } from '@classes/settingsManager';
import { observer } from 'mobx-react';

import './sessionBudget.scss';

type TableDataRow = TSessionBudgetRecord & {
    key: string;
};

type TState = {
    budget: TSessionBudget | null;
    isLoading: boolean;
    tableData: TableDataRow[];
};

const SessionBudget = observer((): JSX.Element => {
    const state = useReactive<TState>({
        budget: null,
        isLoading: false,
        tableData: [],
    });
    const navigate = useNavigate();

    const tableColumns: TableProps<TableDataRow>['columns'] = [
        {
            title: 'Задача',
            dataIndex: 'title',
            key: 'title',
            render: (value, record) =>
                record.type == 'task' ? (
                    <Button
                        onClick={() => navigate(`/session/tasks/${record.task_uid}`)}
                        type="link"
                    >
                        {value}
                    </Button>
                ) : record.type == 'total' ? (
                    <Row style={{ justifyContent: 'space-between ' }}>
                        <Col>
                            <span>{value}</span>
                        </Col>
                    </Row>
                ) : (
                    <span>{value}</span>
                ),
        },
        {
            title: 'Прогресс',
            dataIndex: 'progress',
            key: 'progress',
            render: (value, record) =>
                record.type == 'total'
                    ? record.progress > 1
                        ? '100%'
                        : `${(record.progress * 100).toFixed(0)}%`
                    : value == null || value == 0
                      ? null
                      : value > record.current_weight
                        ? '100%'
                        : `${Math.floor((value / record.current_weight) * 100).toFixed(0)}%`,
        },
        {
            title: 'Расходы (факт)',
            dataIndex: 'budget_current',
            key: 'budget_current',
            render: (_, record) =>
                record.budget_current > record.budget_plan ? (
                    <span className="budget-overflow">
                        {Math.ceil(record.budget_current).toLocaleString()}
                    </span>
                ) : (
                    <span>{Math.ceil(record.budget_current).toLocaleString()}</span>
                ),
        },
        {
            title: 'Расходы (прогноз)',
            dataIndex: 'budget_prediction',
            key: 'budget_prediction',
            render: (_, record) =>
                record.budget_prediction == null ? null : record.budget_prediction >
                  record.budget_plan ? (
                    <span className="budget-overflow">
                        {Math.ceil(record.budget_prediction).toLocaleString()}
                    </span>
                ) : (
                    <span>{Math.ceil(record.budget_prediction).toLocaleString()}</span>
                ),
        },
        {
            title: 'Расходы (план)',
            dataIndex: 'budget_plan',
            key: 'budget_plan',
            render: (value) => <span>{value.toLocaleString()}</span>,
        },
        {
            title: 'Команда',
            dataIndex: 'workers',
            key: 'workers',
            render: (value: TSessionBudgetRecord['workers']) => (
                <Row className="p3 budget-workers">
                    {value.map((wi) => (
                        <Col key={wi.worker_uid}>
                            <Button
                                onClick={() => {
                                    navigate(`/session/workers/${wi.worker_uid}`);
                                }}
                                type="link"
                            >
                                {wi.name}
                            </Button>
                        </Col>
                    ))}
                </Row>
            ),
        },
    ];

    async function init() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.budget = await rootStore.ingameStore.getBudgetData();
        state.tableData = state.budget.map((bi) => {
            return {
                ...bi,
                key: bi.type == 'task' ? `task-${bi.task_uid}` : bi.type,
            };
        });
        state.isLoading = false;
    }

    useEffect(() => {
        init();
    }, [rootStore.ingameStore.SAI?.config?.state]);

    return (
        <IngameLayout
            showTopBar={true}
            topBarText="Бюджет"
        >
            {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
            <div className="budget-container">
                <Table
                    bordered
                    columns={tableColumns}
                    dataSource={state.tableData}
                    pagination={false}
                    rowClassName={(record) => (record.type == 'task' ? '' : 'cut-team-col')}
                    size="small"
                />
            </div>
        </IngameLayout>
    );
});

export default SessionBudget;
