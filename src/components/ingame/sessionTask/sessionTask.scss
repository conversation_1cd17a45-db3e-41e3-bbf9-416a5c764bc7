@use '/src/styles/colors';
@use '/src/styles/icons';

.session-task-container {
    min-width: 960px;
    width: 100%;

    .session-task-profile {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px;
        width: 100%;

        .session-task-nav {
            justify-content: end;
        }
        .session-task-details {
            > .ant-col {
                width: 100%;
                .details-header,
                .details-bordered {
                    align-items: center;
                    height: 64px;

                    .ant-col {
                        color: colors.$neutral800;
                    }
                    .ant-col:first-child {
                        padding-left: 16px;
                    }
                    .ant-col:last-child {
                        color: colors.$successW800;
                    }
                    h5 {
                        margin: 0;
                    }
                }
                .details-bordered {
                    border: 1px solid colors.$accentW950;
                    border-radius: 8px;
                }
            }
        }
        .session-task-progress {
            .ant-progress-text {
                color: colors.$accentW500;
                font-family: 'InterVariable';
                font-size: 20px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px;
            }
        }
        .session-task-assigned {
            .ant-table-wrapper {
                width: 100%;
            }
            .ant-table-thead .ant-table-cell,
            .ant-table-row .ant-table-cell,
            .worker-row .worker-name .ant-btn-link,
            .ant-table-thead .edit-assignments-btn {
                color: colors.$accentW950;
                font-family: 'InterVariable';
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 18px;
            }
            .ant-table-thead,
            .ant-table-row {
                border-radius: 8px;

                .ant-table-cell {
                    border-bottom: 1px solid colors.$accentW950;
                    border-top: 1px solid colors.$accentW950;
                    padding: 20px;

                    &:not(:first-child) {
                        text-align: center;
                    }
                }
                .ant-table-cell:first-child {
                    border-left: 1px solid colors.$accentW950;
                    border-bottom-left-radius: 8px;
                    border-top-left-radius: 8px;
                }
                .ant-table-cell:last-child {
                    border-bottom-right-radius: 8px;
                    border-right: 1px solid colors.$accentW950;
                    border-top-right-radius: 8px;
                }
            }
            .ant-table-thead .ant-table-cell::before {
                display: none;
            }
            .ant-table-thead .ant-table-cell .edit-assignments-btn {
                background: colors.$accentW0;
                border: 2px solid colors.$neutral25;
                border-radius: 8px;
                color: colors.$neutral950;
                height: 48px;

                .edit-icon {
                    @include icons.icon-edit('#1A1D24');
                    background-repeat: no-repeat;
                    background-size: contain;
                    height: 24px;
                    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                    width: 24px;
                }
                &:not(:disabled):hover {
                    background: colors.$accentW10;
                    border: 2px solid colors.$accentW10;
                    color: colors.$accentW500;

                    .edit-icon {
                        @include icons.icon-edit('#35ABFF');
                    }
                }
                &:disabled {
                    background: colors.$neutral25;
                    color: colors.$neutral300;

                    .edit-icon {
                        @include icons.icon-edit('#8F98AA');
                    }
                }
            }
            .ant-table-row {
                .ant-table-cell {
                    .worker-row {
                        align-items: center;
                        column-gap: 8px;
                        flex-wrap: nowrap;
                        justify-content: space-between;
                        width: 100%;

                        .worker-avatar-name {
                            align-items: flex-start;
                            display: flex;
                            flex-direction: column;
                            row-gap: 4px;

                            > .ant-row {
                                align-items: center;
                                column-gap: 8px;
                                flex-wrap: nowrap;
                                cursor: pointer;

                                .worker-avatar {
                                    align-items: center;
                                    background-color: colors.$accentC50;
                                    border-radius: 50%;
                                    color: colors.$neutral900;
                                    display: flex;
                                    flex-direction: column;
                                    height: 32px;
                                    justify-content: center;
                                    min-width: 32px;
                                    max-width: 32px;
                                }
                                .ant-btn-link {
                                    color: #1677ff;
                                    height: auto;
                                    padding: 0;

                                    &:hover {
                                        color: #69b1ff;
                                    }
                                }
                            }
                        }
                        .worker-actions {
                            > .ant-row {
                                align-items: center;
                                column-gap: 8px;
                                flex-wrap: nowrap;

                                .action-btn {
                                    background: colors.$accentW0;
                                    border: 2px solid colors.$neutral25;
                                    border-radius: 4px;
                                    color: colors.$neutral950;
                                    height: 32px;
                                    width: 32px;

                                    .cancel-icon,
                                    .restore-icon {
                                        background-repeat: no-repeat;
                                        background-size: contain;
                                        height: 24px;
                                        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                                        width: 24px;
                                    }
                                    .cancel-icon {
                                        @include icons.icon-plus('#1A1D24');
                                        transform: rotate(45deg);
                                    }
                                    .restore-icon {
                                        @include icons.icon-restore('#1A1D24');
                                    }
                                    &:not(:disabled):hover {
                                        background: colors.$accentW10;
                                        border: 2px solid colors.$accentW10;
                                        color: colors.$accentW500;

                                        .cancel-icon {
                                            @include icons.icon-plus('#35ABFF');
                                        }
                                        .restore-icon {
                                            @include icons.icon-restore('#35ABFF');
                                        }
                                    }
                                    &:disabled {
                                        background: colors.$neutral25;

                                        .cancel-icon {
                                            @include icons.icon-plus('#8F98AA');
                                        }
                                        .restore-icon {
                                            @include icons.icon-restore('#8F98AA');
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                &.task-row .ant-table-cell,
                &.task-row:hover .ant-table-cell {
                    background-color: colors.$neutral100;
                }
                &.prev-task-row .ant-table-cell,
                &.task-row:hover .ant-table-cell {
                    background-color: colors.$black25;
                }
            }
            .ant-table-thead .ant-table-cell {
                background-color: colors.$accentW25;
            }
        }
    }
}
