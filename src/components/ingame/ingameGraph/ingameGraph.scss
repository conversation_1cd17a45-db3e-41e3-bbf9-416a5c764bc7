@use '/src/styles/colors';

.child-container {
    padding: 0;

    .graph-container.ingame {
        background: colors.$neutral25;
        height: calc(100vh - 128px);
        width: 100%;

        .bottom-menu {
            background-color: colors.$accentW0;
            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.08);
            height: 64px;
            padding: 8px 48px;
            width: 100%;

            .bottom-controls {
                .ant-btn {
                    background: colors.$accentW0;
                    border-radius: 4px;
                    border: 2px solid colors.$neutral25;
                    color: colors.$neutral950;
                    height: 48px;
                    padding: 0 40px;

                    &:hover {
                        border: 2px solid colors.$accentW10;
                        background: colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
                .ant-btn-icon-only {
                    padding: 0 24px;
                }
                .icon-btn-active svg {
                    color: colors.$accentW500 !important;
                }
                .shuffle-vertical svg {
                    transform: rotate(90deg);
                }
            }
        }
    }
}

.task-drawer {
    .ant-drawer-body > .ant-col > .ant-row {
        width: 100%;
        .iwl,
        .ant-col {
            width: 100%;
        }
        .ant-input-number-affix-wrapper,
        .ant-input-number {
            width: 100%;
        }
        &.scroll-row {
            margin-bottom: 340px;
        }
        &.fixed-row {
            background: colors.$accentW0;
            bottom: 16px;
            padding-top: 10px;
            right: 39px;
            position: fixed;
            width: 315px;
        }
    }
}

.table-drawer {
    border-left: 2px solid colors.$neutral25;
    .node-table .ant-table-container {
        .ant-table-body,
        .ant-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
        }
        .ant-table-tbody .ant-table-cell {
            padding: 4px;
        }
    }
    .node-table .ant-input-number {
        width: 100%;
    }
}

.search-group .filters {
    display: none;
}

.react-flow.graph-flow {
    /* Custom Variables */
    --xy-theme-selected: #f57dbd;
    --xy-theme-hover: #b5c5b5;
    --xy-edge-stroke: #7c869c;
    --xy-edge-stroke-selected: #272c35;
    --xy-theme-edge-hover: #69758e;
    --xy-edge-stroke-width-default: 2px;
    --xy-theme-color-focus: #e8e8e8;

    /* Built-in Variables see https://reactflow.dev/learn/customization/theming */
    --xy-node-border-default: 1px solid #ededed;

    --xy-node-boxshadow-default:
        0px 3.54px 4.55px 0px #00000005, 0px 3.54px 4.55px 0px #0000000d,
        0px 0.51px 1.01px 0px #0000001a;

    --xy-node-border-radius-default: 8px;

    --xy-handle-background-color-default: colors.$accentW0;
    --xy-handle-border-color-default: #aaaaaa;

    --xy-edge-label-color-default: #505050;

    &.dark {
        --xy-node-boxshadow-default:
            0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.05),
            /* light shadow */ 0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.13),
            /* medium shadow */ 0px 0.51px 1.01px 0px rgba(255, 255, 255, 0.2); /* smallest shadow */
        --xy-theme-color-focus: #535353;
    }

    /* Customizing Default Theming */

    .react-flow__node {
        align-items: center;
        background-color: var(--xy-node-background-color-default);
        border-radius: var(--xy-node-border-radius-default);
        border: 2px solid colors.$neutral25;
        box-shadow: var(--xy-node-boxshadow-default);
        color: colors.$neutral500;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        height: 100px;
        justify-content: center;
        text-align: center;
        width: 296px;

        .react-flow__handle {
            border-radius: 0;
            cursor: grab;
            max-height: 0;
            max-width: 0;
        }
        .graph-node-wrapper {
            border-radius: var(--xy-node-border-radius-default);
            height: 100%;
            width: 100%;

            .graph-node-inner {
                border-radius: var(--xy-node-border-radius-default);
                display: flex;
                flex-direction: column;
                height: 100%;
                width: 100%;

                .task-id,
                .task-name {
                    color: colors.$neutral500;
                }
                .task-id {
                    border-radius: var(--xy-node-border-radius-default)
                        var(--xy-node-border-radius-default) 0 0;
                    justify-content: left;
                    padding: 4px 8px;
                }
                .task-name {
                    align-items: center;
                    height: 48px;
                    justify-content: center;
                    padding: 1px 4px;

                    .p2 {
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        max-width: 270px;
                        overflow: hidden;
                        white-space: normal;
                        word-break: break-all;
                    }
                }
                .task-progress {
                    padding-top: 10px;
                    .ant-progress {
                        font-size: 8px;
                        line-height: 1;
                    }
                }
            }
        }
    }
    .react-flow__node.selectable:hover,
    .react-flow__node.draggable:hover {
        border-color: var(--xy-theme-hover);
    }
    .react-flow__edge:hover .react-flow__edge-path {
        cursor: pointer;
        filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.8)) !important;
        stroke: #00ffff !important;
        stroke-width: 2px !important;
    }
    .react-flow__edge.critical:hover .react-flow__edge-path {
        cursor: pointer;
        filter: drop-shadow(0 0 8px rgba(255, 128, 0, 0.8)) !important;
        stroke: #ff7700 !important;
        stroke-width: 2px !important;
    }
    .react-flow__handle {
        background-color: var(--xy-handle-background-color-default);
    }
}
