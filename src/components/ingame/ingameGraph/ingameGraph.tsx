import {
    Background,
    MarkerType,
    Node,
    ReactFlow,
    ReactFlowProvider,
    useEdgesState,
    useNodesState,
    useReactFlow,
} from '@xyflow/react';
import { useReactive } from 'ahooks';
import { useCallback, useEffect } from 'react';
import { TSimulation } from 'types/simulation/simulation';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { IngameTimeSettings } from 'types/ingame';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { TSimTask } from 'types/simulation/simulationTask';
import { rootStore } from '@store/instanse';
import Dagre from '@dagrejs/dagre';
import { IngameLayout } from '../ingameLayout/ingameLayout';
import { Loader } from '@components/ui/loader/loader';
import Colors from '@classes/colors';
import { GraphLowerMenu } from '@components/constructor/constructorGraph/graphLowerMenu';
import graphNode from './graphNode';
import { TaskUtils } from '@classes/taskUtitlty';
import { DefaultTimeSettings } from '@store/ingame/data';
import { GanttUseCases } from '@components/gantt/gantt';
import { SettingsManager } from '@classes/settingsManager';
import { observer } from 'mobx-react';
import _ from 'lodash';

import '@xyflow/react/dist/style.css';
import './ingameGraph.scss';

// Константы для стилей
const EDGE_STYLE = {
    stroke: '#4a9eff',
    strokeWidth: 1.5,
    transition: 'all 0.3s ease',
};
const EDGE_CRIT_STYLE = {
    stroke: Colors.Error.warm[400],
    strokeWidth: 2.5,
    transition: 'all 0.3s ease',
};

const EDGE_MARKER = {
    type: MarkerType.ArrowClosed,
    width: 12,
    height: 12,
    color: '#4a9eff',
    strokeWidth: 1.5,
};
const EDGE_CRIT_MARKER = {
    type: MarkerType.ArrowClosed,
    width: 12,
    height: 12,
    color: Colors.Error.warm[400],
    strokeWidth: 1.5,
};

const defaultEdgeOptions = {
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#00bfff', strokeWidth: 2 },
};

const nodeTypes = { graphNode: graphNode };

const getLayoutedElements = (nodes, edges, options) => {
    const g = new Dagre.graphlib.Graph().setDefaultEdgeLabel(() => ({}));
    g.setGraph({
        align: 'UL',
        edgesep: 0,
        nodesep: 30,
        rankdir: options.direction,
        ranksep: 30,
    });

    edges.forEach((edge) => g.setEdge(edge.source, edge.target));
    nodes.forEach((node) =>
        g.setNode(node.id, {
            ...node,
            width: 296,
            height: 100,
        }),
    );

    Dagre.layout(g);

    return {
        nodes: nodes.map((node) => {
            const position = g.node(node.id);
            // We are shifting the dagre node position (anchor=center center) to the top left
            // so it matches the React Flow node anchor point (top left).
            const x = position.x - 296 / 2;
            const y = position.y - 100 / 2;

            return { ...node, position: { x, y } };
        }),
        edges,
    };
};

export type GraphNode = Node<TSWEgrid>;

export type TSWEgrid = TSessionTaskExtended & {
    grid_x?: TSimTask['grid_x'];
    grid_y?: TSimTask['grid_y'];
};

type TState = {
    initSort: boolean;
    isLoading: boolean;
    simulation: TSimulation | null;
    tasks: TSWEgrid[];
    timeSettings: IngameTimeSettings | null;
    workers: TSessionWorkerExtended[];
};

const IngameGraphContent = observer((): JSX.Element => {
    const state = useReactive<TState>({
        initSort: false,
        isLoading: false,
        simulation: null,
        tasks: [],
        timeSettings: null,
        workers: [],
    });
    const navigate = useNavigate();
    const { fitView, zoomIn, zoomOut } = useReactFlow();
    const [nodes, setNodes] = useNodesState<GraphNode>([]);
    const [edges, setEdges] = useEdgesState([]);

    const handleZoomIn = useCallback(() => {
        zoomIn();
    }, [zoomIn]);

    const handleZoomOut = useCallback(() => {
        zoomOut();
    }, [zoomOut]);

    const handleFitView = useCallback(() => {
        fitView({ padding: 0.2 });
    }, [fitView]);

    async function initGraph() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.simulation = rootStore.ingameStore.getSimulation();
        state.timeSettings = rootStore.ingameStore.getTimeSettings();
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.workers = await rootStore.ingameStore.getWorkerList();
        tasksToNodes();
        tasksToEdges();
        state.isLoading = false;
    }

    useEffect(() => {
        initGraph();
    }, []);

    useEffect(() => {
        if (nodes.length > 0 && edges.length > 0 && !state.initSort) {
            state.initSort = true;
            onLayout('LR');
        }
    }, [nodes, edges]);

    function tasksToNodes() {
        const taskUtilsOut = TaskUtils.tasksToGantt({
            hoursInADay: DefaultTimeSettings.workDayHours - +DefaultTimeSettings.workDayLunchSkip,
            simulation: state.simulation,
            tasks: state.tasks,
            useCase: GanttUseCases.Ingame,
            workers: state.workers,
        });
        state.tasks = state.tasks.map((sti) => {
            const tuo = taskUtilsOut.find((tuoi) => tuoi.id == sti.id);
            return { ...sti, milestone: tuo?.milestone };
        });
        const pt: GraphNode[] = state.tasks.map((t) => {
            return {
                id: '' + t.id,
                position: {
                    x: t.grid_x,
                    y: t.grid_y,
                },
                data: {
                    ...t,
                    grid_x: t.grid_x,
                    grid_y: t.grid_y,
                    allowLeft: state.simulation?.first_task != t.id,
                    allowRight: state.simulation?.last_task != t.id,
                },
                type: 'graphNode',
            };
        });
        setNodes(pt);
    }

    function tasksToEdges() {
        const links = [];
        for (let i = 0; i < state.tasks.length; i++) {
            const t = state.tasks[i];
            for (let j = 0; j < t.previous.length; j++) {
                const tprev = state.tasks.find((ti) => ti.id == t.previous[j]);
                const crit = t.milestone && tprev.milestone;
                links.push({
                    id: `e${tprev.id}-${t.id}`,
                    source: `${tprev.id}`,
                    target: `${t.id}`,
                    type: 'smoothstep',
                    animated: true,
                    className: crit ? 'critical' : '',
                    style: crit ? EDGE_CRIT_STYLE : EDGE_STYLE,
                    markerEnd: crit ? EDGE_CRIT_MARKER : EDGE_MARKER,
                });
            }
        }
        setEdges(links);
    }

    const handleNodeClick = useCallback((event: React.MouseEvent, node: GraphNode) => {
        event.preventDefault();
        navigate(`/session/tasks/${node.data.task_uid}`);
    }, []);

    const onLayout = useCallback(
        (direction) => {
            const layouted = getLayoutedElements(nodes, edges, { direction });

            setNodes([...layouted.nodes]);
            setEdges([...layouted.edges]);

            fitView();
        },
        [nodes, edges],
    );

    return (
        <IngameLayout
            paddingNone={true}
            showTopBar={true}
            topBarText="Сетевой график"
        >
            <div className="graph-container ingame">
                {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
                <ReactFlow
                    className="graph-flow"
                    nodes={nodes}
                    edges={edges}
                    onNodeClick={handleNodeClick}
                    nodeTypes={nodeTypes}
                    defaultEdgeOptions={defaultEdgeOptions}
                    proOptions={{ hideAttribution: true }}
                    // Запрет удаления
                    deleteKeyCode={null}
                    defaultViewport={{ x: 0, y: 0, zoom: 0.7 }}
                    // Не фокусить связи
                    edgesFocusable={false}
                    // Отруб подсветки
                    elevateEdgesOnSelect={false}
                    fitView={true}
                    maxZoom={1.5}
                    minZoom={0.1}
                    // Запрет мультивыбора
                    multiSelectionKeyCode={null}
                    // Не соединять узлы
                    nodesConnectable={false}
                    // Не перемещать узлы
                    nodesDraggable={false}
                    // Не фокусить узлы
                    nodesFocusable={false}
                    // Рендер только видимых элементов
                    onlyRenderVisibleElements={true}
                    // Запрет выбора
                    selectionKeyCode={null}
                >
                    <Background
                        gap={16}
                        size={1}
                        color={Colors.Black[950]}
                    />
                </ReactFlow>
                <GraphLowerMenu
                    addTableTask={null}
                    fitView={handleFitView}
                    flipTableOpen={null}
                    saveChanges={null}
                    shuffleHorizontal={() => onLayout('LR')}
                    shuffleVertical={() => onLayout('TB')}
                    tableIsOpen={false}
                    tasksWithValidationErrors={[]}
                    zoomIn={handleZoomIn}
                    zoomOut={handleZoomOut}
                />
            </div>
        </IngameLayout>
    );
});

const IngameGraph = (): JSX.Element => {
    return (
        <ReactFlowProvider>
            <IngameGraphContent />
        </ReactFlowProvider>
    );
};

export default IngameGraph;
