import { <PERSON>le, Position } from '@xyflow/react';
import { Col, Progress, Row } from 'antd';
import { memo } from 'react';
import { TSWEgrid } from './ingameGraph';
import Colors from '@classes/colors';

type GraphNodeProps = {
    data: TSWEgrid;
    isConnectable: boolean;
};

export default memo(({ data }: GraphNodeProps) => (
    <div
        className="graph-node-wrapper"
        style={{
            backgroundColor: data.milestone ? Colors.Error.warm[25] : Colors.Accent.warm[0],
        }}
    >
        {data.previous.length > 0 && (
            <Handle
                id="in"
                type="target"
                position={Position.Left}
            />
        )}
        <div className="graph-node-inner">
            <Row
                className="p3 task-id"
                style={{ justifyContent: 'space-between' }}
            >
                <Col>ID {data.id}</Col>
            </Row>
            <Row className="task-name">
                <span className="p2">{data.name}</span>
            </Row>
            <Row className="task-progress">
                <Progress
                    percent={Math.floor((data.progress / data.current_weight) * 100)}
                    showInfo={false}
                    strokeColor={data.milestone ? Colors.Error.warm[500] : Colors.Accent.warm[500]}
                    trailColor={data.milestone ? Colors.Error.warm[200] : Colors.Accent.warm[25]}
                    type="line"
                />
            </Row>
        </div>
        {data.following.length > 0 && (
            <Handle
                id="out"
                type="source"
                position={Position.Right}
            />
        )}
    </div>
));
