import { Col, message, Progress, Row } from 'antd';
import { IngameLayout } from '../ingameLayout/ingameLayout';
import { IngamePermissions, IngameTimeSettings } from 'types/ingame';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { TSimulation } from 'types/simulation/simulation';
import { useNavigate } from 'react-router-dom';
import { rootStore } from '@store/instanse';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { SettingsManager } from '@classes/settingsManager';
import { Loader } from '@components/ui/loader/loader';
import { Gantt, GanttUseCases } from '@components/gantt/gantt';
import { TSessionAssignmentInfo } from 'types/session/sessionAssignmentInfo';
import Colors from '@classes/colors';
import { TChatExtended } from '@store/socketStore';
import { TChatMessage } from 'types/chats/chats';
import dayjs from 'dayjs';
import { Common } from '@classes/common';
import { ComplexTaskChart } from '../charts/complexTaskChart/complexTaskChart';

import './sessionDashboard.scss';

type TState = {
    isLoading: boolean;
    SAI: TSessionAssignmentInfo | null;
    simulation: TSimulation | null;
    permissions: IngamePermissions | null;
    tasks: TSessionTaskExtended[];
    timeSettings: IngameTimeSettings | null;
    workers: TSessionWorkerExtended[];
};

const SessionDashboard = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        permissions: null,
        SAI: null,
        simulation: null,
        tasks: [],
        timeSettings: null,
        workers: [],
    });
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();
    const creds = SettingsManager.getConnectionCredentials();

    async function init() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        // Gantt portion
        state.permissions = rootStore.ingameStore.getPermissions();
        state.simulation = rootStore.ingameStore.getSimulation();
        state.timeSettings = rootStore.ingameStore.getTimeSettings();
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.workers = await rootStore.ingameStore.getWorkerList();
        // Chats portion
        // Подгрузка списка чатов, если этого ещё не было сделано;
        const result = await rootStore.socketStore.getChatList(undefined, false, false);
        // Проверка на существование чата, если указан
        if (result != null) {
            messageApi.error(result.text);
        }
        // 4WD portion
        state.SAI = await rootStore.ingameStore.getInfo();
        state.isLoading = false;
    }

    useEffect(() => {
        init();
    }, [rootStore.ingameStore.SAI?.config?.state]);

    /**
     * Получить соответствующее типу название чата
     * @param chat Объект чата
     * @returns Название чата
     */
    function getChatName(chat: TChatExtended) {
        if (chat == null) return '';
        if (chat.type == 'group') {
            return chat.chat_name;
        } else {
            const otherSender = chat.users.find((cui) => cui.id != creds?.user_id);
            if (otherSender == undefined || otherSender?.name == null) return 'Неизвестный';
            else return otherSender.name;
        }
    }

    /**
     * Получить отправителя последнего сообщения
     * @param chat Объект чата
     * @returns Имя отправителя
     */
    function getMessageSenderName(
        chat: TChatExtended,
        sender_id: TChatMessage['sender_id'] = null,
    ) {
        if (chat.messages.length == 0) return null;
        const msgSenderId = sender_id == null ? chat.messages[0].sender_id : sender_id;
        if (msgSenderId == creds?.user_id) return 'Вы';
        else {
            const sender = chat.users.find((cui) => cui.id == msgSenderId);
            if (sender == undefined || sender?.name == null) return 'Неизвестный';
            else return sender.name;
        }
    }

    /**
     * Форматирование времени отправки последнего сообщения
     * @param msg Объект сообщения
     * @returns HH:mm, день недели или дата
     */
    function formatMessageDatetime(msg: TChatMessage) {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const dt = dayjs.utc(msg.created_at).tz(timezone);
        if (dt.isSame(dayjs.utc().tz(timezone), 'day')) {
            return dt.format('HH:mm');
        }
        if (dt.diff(dayjs.utc().tz(timezone), 'days') < 7) {
            return Common.weekdayNumToShortString(+dt.format('d'));
        } else {
            return dt.format('DD.MM');
        }
    }

    return (
        <IngameLayout
            additionalClass="uni-fixed-height"
            paddingNone={true}
            showTopBar={false}
            topBarText={null}
        >
            {contextHolder}
            {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
            <div className="dashboard-container">
                <Row className="gantt-graph-row">
                    <Col
                        className="gantt-col"
                        onClick={(e) => {
                            e.stopPropagation();
                            navigate('/session/gantt');
                        }}
                    >
                        {state.permissions != null &&
                            state.simulation != null &&
                            state.timeSettings != null &&
                            state.tasks.length > 0 &&
                            state.workers.length > 0 && (
                                <Gantt
                                    className="ingame-mini"
                                    currentDay={rootStore.ingameStore.SAI?.config?.day}
                                    daysInAWeek={state.timeSettings?.daysInAWeek}
                                    onTaskOpen={null}
                                    onWorkerAssign={null}
                                    onWorkerUnassign={null}
                                    simulation={state.simulation}
                                    SWAlinks={null}
                                    tasks={state.tasks}
                                    useCase={GanttUseCases.Mini}
                                    workers={state.workers}
                                />
                            )}
                    </Col>
                    <Col
                        className="graph-col"
                        onClick={(e) => {
                            e.stopPropagation();
                            navigate('/sessions/charts/complex-task-chart');
                        }}
                    >
                        {state.timeSettings != null &&
                            state.tasks.length > 0 &&
                            state.workers.length > 0 && <ComplexTaskChart useCase="Mini" />}
                    </Col>
                </Row>
                <Row className="chats-4wd-row">
                    {rootStore.socketStore.chatList.length == 0 ? (
                        <Col className="chats-col no-hover">
                            <Row className="no-chats">
                                <span className="p2">Чатов пока что нет</span>
                            </Row>
                        </Col>
                    ) : (
                        <Col className="chats-col no-hover">
                            {rootStore.socketStore.chatList.map((chat) => {
                                const chatName = getChatName(chat);
                                const lastMessageSender = getMessageSenderName(chat);
                                return (
                                    <Row
                                        className="chat-row"
                                        key={chat.id}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            navigate(`/session/chats/${chat.id}`);
                                        }}
                                    >
                                        <Col className="avatar-col">
                                            <Row className="chat-avatar">
                                                <div className="p1-strong">{chatName[0]}</div>
                                            </Row>
                                        </Col>
                                        <Col
                                            className="body-col"
                                            flex={1}
                                        >
                                            <Row className="p3 chat-name">{chatName}</Row>
                                            {chat.messages.length == 0 ? (
                                                <Row className="p3 last-message">Нет сообщений</Row>
                                            ) : (
                                                <Row className="p3 last-message">
                                                    {chat.type == 'group' && (
                                                        <Col className="message-sender">
                                                            {lastMessageSender}
                                                        </Col>
                                                    )}
                                                    <Col className="message-text">
                                                        {chat.messages[0].text}
                                                    </Col>
                                                </Row>
                                            )}
                                        </Col>
                                        {lastMessageSender != null && (
                                            <Col className="time-col">
                                                <Row className="p3 chat-datetime">
                                                    {formatMessageDatetime(chat.messages[0])}
                                                </Row>
                                            </Col>
                                        )}
                                    </Row>
                                );
                            })}
                        </Col>
                    )}
                    <Col className="four-wheel-drive-col no-hover">
                        <Row>
                            <Col>
                                <Row className="knob-row">
                                    <Progress
                                        format={(percent) => <h4>{Number(percent).toFixed(2)}%</h4>}
                                        percent={
                                            ((state.SAI?.parameters?.total_hours_current ?? 1) /
                                                (state.SAI?.parameters?.total_hours_plan ?? 1)) *
                                            100
                                        }
                                        size={160}
                                        status="normal"
                                        strokeColor={Colors.Accent.warm[600]}
                                        trailColor={Colors.Accent.warm[25]}
                                        type="dashboard"
                                    />
                                    <h4>Часов</h4>
                                </Row>
                            </Col>
                            <Col>
                                <Row className="knob-row">
                                    <Progress
                                        format={(percent) => <h4>{Number(percent).toFixed(2)}%</h4>}
                                        percent={
                                            ((state.SAI?.parameters?.total_budget_current ?? 1) /
                                                (state.SAI?.parameters?.total_budget_plan ?? 1)) *
                                            100
                                        }
                                        size={160}
                                        status="normal"
                                        strokeColor={Colors.Accent.warm[600]}
                                        trailColor={Colors.Accent.warm[25]}
                                        type="dashboard"
                                    />
                                    <h4>Затрат</h4>
                                </Row>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <Row className="knob-row">
                                    <Progress
                                        format={(percent) => <h4>{Number(percent).toFixed(2)}%</h4>}
                                        percent={state.SAI?.parameters?.motivation ?? 100}
                                        size={160}
                                        status="normal"
                                        strokeColor={Colors.Accent.warm[600]}
                                        trailColor={Colors.Accent.warm[25]}
                                        type="dashboard"
                                    />
                                    <h4>МОТ</h4>
                                </Row>
                            </Col>
                            <Col>
                                <Row className="knob-row">
                                    <Progress
                                        format={(percent) => <h4>{Number(percent).toFixed(2)}%</h4>}
                                        percent={state.SAI?.parameters?.productivity ?? 100}
                                        size={160}
                                        status="normal"
                                        strokeColor={Colors.Accent.warm[600]}
                                        trailColor={Colors.Accent.warm[25]}
                                        type="dashboard"
                                    />
                                    <h4>КПД</h4>
                                </Row>
                            </Col>
                        </Row>
                    </Col>
                </Row>
            </div>
        </IngameLayout>
    );
};

export default SessionDashboard;
