@use '/src/styles/colors';

#ingame-layout.uni-fixed-height .main-container {
    max-height: 100vh;
    min-height: 100vh;

    .child-container:has(.dashboard-container) {
        max-height: 100vh;
        min-height: 100vh;
    }
}
.dashboard-container {
    display: flex;
    flex-direction: column;
    max-height: 100%;
    min-height: 100%;
    overflow: auto;
    padding: 24px;
    row-gap: 18px;
    width: 100%;

    .gantt-graph-row .gantt-col,
    .gantt-graph-row .graph-col,
    .chats-4wd-row .chats-col,
    .chats-4wd-row .four-wheel-drive-col {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral50;
        max-height: 100%;
        min-height: 100%;

        &:not(.no-hover):hover {
            border: 2px solid colors.$accentW100;
        }
    }
    .gantt-graph-row {
        column-gap: 24px;
        flex-wrap: nowrap;
        height: calc(55% - 9px);
        min-width: 1696px;
        width: 100%;

        .gantt-col {
            background: colors.$neutral25;
            display: flex;
            flex-direction: column;
            height: 100%;
            min-width: 1096px;
            overflow-y: scroll;
            width: calc(65% - 12px);

            .ingame-mini {
                > .ant-row {
                    height: 100%;
                }
            }
            .gantt-flow-col {
                min-height: unset;
            }
        }
        .graph-col {
            align-items: center;
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: center;
            min-width: 576px;
            padding: 8px;
            width: calc(35% - 12px);
        }
    }
    .chats-4wd-row {
        column-gap: 24px;
        flex-wrap: nowrap;
        height: calc(45% - 9px);
        min-height: 376px;
        min-width: 1696px;
        width: 100%;

        .chats-col {
            min-width: 1096px;
            overflow-y: auto;
            width: calc(65% - 12px);

            .no-chats {
                align-items: center;
                display: flex;
                flex-direction: row;
                height: 100%;
                justify-content: center;
                width: 100%;
            }
            .chat-row {
                align-items: center;
                border: 2px solid colors.$accentW0;
                column-gap: 12px;
                height: 72px;
                padding: 12px 18px;

                &:hover {
                    border: 2px solid colors.$accentW100;
                }
                .avatar-col {
                    .chat-avatar {
                        align-items: center;
                        background-color: colors.$accentC50;
                        border-radius: 50%;
                        color: colors.$neutral900;
                        display: flex;
                        flex-direction: column;
                        height: 40px;
                        justify-content: center;
                        min-width: 40px;
                        max-width: 40px;
                    }
                }
                .body-col {
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    row-gap: 4px;

                    .chat-name {
                        color: colors.$neutral800;
                    }
                    .last-message {
                        color: colors.$neutral800;
                        column-gap: 4px;

                        .message-sender {
                            color: colors.$neutral800;
                        }
                        .message-text {
                            color: colors.$neutral200;
                        }
                    }
                }
                .time-col {
                    align-items: center;
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    justify-content: flex-start;
                }
            }
        }
        .four-wheel-drive-col {
            background: none;
            border: none;
            display: flex;
            flex-direction: column;
            min-width: 576px;
            row-gap: 16px;
            width: calc(35% - 12px);

            &:hover {
                border: none;
            }
            > .ant-row {
                column-gap: 16px;
                height: 100%;

                > .ant-col {
                    width: calc(50% - 8px);

                    .knob-row {
                        align-items: center;
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral50;
                        column-gap: 8px;
                        justify-content: flex-start;
                        flex-wrap: nowrap;
                        min-width: 280px;
                        padding: 8px;
                        width: 100%;

                        &:hover {
                            border: 2px solid colors.$accentW100;
                        }
                        h4 {
                            color: colors.$neutral900;
                            margin: 0;
                        }
                    }
                }
            }
        }
    }
}
