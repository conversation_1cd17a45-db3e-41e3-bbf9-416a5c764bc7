import { Gantt, GanttUseCases } from '@components/gantt/gantt';
import { IngameLayout } from '../ingameLayout/ingameLayout';
import { observer } from 'mobx-react';
import { useReactive } from 'ahooks';
import { Loader } from '@components/ui/loader/loader';
import { IngamePermissions, IngameTimeSettings, IngameTotalSWAlinks } from 'types/ingame';
import { useEffect } from 'react';
import { rootStore } from '@store/instanse';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { TSimulation } from 'types/simulation/simulation';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { SettingsManager } from '@classes/settingsManager';

import './sessionGantt.scss';

type TState = {
    dayTick: { day: number; tick: number } | null;
    isLoading: boolean;
    permissions: IngamePermissions | null;
    simulation: TSimulation | null;
    SWAlinks: IngameTotalSWAlinks | null;
    tasks: TSessionTaskExtended[];
    timeSettings: IngameTimeSettings | null;
    workers: TSessionWorkerExtended[];
};

const SessionGantt = observer((): JSX.Element => {
    const state = useReactive<TState>({
        dayTick: null,
        isLoading: false,
        permissions: null,
        simulation: null,
        SWAlinks: null,
        tasks: [],
        timeSettings: null,
        workers: [],
    });
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();

    async function initGantt() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.dayTick = rootStore.ingameStore.getDayTick();
        state.permissions = rootStore.ingameStore.getPermissions();
        state.simulation = rootStore.ingameStore.getSimulation();
        state.timeSettings = rootStore.ingameStore.getTimeSettings();
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.workers = await rootStore.ingameStore.getWorkerList();
        state.SWAlinks = await rootStore.ingameStore.getTotalSWAlinks();
        state.isLoading = false;
    }

    useEffect(() => {
        initGantt();
    }, [rootStore.ingameStore.SAI?.config?.state]);

    useEffect(() => {
        state.dayTick = rootStore.ingameStore.getDayTick();
    }, [
        rootStore.ingameStore.SAI?.config?.week,
        rootStore.ingameStore.SAI?.config?.day,
        rootStore.ingameStore.SAI?.config?.tick,
    ]);

    function onTaskOpen(task_uid: TSimTask['uid']) {
        const task = state.tasks.find((ti) => ti.task_uid == task_uid);
        if (task == undefined) {
            messageApi.error('Не удалось найти задачу для перехода');
            return;
        }
        navigate(`/session/tasks/${task.task_uid}`);
    }

    async function onWorkerAssign(task_uid: TSimTask['uid'], worker_uid: TSimWorker['uid']) {
        if (!rootStore.ingameStore.allowSWAassign(task_uid)) {
            messageApi.warning('Назначение на эту задачу не разрешено');
            return;
        }
        await rootStore.ingameStore.SWAassign(task_uid, worker_uid);
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.workers = await rootStore.ingameStore.getWorkerList();
        state.SWAlinks = await rootStore.ingameStore.getTotalSWAlinks();
    }

    async function onWorkerUnassign(task_uid: TSimTask['uid'], worker_uid: TSimWorker['uid']) {
        if (!rootStore.ingameStore.allowSWAcancel(task_uid)) {
            messageApi.warning('Снятие назначения с этой задачи не разрешено');
            return;
        }
        await rootStore.ingameStore.SWAcancel(task_uid, worker_uid);
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.workers = await rootStore.ingameStore.getWorkerList();
        state.SWAlinks = await rootStore.ingameStore.getTotalSWAlinks();
    }

    return (
        <IngameLayout
            paddingNone={true}
            showTopBar={true}
            topBarText="Диаграмма Ганта"
        >
            {contextHolder}
            {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
            {state.permissions != null &&
                state.simulation != null &&
                state.timeSettings != null &&
                state.tasks.length > 0 &&
                state.workers.length > 0 &&
                state.SWAlinks != null && (
                    <Gantt
                        className="ingame"
                        currentDay={state.dayTick?.day}
                        daysInAWeek={state.timeSettings.daysInAWeek}
                        onTaskOpen={onTaskOpen}
                        onWorkerAssign={state.permissions.workerAssignTask ? onWorkerAssign : null}
                        onWorkerUnassign={
                            state.permissions.workerCancelTask ? onWorkerUnassign : null
                        }
                        simulation={state.simulation}
                        SWAlinks={state.SWAlinks}
                        tasks={state.tasks}
                        useCase={GanttUseCases.Ingame}
                        workers={state.workers}
                    />
                )}
        </IngameLayout>
    );
});

export default SessionGantt;
