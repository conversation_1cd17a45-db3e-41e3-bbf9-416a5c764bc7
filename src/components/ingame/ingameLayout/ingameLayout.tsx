import Logo from '@components/ui/logo/logo';
import { faHome, faPlay, faPause, faStop } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Col, message, Row, Tooltip } from 'antd';
import { ReactNode, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { rootStore } from '@store/instanse';
import { observer } from 'mobx-react';
import { IngamePermissions } from 'types/ingame';
import { useReactive } from 'ahooks';
import { TSessionAssignmentInfo } from 'types/session/sessionAssignmentInfo';

import './ingameLayout.scss';
import useDraggableScroll from 'use-draggable-scroll';

type IngameLayoutProps = {
    additionalClass?: string;
    paddingNone?: boolean;
    showTopBar: boolean;
    topBarText: string | null;
    children: ReactNode;
};

type TState = {
    permissions: IngamePermissions | null;
    SAI: TSessionAssignmentInfo | null;
};

const IngameLayout = observer(
    ({
        additionalClass,
        paddingNone = false,
        showTopBar,
        topBarText,
        children,
    }: IngameLayoutProps): JSX.Element => {
        const state = useReactive<TState>({
            permissions: null,
            SAI: null,
        });
        const navigate = useNavigate();
        const location = useLocation();
        const menuGroupRef = useRef<HTMLDivElement>(null);
        const { onMouseDown } = useDraggableScroll(menuGroupRef, {
            direction: 'vertical',
        });

        const menuEntries = [
            {
                id: 'desktop',
                icon: <div className="menu-item-icon desktop-icon" />,
                label: 'Рабочий стол',
                active: () => false,
                onClick: () => {
                    navigate('/session/desktop');
                },
            },
            {
                id: 'dashboard',
                icon: <div className="menu-item-icon dashboard-icon" />,
                label: 'Дашборд',
                active: (path) => path.includes('/dashboard'),
                onClick: () => {
                    navigate(`/session/dashboard`);
                },
            },
            {
                id: 'gantt',
                icon: <div className="menu-item-icon gantt-icon" />,
                label: 'Гант',
                active: (path) => path.includes('/gantt') || path.includes('/tasks'),
                onClick: () => {
                    navigate(`/session/gantt`);
                },
            },
            {
                id: 'calendar',
                icon: <div className="menu-item-icon calendar-icon" />,
                label: 'Календарь',
                active: (path) => path.includes('/calendar'),
                onClick: () => {
                    navigate(`/session/calendar`);
                },
            },
            {
                id: 'budget',
                icon: <div className="menu-item-icon budget-icon" />,
                label: 'Бюджет',
                active: (path) => path.includes('/budget'),
                onClick: () => {
                    navigate(`/session/budget`);
                },
            },
            {
                id: 'workers',
                icon: <div className="menu-item-icon workers-icon" />,
                label: 'Команда',
                active: (path) => path.includes('/workers'),
                onClick: () => {
                    navigate(`/session/workers`);
                },
            },
            {
                id: 'graph',
                icon: <div className="menu-item-icon graph-icon" />,
                label: 'Сеть',
                active: (path) => path.includes('/graph'),
                onClick: () => {
                    navigate(`/session/graph`);
                },
            },
            {
                id: 'charts',
                icon: <div className="menu-item-icon charts-icon" />,
                label: 'Отчёты',
                active: (path) => path.includes('/charts'),
                onClick: () => {
                    navigate(`/session/charts`);
                },
            },
            {
                id: 'chats',
                icon: <div className="menu-item-icon chats-icon" />,
                label: 'Чаты',
                active: (path) => path.includes('/chats'),
                onClick: () => {
                    navigate(`/session/chats`);
                },
            },
            {
                id: 'home',
                icon: <FontAwesomeIcon icon={faHome} />,
                label: 'Домой',
                active: null,
                onClick: () => {
                    rootStore.ingameStore.leftMenuScrollPosition = 0;
                    navigate('/controls/assignments');
                },
            },
            {
                id: 'minimize',
                icon: (
                    <div
                        className={`menu-item-icon minimize-icon${rootStore.ingameStore.sideBarMinimized ? ' minimized' : ''}`}
                    />
                ),
                label: null,
                onClick: () => {
                    rootStore.ingameStore.sideBarMinimized =
                        !rootStore.ingameStore.sideBarMinimized;
                },
            },
        ];

        async function init() {
            message.destroy('assignment-notice');
            state.permissions = rootStore.ingameStore.getPermissions();
            if (rootStore.ingameStore.getInitStatus()) {
                state.SAI = await rootStore.ingameStore.getInfo();
            }
        }

        useEffect(() => {
            init();
        }, []);

        useEffect(() => {
            state.SAI = rootStore.ingameStore.SAI;
        }, [
            rootStore.ingameStore.SAI?.config?.state,
            rootStore.ingameStore.SAI?.config?.day,
            rootStore.ingameStore.SAI?.config?.tick,
        ]);

        async function start() {
            await rootStore.ingameStore.stateStart();
            await rootStore.ingameStore.getInfo(true);
        }

        async function pause() {
            await rootStore.ingameStore.statePause();
            await rootStore.ingameStore.getInfo(true);
        }

        async function resume() {
            await rootStore.ingameStore.stateResume();
            await rootStore.ingameStore.getInfo(true);
        }

        async function stop() {
            await rootStore.ingameStore.stateStop();
            await rootStore.ingameStore.getInfo(true);
        }

        useEffect(() => {
            const handleBeforeUnload = (event) => {
                if (rootStore.ingameStore.SAI?.config?.state != 'finished') {
                    event.preventDefault();
                    const message = 'Остались несохранённые изменения!';
                    event.returnValue = message; // For most browsers
                    return message; // For some older versions
                }
            };

            window.addEventListener('beforeunload', handleBeforeUnload);

            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        }, [rootStore.ingameStore.SAI?.config?.state]);

        async function reinitPerms() {
            if (!rootStore.ingameStore.getInitStatus()) return;
            await rootStore.ingameStore.initPermissions();
            state.permissions = rootStore.ingameStore.getPermissions();
        }

        useEffect(() => {
            reinitPerms();
        }, [
            rootStore.ingameStore.sessionAssignment,
            rootStore.currentUserStore.user,
        ]);

        return (
            <div
                id="ingame-layout"
                className={`${additionalClass != null ? additionalClass : ''}`}
            >
                <div
                    className={
                        rootStore.ingameStore.sideBarMinimized
                            ? 'left-side-bar-mini'
                            : 'left-side-bar'
                    }
                >
                    <div className="logo-outer-container">
                        <Logo
                            showText={!rootStore.ingameStore.sideBarMinimized}
                            width={rootStore.ingameStore.sideBarMinimized ? '70px' : '96px'}
                        />
                    </div>
                    <div
                        className="menu-container"
                        ref={menuGroupRef}
                        onMouseDown={onMouseDown}
                        onScroll={(e) => {
                            rootStore.ingameStore.leftMenuScrollPosition = (
                                e.nativeEvent.target as HTMLDivElement
                            ).scrollTop;
                        }}
                    >
                        {menuEntries.map((me) => (
                            <div
                                key={me.id}
                                className={
                                    me.onClick == null
                                        ? me.icon == null
                                            ? 'menu-item-blank'
                                            : 'menu-item-disabled'
                                        : me.icon == null
                                          ? 'menu-item-blank'
                                          : me.active != undefined && me.active(location.pathname)
                                            ? 'menu-item-active'
                                            : 'menu-item'
                                }
                                onClick={me.onClick == null ? () => {} : me.onClick}
                            >
                                <Tooltip
                                    placement="right"
                                    title={me.label}
                                >
                                    <div className="menu-item-inner">
                                        {me.icon != null && me.icon}
                                        {me.label != null &&
                                            !rootStore.ingameStore.sideBarMinimized && (
                                                <div className="menu-item-label p1">{me.label}</div>
                                            )}
                                    </div>
                                </Tooltip>
                            </div>
                        ))}
                    </div>
                </div>
                <div
                    className={`main-container${rootStore.ingameStore.sideBarMinimized ? ' sbm' : ''}`}
                >
                    {showTopBar && (
                        <Row className={`top-bar ${topBarText == null ? 'left-side' : 'spaced'}`}>
                            {topBarText != null && (
                                <Col>
                                    <h4>{topBarText}</h4>
                                </Col>
                            )}
                            <Col className="time-and-controls">
                                <Row>
                                    <Col className="controls">
                                        {state.SAI?.config?.state == 'prestarted' && (
                                            <Row>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Запуск прохождения"
                                                >
                                                    <Button
                                                        disabled={!state.permissions?.allowStart}
                                                        icon={<FontAwesomeIcon icon={faPlay} />}
                                                        onClick={start}
                                                    />
                                                </Tooltip>
                                            </Row>
                                        )}
                                        {(state.SAI?.config?.state == 'started' ||
                                            state.SAI?.config?.state == 'resumed') && (
                                            <Row>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Прохождение идёт"
                                                >
                                                    <Button
                                                        icon={<FontAwesomeIcon icon={faPlay} />}
                                                        type="primary"
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Поставить на паузу"
                                                >
                                                    <Button
                                                        disabled={!state.permissions?.allowPause}
                                                        icon={<FontAwesomeIcon icon={faPause} />}
                                                        onClick={pause}
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Приостановить"
                                                >
                                                    <Button
                                                        disabled={!state.permissions?.allowStop}
                                                        icon={<FontAwesomeIcon icon={faStop} />}
                                                        onClick={stop}
                                                    />
                                                </Tooltip>
                                            </Row>
                                        )}
                                        {state.SAI?.config?.state == 'paused' && (
                                            <Row>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Снять с паузы"
                                                >
                                                    <Button
                                                        disabled={!state.permissions?.allowResume}
                                                        icon={<FontAwesomeIcon icon={faPlay} />}
                                                        onClick={resume}
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="На паузе"
                                                >
                                                    <Button
                                                        icon={<FontAwesomeIcon icon={faPause} />}
                                                        type="primary"
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Приостановить"
                                                >
                                                    <Button
                                                        disabled
                                                        icon={<FontAwesomeIcon icon={faStop} />}
                                                        onClick={stop}
                                                    />
                                                </Tooltip>
                                            </Row>
                                        )}
                                        {state.SAI?.config?.state == 'stopped' && (
                                            <Row>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Возобновить"
                                                >
                                                    <Button
                                                        disabled={!state.permissions?.allowResume}
                                                        icon={<FontAwesomeIcon icon={faPlay} />}
                                                        onClick={resume}
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Поставить на паузу"
                                                >
                                                    <Button
                                                        disabled
                                                        icon={<FontAwesomeIcon icon={faPause} />}
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Остановлено"
                                                >
                                                    <Button
                                                        icon={<FontAwesomeIcon icon={faStop} />}
                                                        type="primary"
                                                    />
                                                </Tooltip>
                                            </Row>
                                        )}
                                        {state.SAI?.config?.state == 'finished' && (
                                            <Row>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Прохождение завершено"
                                                >
                                                    <Button
                                                        disabled
                                                        icon={<FontAwesomeIcon icon={faPlay} />}
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Прохождение завершено"
                                                >
                                                    <Button
                                                        disabled
                                                        icon={<FontAwesomeIcon icon={faPause} />}
                                                    />
                                                </Tooltip>
                                                <Tooltip
                                                    placement="bottom"
                                                    title="Прохождение завершено"
                                                >
                                                    <Button
                                                        icon={<FontAwesomeIcon icon={faStop} />}
                                                        type="primary"
                                                    />
                                                </Tooltip>
                                            </Row>
                                        )}
                                    </Col>
                                    <Col className="p2 time">
                                        {rootStore.ingameStore.infoDayTickToString()}
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    )}
                    <div
                        className={`child-container ${showTopBar ? '' : 'fill-top'}`}
                        style={paddingNone ? { padding: '0px' } : {}}
                    >
                        {children}
                    </div>
                </div>
            </div>
        );
    },
);

export { IngameLayout };
