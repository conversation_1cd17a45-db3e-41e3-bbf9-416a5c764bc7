import { WorkInProgress } from '@components/ui/workInProgress/workInProgress';
import { IngameLayout } from '../ingameLayout/ingameLayout';
import './sessionCalendar.scss';

const SessionCalendar = (): JSX.Element => {
    return (
        <IngameLayout
            showTopBar={true}
            topBarText="Календарь"
        >
            <WorkInProgress />
        </IngameLayout>
    );
};

export default SessionCalendar;
