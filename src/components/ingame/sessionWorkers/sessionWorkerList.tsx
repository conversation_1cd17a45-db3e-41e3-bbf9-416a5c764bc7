import { useReactive } from 'ahooks';
import { IngameLayout } from '../ingameLayout/ingameLayout';
import { TSimulation } from 'types/simulation/simulation';
import { IngameTimeSettings } from 'types/ingame';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { TSimTask } from 'types/simulation/simulationTask';
import { rootStore } from '@store/instanse';
import { useEffect } from 'react';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { Loader } from '@components/ui/loader/loader';
import { GanttUseCases } from '@components/gantt/gantt';
import { Timeline } from '@components/timeline/timeline';
import { observer } from 'mobx-react';
import { SettingsManager } from '@classes/settingsManager';
import { TSessionTaskProgress } from 'types/session/sessionTaskProgress';

import './sessionWorkerList.scss';

type TState = {
    dayTick: { day: number; tick: number } | null;
    isLoading: boolean;
    simulation: TSimulation | null;
    STP: TSessionTaskProgress[];
    tasks: TSessionTaskExtended[];
    timeSettings: IngameTimeSettings | null;
    workers: TSessionWorkerExtended[];
};

const SessionWorkerList = observer((): JSX.Element => {
    const state = useReactive<TState>({
        dayTick: null,
        isLoading: false,
        simulation: null,
        STP: [],
        tasks: [],
        timeSettings: null,
        workers: [],
    });
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();

    async function initTimeline() {
        state.isLoading = true;
        const SAinitialized = rootStore.ingameStore.getInitStatus();
        if (!SAinitialized) {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.sessionAssignmentId != null) {
                await rootStore.ingameStore.initSA(creds?.sessionAssignmentId);
                if (rootStore.ingameStore.httpState == 'error') {
                    state.isLoading = false;
                    message.error('Ошибка при инициализации, попробуйте ещё раз');
                    return;
                }
            } else {
                state.isLoading = false;
                navigate('/lk');
                message.error('Не найдено прохождение');
                return;
            }
        }
        state.dayTick = rootStore.ingameStore.getDayTick();
        state.simulation = rootStore.ingameStore.getSimulation();
        state.timeSettings = rootStore.ingameStore.getTimeSettings();
        state.STP = await rootStore.ingameStore.getSTP();
        state.tasks = await rootStore.ingameStore.getTaskList();
        state.workers = await rootStore.ingameStore.getWorkerList();
        state.isLoading = false;
    }

    useEffect(() => {
        initTimeline();
    }, [rootStore.ingameStore.SAI?.config?.state]);

    function onWorkerOpen(worker_uid: TSimWorker['uid']) {
        const worker = state.workers.find((wi) => wi.worker_uid == worker_uid);
        if (worker == undefined) {
            messageApi.error('Не удалось найти ПШЕ для перехода');
            return;
        }
        navigate(`/session/workers/${worker.worker_uid}`);
    }

    function onTaskOpen(task_uid: TSimTask['uid']) {
        const task = state.tasks.find((ti) => ti.task_uid == task_uid);
        if (task == undefined) {
            messageApi.error('Не удалось найти задачу для перехода');
            return;
        }
        navigate(`/session/tasks/${task.task_uid}`);
    }

    return (
        <IngameLayout
            paddingNone={true}
            showTopBar={true}
            topBarText="Список ПШЕ"
        >
            {contextHolder}
            {(state.isLoading || rootStore.ingameStore.httpState == 'loading') && <Loader />}
            {state.simulation != null &&
                state.timeSettings != null &&
                state.tasks.length > 0 &&
                state.workers.length > 0 && (
                    <Timeline
                        className="ingame"
                        currentDay={state.dayTick?.day}
                        daysInAWeek={state.timeSettings.daysInAWeek}
                        onTaskOpen={onTaskOpen}
                        onWorkerOpen={onWorkerOpen}
                        simulation={state.simulation}
                        STP={state.STP}
                        tasks={state.tasks}
                        useCase={GanttUseCases.Ingame}
                        weeksInSimulation={state.simulation.weeks}
                        workers={state.workers}
                    />
                )}
        </IngameLayout>
    );
});

export default SessionWorkerList;
