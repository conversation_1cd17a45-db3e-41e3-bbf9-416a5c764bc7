@use '/src/styles/colors';
@use '/src/styles/icons';

.session-worker-container {
    min-width: 892px;
    width: 100%;

    .session-worker-profile {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px;
        width: 100%;

        .session-worker-nav {
            justify-content: end;
        }
        .swp-upper {
            column-gap: 24px;
            flex-wrap: nowrap;

            .ant-row {
                align-items: center;
            }
            .swp-avatar {
                background: url('/src/assets/figma-worker-image.png');
                background-color: colors.$accentW0;
                background-repeat: no-repeat;
                background-size: contain;
                border-radius: 4px;
                height: 264px;
                width: 264px;
            }
            .swp-name h2 {
                margin: 0;
            }
            .swp-details {
                display: flex;
                flex-direction: column;
                row-gap: 12px;

                .swp-description {
                    word-break: break-word;
                }
                .swp-controls {
                    column-gap: 12px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 8px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:not(:disabled):hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                        &:disabled {
                            background: colors.$neutral25;
                            color: colors.$neutral300;
                        }
                    }
                }
            }
            .swp-knobs > .ant-row {
                column-gap: 12px;
                flex-wrap: nowrap;

                .ant-col {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .ant-progress-text {
                        color: colors.$accentW700;
                    }
                }
            }
        }
        .swp-lower {
            .ant-table-wrapper {
                width: 100%;
            }
            .ant-table-thead .ant-table-cell,
            .ant-table-row .ant-table-cell,
            .task-name-actions .task-name .ant-btn-link,
            .ant-table-thead .add-task-btn {
                color: colors.$accentW950;
                font-family: 'InterVariable';
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 18px;
            }
            .ant-table-thead,
            .ant-table-row {
                border-radius: 8px;

                .ant-table-cell {
                    border-bottom: 1px solid colors.$accentW950;
                    border-top: 1px solid colors.$accentW950;
                    padding: 20px;

                    &:not(:first-child) {
                        text-align: center;
                    }
                }
                .ant-table-cell:first-child {
                    border-left: 1px solid colors.$accentW950;
                    border-bottom-left-radius: 8px;
                    border-top-left-radius: 8px;
                }
                .ant-table-cell:last-child {
                    border-bottom-right-radius: 8px;
                    border-right: 1px solid colors.$accentW950;
                    border-top-right-radius: 8px;
                }
            }
            .ant-table-thead .ant-table-cell::before {
                display: none;
            }
            .ant-table-thead .ant-table-cell .add-task-btn {
                background: colors.$accentW0;
                border: 2px solid colors.$neutral25;
                border-radius: 8px;
                color: colors.$neutral950;
                height: 48px;

                .plus-icon {
                    @include icons.icon-plus('#1A1D24');
                    background-repeat: no-repeat;
                    background-size: contain;
                    height: 24px;
                    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                    width: 24px;
                }
                &:not(:disabled):hover {
                    background: colors.$accentW10;
                    border: 2px solid colors.$accentW10;
                    color: colors.$accentW500;

                    .plus-icon {
                        @include icons.icon-plus('#35ABFF');
                    }
                }
                &:disabled {
                    background: colors.$neutral25;
                    color: colors.$neutral300;

                    .plus-icon {
                        @include icons.icon-plus('#8F98AA');
                    }
                }
            }
            .ant-table-row {
                .ant-table-cell {
                    .task-name-actions {
                        align-items: center;
                        column-gap: 8px;
                        flex-wrap: nowrap;
                        justify-content: space-between;
                        width: 100%;

                        .task-name {
                            align-items: flex-start;
                            display: flex;
                            flex-direction: column;
                            row-gap: 4px;

                            .ant-btn-link {
                                color: #1677ff;
                                height: auto;
                                padding: 0;

                                &:hover {
                                    color: #69b1ff;
                                }
                                span {
                                    -webkit-line-clamp: 1;
                                    -webkit-box-orient: vertical;
                                    display: -webkit-box;
                                    overflow: hidden;
                                    white-space: normal;
                                    word-break: break-all;
                                }
                            }
                        }
                        .task-actions {
                            > .ant-row {
                                align-items: center;
                                column-gap: 8px;
                                flex-wrap: nowrap;

                                .action-btn {
                                    background: colors.$accentW0;
                                    border: 2px solid colors.$neutral25;
                                    border-radius: 4px;
                                    color: colors.$neutral950;
                                    height: 32px;
                                    width: 32px;

                                    .arrow-down-icon,
                                    .arrow-up-icon,
                                    .cancel-icon,
                                    .restore-icon {
                                        background-repeat: no-repeat;
                                        background-size: contain;
                                        height: 24px;
                                        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                                        width: 24px;
                                    }
                                    .arrow-down-icon {
                                        @include icons.icon-arrow('#1A1D24');
                                        transform: rotate(270deg);
                                    }
                                    .arrow-up-icon {
                                        @include icons.icon-arrow('#1A1D24');
                                        transform: rotate(90deg);
                                    }
                                    .cancel-icon {
                                        @include icons.icon-plus('#1A1D24');
                                        transform: rotate(45deg);
                                    }
                                    .restore-icon {
                                        @include icons.icon-restore('#1A1D24');
                                    }
                                    &:not(:disabled):hover {
                                        background: colors.$accentW10;
                                        border: 2px solid colors.$accentW10;
                                        color: colors.$accentW500;

                                        .arrow-down-icon {
                                            @include icons.icon-arrow('#35ABFF');
                                        }
                                        .arrow-up-icon {
                                            @include icons.icon-arrow('#35ABFF');
                                        }
                                        .cancel-icon {
                                            @include icons.icon-plus('#35ABFF');
                                        }
                                        .restore-icon {
                                            @include icons.icon-restore('#35ABFF');
                                        }
                                    }
                                    &:disabled {
                                        background: colors.$neutral25;

                                        .arrow-down-icon {
                                            @include icons.icon-arrow('#8F98AA');
                                        }
                                        .arrow-up-icon {
                                            @include icons.icon-arrow('#8F98AA');
                                        }
                                        .cancel-icon {
                                            @include icons.icon-plus('#8F98AA');
                                        }
                                        .restore-icon {
                                            @include icons.icon-restore('#8F98AA');
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                &.task-row .ant-table-cell,
                &.task-row:hover .ant-table-cell {
                    background-color: colors.$neutral100;
                }
                &.prev-task-row .ant-table-cell,
                &.task-row:hover .ant-table-cell {
                    background-color: colors.$black25;
                }
            }
            .ant-table-thead .ant-table-cell {
                background-color: colors.$accentW25;
            }
        }
    }
}
