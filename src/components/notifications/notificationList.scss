@use '/src/styles/colors';

.notification-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    width: 100%;

    .radio-group {
        padding: 24px 24px 12px 40px;
    }
    .notification-list {
        padding: 20px 40px 140px 38px;
        width: 100%;

        .ant-list-empty-text {
            color: colors.$neutral700;
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .notification-list-card {
            align-items: flex-start;
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            flex-direction: row;
            gap: 10px;
            justify-content: flex-start;
            padding: 20px;
            width: 480px;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 10px;
            }
            .header-row {
                align-items: center;
                justify-content: space-between;
            }
            .controls-row {
                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;
                    padding: 0 36px;

                    &:hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .notification-list-container {
        min-width: 334px;

        .radio-group {
            flex-wrap: wrap;
            padding: 8px 0 4px 16px;
            row-gap: 8px;
        }
        .notification-list {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            .ant-list {
                min-width: 318px;

                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 0;
                    min-width: 318px;
                }
                .notification-list-card {
                    height: 218px;
                    width: 318px;

                    .ant-btn-link {
                        padding: 0 4px;
                    }
                }
            }
        }
    }
}
