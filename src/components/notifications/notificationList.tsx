import { useReactive } from 'ahooks';
import { TNotification } from 'types/chats/notification';
import { useEffect } from 'react';
import { CRMAPIManager } from '@api/crmApiManager';
import { NotificationListResp } from '@api/responseModels/notifications/notificationListResponse';
import { useNavigate } from 'react-router-dom';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { Loader } from '@components/ui/loader/loader';
import { Button, Col, ConfigProvider, List, message, Radio, Row } from 'antd';
import { GlobalConstants } from '@classes/constants';
import { Common } from '@classes/common';
import { Permissions } from '@classes/permissions';

import './notificationList.scss';

type TState = {
    isLoading: boolean;
    mode: 'default' | 'example';
    notifications: TNotification[];
};

const NotificationList = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        mode: 'default',
        notifications: [],
    });
    const navigate = useNavigate();

    async function loadNotificationList() {
        if (!Permissions.checkPermission(Permissions.NotificationList)) {
            navigate('/lk');
            message.error('Недостаточно прав для просмотра списка уведомлений');
            return;
        }
        state.isLoading = true;
        try {
            const ntList = await CRMAPIManager.request<NotificationListResp>(async (api) => {
                return await api.getNotificationList({
                    user_id: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                });
            });
            if (ntList.errorMessages) throw ntList.errorMessages;
            state.notifications = ntList.data.data;
        } catch (errors) {
            console.log(errors);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        /*if (state.mode != "example")*/ loadNotificationList();
        /*else {
            state.notifications = [
                {
                    id: 1,
                    user_id: "fc374fb2-c3f3-4517-aa03-fd34bc780ab4",
                    chat_id: "583b0bf2-a8e1-4da4-8258-e9517af9db57",
                    text: "Пришло новое сообщение",
                    entity_type: "chat",
                    created_at: "2025-04-03T22:12:57",
                    updated_at: "2025-04-03T22:12:57",
                    entity_name: ""
                },
                {
                    id: 2,
                    user_id: "fc374fb2-c3f3-4517-aa03-fd34bc780ab4",
                    chat_id: "583b0bf2-a8e1-4da4-8258-e9517af9db57",
                    text: "Пришло новое сообщение",
                    entity_type: "chat",
                    created_at: "2025-04-03T22:12:57",
                    updated_at: "2025-04-03T22:12:57",
                    entity_name: ""
                },
                {
                    id: 3,
                    user_id: "fc374fb2-c3f3-4517-aa03-fd34bc780ab4",
                    chat_id: "583b0bf2-a8e1-4da4-8258-e9517af9db57",
                    text: "Пришло новое сообщение",
                    entity_type: "chat",
                    created_at: "2025-04-03T22:12:57",
                    updated_at: "2025-04-03T22:12:57",
                    entity_name: ""
                }
            ];
        }*/
    }, [state.mode]);

    return (
        <UniLayout
            activeTab="Уведомления"
            additionalClass="list-min-width"
            tabSet={null}
        >
            <div className="notification-list-container">
                {state.isLoading && <Loader />}
                {/*<div className="radio-group">
                    <Radio.Group 
                        onChange={(e) => state.mode = e.target.value}
                        options={[
                            { label: 'Мои', value: 'default' },
                            { label: 'Пример', value: 'example' }
                        ]}
                        optionType="button"
                        value={state.mode}
                    />
                </div>*/}
                <div className="notification-list">
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={state.notifications}
                            grid={GlobalConstants.ListGridCols}
                            itemLayout="horizontal"
                            locale={{ emptyText: 'Уведомлений нет :)' }}
                            renderItem={(item: TNotification) => (
                                <div className="notification-list-card">
                                    <Col flex={1}>
                                        <Row className="header-row p2-strong">
                                            <Col>ID: {item.id}</Col>
                                            <Col>от {Common.formatDateString(item.created_at)}</Col>
                                        </Row>
                                        {state.mode != 'default' && (
                                            <Row className="header-row p3">
                                                <Col>Получатель:</Col>
                                                <Col>
                                                    <Button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            navigate(
                                                                `/management/users/${item.user_id}`,
                                                            );
                                                        }}
                                                        type="link"
                                                    >
                                                        {item.user_id}
                                                    </Button>
                                                </Col>
                                            </Row>
                                        )}
                                        <Row className="body-row p3">{item.text}</Row>
                                        {item.entity_type == 'chat' && (
                                            <Row className="controls-row p3">
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(`/chats/${item.chat_id}`);
                                                    }}
                                                >
                                                    Перейти в чат
                                                </Button>
                                            </Row>
                                        )}
                                    </Col>
                                </div>
                            )}
                        />
                    </ConfigProvider>
                </div>
            </div>
        </UniLayout>
    );
};

export default NotificationList;
