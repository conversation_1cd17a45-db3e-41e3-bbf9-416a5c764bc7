@use '/src/styles/colors';
@use '/src/styles/icons';

.invitation-profile-container {
    width: 100%;

    .invitation-profile {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 40px;
        width: 100%;

        > .ant-col {
            display: flex;
            flex-direction: column;
            row-gap: 20px;
        }

        .top-row {
            h3 {
                margin: 0;
            }
        }
        .filter-row {
            align-items: center;
            column-gap: 16px;
            row-gap: 8px;

            .filter-change-btn {
                background: colors.$accentW0;
                border: 2px solid colors.$neutral25;
                border-radius: 4px;
                color: colors.$neutral950;
                height: 32px;
                width: 32px;

                .plus-icon {
                    @include icons.icon-plus('#1A1D24');
                    background-repeat: no-repeat;
                    background-size: contain;
                    height: 24px;
                    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                    width: 24px;
                }
                &:not(:disabled):hover {
                    background: colors.$accentW10;
                    border: 2px solid colors.$accentW10;
                    color: colors.$accentW500;

                    .plus-icon {
                        @include icons.icon-plus('#35ABFF');
                    }
                }
                &:disabled {
                    background: colors.$neutral25;

                    .plus-icon {
                        @include icons.icon-plus('#8F98AA');
                    }
                }
            }
        }
        .inv-form {
            display: flex;
            flex-direction: column;
            row-gap: 8px;

            .role-row {
                align-items: center;
                column-gap: 8px;

                &:has(.ant-form-item) {
                    align-items: flex-start;

                    > .ant-col:first-child {
                        padding: 4px 0;
                    }
                }
            }
            .ant-btn-link {
                padding: 0;
            }
            .input-row > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .ant-form-item {
                    width: 100%;
                }
            }
            .controls-row {
                column-gap: 24px;
                row-gap: 8px;

                .ant-btn {
                    background: colors.$accentW0;
                    border-radius: 4px;
                    border: 2px solid colors.$neutral25;
                    color: colors.$neutral950;
                    height: 48px;

                    &:disabled {
                        background: colors.$neutral25;
                        color: colors.$neutral300;
                    }
                    &:not(:disabled):hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .invitation-profile-container {
        .invitation-profile {
            padding: 16px;

            .ant-btn-link span {
                font-size: 14px;
                line-height: 20px;
            }
        }
    }
}
