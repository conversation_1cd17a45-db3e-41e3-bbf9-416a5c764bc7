import { useEffect } from 'react';
import { useRouteError } from 'react-router-dom';
import * as Sentry from '@sentry/react';

export function YourCustomRootErrorBoundary() {
    const error = useRouteError() as Error;

    console.log(Sentry.captureMessage('test'));
    useEffect(() => {
        Sentry.captureException(error);
    }, [error]);

    return (
        <div>
            <h1>Ouch!</h1>
        </div>
    );
}
