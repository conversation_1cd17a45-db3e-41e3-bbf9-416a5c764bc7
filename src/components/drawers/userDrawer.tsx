import { CRMAPIManager } from '@api/crmApiManager';
import { UserListResp } from '@api/responseModels/users/userListResp';
import { faCheck, faPlus, faXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useReactive } from 'ahooks';
import { Button, Checkbox, Col, Drawer, Dropdown, List, message, Row, Select } from 'antd';
import { useCallback, useEffect } from 'react';
import { TUser } from 'types/user/user';
import { TFilter } from 'types/filter';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { Common } from '@classes/common';
import Search from 'antd/es/input/Search';
import Colors from '@classes/colors';

import './drawers.scss';

export enum UserDrawerUseCase {
    All = 'all',
    Clients = 'clients',
    Staff = 'staff',
}

type TProps = {
    filters?: TFilter[];
    isOpen: boolean;
    multiple?: boolean;
    onConfirm?: () => void;
    onSelect: (users: TUser['id'][]) => void;
    selected: TUser['id'][];
    setIsOpen?: (isOpen: boolean) => void;
    title?: string;
    useCase: UserDrawerUseCase;
    userList?: TUser[];
};

type TState = {
    filters: TFilter[];
    isLoading: boolean;
    selectedFilters: TFilter['id'][];
    users: TUser[];
    query: string;
};

const UserDrawer = ({
    isOpen,
    filters,
    multiple = false,
    onConfirm,
    onSelect,
    selected,
    setIsOpen,
    title,
    useCase,
    userList,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        filters: [],
        isLoading: false,
        selectedFilters: [],
        users: [],
        query: '',
    });
    const [messageApi, contextHolder] = message.useMessage();

    async function loadUsers() {
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<UserListResp>(async (api) => {
                return await api.getUserList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    role: useCase == 'all' ? null : useCase,
                });
            });
            state.users = result.data.data;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка пользователей');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.request<FilterListResp>(async (api) => {
                return await api.getFilterList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data.filter((f) => f.target == 'users');
        } catch (errors) {
            messageApi.error('Ошибка при получении списка фильтров');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function initUserDrawer() {
        state.query = '';
        if (userList != undefined) {
            state.users = userList;
        } else {
            await loadUsers();
        }
        if (filters != undefined) {
            state.filters = filters.filter((f) => f.target == 'users');
        } else {
            await loadFilters();
        }
    }

    useEffect(() => {
        initUserDrawer();
    }, [useCase]);

    useEffect(() => {
        if (!isOpen) state.query = '';
    }, [isOpen]);

    function onClose() {
        setIsOpen(false);
    }

    const filterUserList = useCallback(() => {
        return state.users.filter((u) => {
            let conclusion = true;
            if (state.selectedFilters.length != 0) {
                for (let i = 0; i < state.selectedFilters.length; i++) {
                    if (u.filters.find((f) => f.id == state.selectedFilters[i]) == undefined) {
                        conclusion = false;
                    }
                }
            }
            if (conclusion && !Common.isNullOrEmptyString(state.query)) {
                if (!u.name.toLocaleLowerCase().includes(state.query.toLocaleLowerCase())) {
                    conclusion = false;
                }
            }
            return conclusion;
        });
    }, [
        state.users,
        state.query,
        state.selectedFilters,
    ]);

    function makeFilterSelectOptions() {
        return state.filters.map((fi) => {
            return {
                label: (
                    <FilterButton
                        hex={fi.color_hex}
                        text={fi.name}
                    />
                ),
                value: fi.id,
            };
        });
    }

    const allChecked = useCallback((): 'all' | 'some' | 'none' => {
        let conclusion: 'all' | 'some' | 'none' = 'none';
        const filteredUsers = filterUserList();
        const tempSel = selected;
        let collisions = 0;
        for (let i = 0; i < filteredUsers.length; i++) {
            if (tempSel.includes(filteredUsers[i].id)) {
                collisions += 1;
            }
        }
        if (collisions == filteredUsers.length) {
            conclusion = 'all';
        } else if (collisions > 0 && collisions < filteredUsers.length) {
            conclusion = 'some';
        } else {
            conclusion = 'none';
        }
        return conclusion;
    }, [
        state.users,
        state.query,
        state.selectedFilters,
        selected,
    ]);

    function checkChange(value: boolean) {
        if (value) {
            onSelect(filterUserList().map((u) => u.id));
        } else {
            onSelect([]);
        }
    }

    function filterClick(id: TFilter['id']) {
        if (!state.selectedFilters.includes(id)) {
            state.selectedFilters = [...state.selectedFilters, id];
        }
    }

    return (
        <Drawer
            className="template-drawer user-drawer"
            closeIcon={null}
            open={isOpen}
            onClose={onClose}
            placement="right"
            title={
                <Row className="drawer-header">
                    <Col>
                        <h4>{title == undefined ? 'Назначить клиенту' : title}</h4>
                    </Col>
                    <Col>
                        <Button
                            icon={<FontAwesomeIcon icon={faXmark} />}
                            onClick={onClose}
                        />
                    </Col>
                    {onConfirm != undefined && (
                        <Col className="p3">
                            <Button
                                disabled={selected.length == 0}
                                onClick={onConfirm}
                                type="primary"
                            >
                                OK
                            </Button>
                        </Col>
                    )}
                </Row>
            }
            width={'auto'}
        >
            {contextHolder}
            <Col>
                <Row className="drawer-search-row">
                    <Search
                        onChange={(e) => (state.query = e.target.value)}
                        placeholder={
                            useCase == UserDrawerUseCase.All
                                ? 'Имя пользователя'
                                : useCase == UserDrawerUseCase.Clients
                                  ? 'Имя клиента'
                                  : 'Имя сотрудника'
                        }
                        value={state.query}
                    />
                </Row>
                {state.filters.length > 0 && (
                    <Row className="drawer-filter-row">
                        <Select
                            allowClear
                            filterOption={(
                                input: string,
                                option: {
                                    label: React.ReactNode;
                                    value: number;
                                },
                            ) => {
                                const filter = state.filters.find((fi) => fi.id == option.value);
                                return filter.name
                                    .toLocaleLowerCase()
                                    .includes(input.toLocaleLowerCase());
                            }}
                            filterSort={(a, b) => {
                                const filterA = state.filters.find((fi) => fi.id == a.value);
                                const filterB = state.filters.find((fi) => fi.id == b.value);
                                return filterA?.name.localeCompare(filterB?.name);
                            }}
                            mode="multiple"
                            notFoundContent={
                                <Col
                                    className="empty-text p3"
                                    flex={1}
                                >
                                    <Row style={{ justifyContent: 'center' }}>
                                        Таких фильтров нет :)
                                    </Row>
                                </Col>
                            }
                            onClear={() => (state.selectedFilters = [])}
                            onDeselect={(value) => {
                                state.selectedFilters = state.selectedFilters.filter(
                                    (sfi) => sfi != value,
                                );
                            }}
                            onSelect={(value) => {
                                state.selectedFilters = [...state.selectedFilters, value];
                            }}
                            options={makeFilterSelectOptions()}
                            placeholder="Выберите фильтр"
                            value={state.selectedFilters}
                        />
                    </Row>
                )}
                {multiple && (
                    <Row className="drawer-select-row">
                        <Col>
                            <Checkbox
                                checked={allChecked() == 'all'}
                                indeterminate={allChecked() == 'some'}
                                onChange={(e) => checkChange(e.target.checked)}
                            />
                        </Col>
                        <Col>
                            <span className="p3">
                                {selected.length} выбрано, {filterUserList().length}/
                                {state.users.length} видно
                            </span>
                        </Col>
                    </Row>
                )}
                <Row className="drawer-list">
                    <List
                        dataSource={filterUserList()}
                        itemLayout="vertical"
                        locale={{
                            emptyText: (
                                <Col
                                    className="empty-text p3"
                                    flex={1}
                                >
                                    <Row style={{ justifyContent: 'center' }}>
                                        Таких пользователей нет :)
                                    </Row>
                                </Col>
                            ),
                        }}
                        renderItem={(item) => (
                            <Row
                                key={item.id}
                                className="drawer-card user-drawer-list"
                            >
                                <Col>
                                    <div className="drawer-card-avatar" />
                                </Col>
                                <Col className="user-card-main-col">
                                    <Row>
                                        <Col>
                                            <h5 className="bold">{item.name ?? 'Без имени'}</h5>
                                            {/*<div className="desc-l">ID: {item.id}</div>*/}
                                        </Col>
                                    </Row>
                                    {!Common.isNullOrEmptyString(item.description) && (
                                        <Row>
                                            <div className="desc-m">{item.description}</div>
                                        </Row>
                                    )}
                                    <Row className="filter-row">
                                        {item.filters.slice(0, 3).map((f) => (
                                            <Col
                                                key={f.id}
                                                onClick={() => filterClick(f.id)}
                                            >
                                                <FilterButton
                                                    hex={f.color_hex}
                                                    text={f.name}
                                                />
                                            </Col>
                                        ))}
                                        {item.filters.length > 3 && (
                                            <Dropdown
                                                menu={{
                                                    items: item.filters
                                                        .slice(3, item.filters.length)
                                                        .map((f) => {
                                                            return {
                                                                key: f.id,
                                                                label: (
                                                                    <Col
                                                                        key={f.id}
                                                                        onClick={() =>
                                                                            filterClick(f.id)
                                                                        }
                                                                    >
                                                                        <FilterButton
                                                                            hex={f.color_hex}
                                                                            text={f.name}
                                                                        />
                                                                    </Col>
                                                                ),
                                                            };
                                                        }),
                                                }}
                                                trigger={['hover', 'click']}
                                            >
                                                <Row>
                                                    <FilterButton
                                                        key="more-filters"
                                                        hex={Colors.Neutral[50]}
                                                        text={`+${item.filters.length - 3}`}
                                                    />
                                                </Row>
                                            </Dropdown>
                                        )}
                                    </Row>
                                </Col>
                                <Col className="drawer-card-select-col">
                                    <Button
                                        icon={
                                            <FontAwesomeIcon
                                                icon={selected.includes(item.id) ? faCheck : faPlus}
                                            />
                                        }
                                        onClick={() => {
                                            if (multiple) {
                                                onSelect(
                                                    selected.includes(item.id)
                                                        ? selected.filter((si) => si != item.id)
                                                        : [...selected, item.id],
                                                );
                                            } else {
                                                onSelect(
                                                    selected.includes(item.id) ? [] : [item.id],
                                                );
                                            }
                                        }}
                                        type={selected.includes(item.id) ? 'primary' : 'default'}
                                    />
                                </Col>
                            </Row>
                        )}
                    />
                </Row>
            </Col>
        </Drawer>
    );
};

export default UserDrawer;
