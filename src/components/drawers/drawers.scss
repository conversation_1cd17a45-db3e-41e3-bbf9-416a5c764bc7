@use '/src/styles/colors';
@use '/src/styles/icons';

.ant-drawer-content-wrapper {
    //width: auto !important;
}

.template-drawer {
    min-width: 360px;

    .drawer-header {
        align-items: center;
        justify-content: space-between;

        h4 {
            margin: 0;
        }
        .ant-col:has(.ant-btn-icon-only) {
            display: none;
        }
    }
    .ant-drawer-body {
        > .ant-col {
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
            row-gap: 16px;

            .drawer-search-row {
            }
            .drawer-filter-row {
                .ant-select {
                    width: 100%;
                }
            }
            .drawer-select-row {
                align-items: center;
                column-gap: 8px;
            }
            .drawer-create-row {
                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;
                    width: 100%;

                    .plus-icon {
                        @include icons.icon-plus('#1A1D24');
                        background-repeat: no-repeat;
                        background-size: contain;
                        height: 32px;
                        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                        width: 32px;
                    }

                    &:hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;

                        .plus-icon {
                            @include icons.icon-plus('#35ABFF');
                        }
                    }
                }
            }
        }
        .drawer-list .ant-list {
            background: colors.$neutral25;
            padding: 10px;
            width: 100%;

            .empty-text {
                color: colors.$neutral950;
            }
            .drawer-card {
                align-items: center;
                background: colors.$accentW0;
                column-gap: 10px;
                padding: 16px;

                .drawer-card-avatar {
                    background: url('/src/assets/worker_image.png');
                    background-color: colors.$accentW0;
                    background-repeat: no-repeat;
                    background-size: contain;
                    border-radius: 5px;
                    height: 106px;
                    width: 106px;
                }
                h5 {
                    margin: 0;
                    &.bold {
                        font-weight: 700;
                    }
                    &:not(.bold) {
                        font-weight: 400;
                    }
                }
            }
        }
    }
}

.user-drawer {
    .drawer-filter-row {
        .ant-select-selection-item {
            align-items: center;
            height: 36px;

            .filter-btn:hover {
                box-shadow: none;
            }
        }
    }
    .drawer-list .ant-list {
        .ant-list-items {
            display: flex;
            flex-direction: column;
            row-gap: 10px;

            .user-drawer-list {
                flex-wrap: nowrap;

                .user-card-main-col {
                    display: flex;
                    flex-direction: column;
                    flex-wrap: nowrap;
                    row-gap: 12px;

                    .filter-row {
                        column-gap: 8px;
                        flex-wrap: wrap;
                        row-gap: 8px;
                    }
                }
                .drawer-card-select-col {
                    margin-left: auto;
                }
                .ant-list-items {
                    display: flex;
                    flex-direction: column;
                    row-gap: 10px;
                }
            }
        }
    }
}

body:has(.user-drawer) .ant-select-dropdown .empty-text {
    color: colors.$neutral950;
}

.sim-worker-drawer {
    .ant-drawer-body > .ant-col {
        display: flex;
        flex-direction: column;
        max-height: 100%;
        min-height: 100%;
        overflow: hidden;
        row-gap: 8px;

        .task-info {
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .task-name {
                    align-items: center;
                    justify-content: space-between;

                    .p2-strong {
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        min-height: 24px;
                        max-height: 24px;
                        max-width: 460px;
                        overflow: hidden;
                        white-space: normal;
                        word-break: break-all;
                    }
                }
                .task-dur-workers {
                    column-gap: 8px;

                    > .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                        width: calc(50% - 4px);
                    }
                    .labeled-input {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;

                        .lighter-tone {
                            color: colors.$neutral700;
                        }
                    }
                }
                .task-requirements .ant-table {
                    width: 536px;
                    th,
                    td {
                        font-family: 'InterVariable';
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 16px;
                        word-break: break-space;
                    }
                }
            }
        }
        .drawer-list {
            overflow-y: scroll;

            .ant-list-items {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .sim-worker-card .sim-worker-card-main-col {
                display: flex;
                flex-direction: column;
                row-gap: 4px;

                > .ant-row {
                    align-items: flex-start;
                    column-gap: 4px;
                    justify-content: space-between;

                    .sim-worker-name {
                        align-items: center;
                        column-gap: 4px;
                    }
                    .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 4px;

                        &:last-child {
                            align-items: flex-end;
                        }
                    }
                }
                .skills-table {
                    width: 360px;
                    th,
                    td {
                        font-family: 'InterVariable';
                        font-size: 10px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 12px;
                        word-break: break-all;
                    }
                }
            }
        }
    }
}

.sim-task-drawer {
    .ant-drawer-body > .ant-col {
        display: flex;
        flex-direction: column;
        max-height: 100%;
        min-height: 100%;
        overflow: hidden;
        row-gap: 8px;

        .worker-info {
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .worker-avatar-name {
                    align-items: center;
                    justify-content: space-between;

                    > .ant-col > .ant-row {
                        align-items: center;
                        cursor: pointer;
                        column-gap: 8px;
                        flex-wrap: nowrap;
                        width: 100%;

                        .worker-avatar {
                            align-items: center;
                            background-color: colors.$accentC50;
                            border-radius: 50%;
                            color: colors.$neutral900;
                            display: flex;
                            flex-direction: column;
                            height: 32px;
                            justify-content: center;
                            min-width: 32px;
                            max-width: 32px;
                        }
                    }
                }
                .worker-rate-percentage {
                    column-gap: 8px;

                    > .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                        width: calc(50% - 4px);
                    }
                    .labeled-input {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;

                        .lighter-tone {
                            color: colors.$neutral700;
                        }
                    }
                }
                .worker-stats .ant-table {
                    width: 536px;
                    th,
                    td {
                        font-family: 'InterVariable';
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 16px;
                        word-break: break-space;
                    }
                }
            }
        }
        .drawer-list {
            overflow-y: scroll;

            .ant-list-items {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .sim-task-card > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 4px;
                width: 100%;

                > .ant-row {
                    align-items: flex-start;
                    column-gap: 4px;
                    justify-content: space-between;

                    .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 4px;

                        &:last-child {
                            align-items: flex-end;
                        }
                    }
                }
                .requirements-table {
                    width: 480px;
                    th,
                    td {
                        font-family: 'InterVariable';
                        font-size: 10px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 12px;
                        word-break: break-space;
                    }
                }
            }
        }
    }
}

.filter-drawer {
    .filter-create-col {
        .labeled-input {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
            width: 100%;

            .ant-form-item {
                width: 100%;
            }
        }
    }
    .ant-list {
        .ant-list-items {
            display: flex;
            flex-direction: column;
            row-gap: 10px;

            .filter-list-card {
                justify-content: space-between;

                .filter-card-main-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 4px;
                }
            }
        }
    }
}

@media only screen and (orientation: portrait) {
    .template-drawer {
        .drawer-header {
            .ant-col:has(.ant-btn-icon-only) {
                display: flex;
                flex-direction: column;
            }
        }
    }
}
