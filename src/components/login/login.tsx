import Logo from '@components/ui/logo/logo';
import { useReactive } from 'ahooks';
import { useNavigate } from 'react-router-dom';
import { Button, Col, Form, Input, InputRef, Row, Tabs } from 'antd';
import useMessage from 'antd/es/message/useMessage';
import { useRef } from 'react';
import { SettingsManager } from '@classes/settingsManager';
import { CRMAPI } from '@api/crmApi';
import { rootStore } from '@store/instanse';

import './login.scss';

type TState = {
    isLoading: boolean;
};

const Login = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
    });
    const [form] = Form.useForm();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = useMessage();
    const loginPasswordRef = useRef<InputRef>(null);
    const registerLoginRef = useRef<InputRef>(null);
    const registerPasswordRef = useRef<InputRef>(null);
    const recoveryLoginRef = useRef<InputRef>(null);
    // https://stackoverflow.com/questions/28555114/regexp-for-login
    const loginMask = /^(?=.*[A-Za-z0-9]$)[A-Za-z][A-Za-z\d]{2,15}$/;
    // https://stackoverflow.com/questions/46155/how-can-i-validate-an-email-address-in-javascript
    const emailMask = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    // https://stackoverflow.com/questions/12090077/javascript-regular-expression-password-validation-having-special-characters
    const passwordMask = /^(?=.*[0-9])[a-zA-Z0-9]{8,24}$/;

    async function wrapper(func: () => Promise<void>) {
        messageApi.loading('Отправляем запрос...', 0);
        state.isLoading = true;
        try {
            await func();
        } catch (err) {
            state.isLoading = false;
            messageApi.destroy();
            messageApi.error(typeof err == 'object' ? err[0] : 'Произошла ошибка');
            console.log(err);
        }
    }

    async function loginSubmit(values: { login: string; password: string }) {
        if (state.isLoading) {
            return;
        }
        wrapper(async () => {
            const api = new CRMAPI();
            const result = await api.login(values.login, values.password);
            if (result.errorMessages) throw result.errorMessages;
            SettingsManager.updateConnectionCredentials({
                accessToken: result.data.data.access_token,
                refreshToken: result.data.data.refresh_token,
                user_id: result.data.data.user.id,
            });
            rootStore.currentUserStore.setUser(result.data.data.user);
            state.isLoading = false;
            messageApi.destroy();
            navigate('/lk');
        });
    }

    async function registerSubmit(values: { email: string; login: string; password: string }) {
        if (state.isLoading) {
            return;
        }
        wrapper(async () => {
            const api = new CRMAPI();
            const result = await api.register(values.email, values.login, values.password);
            if (result.errorMessages) throw result.errorMessages;
            state.isLoading = false;
            messageApi.destroy();
            messageApi.success('На ваш email отправлено письмо');
        });
    }

    async function recoverySubmit(values: { email: string; login: string }) {
        if (state.isLoading) {
            return;
        }
        wrapper(async () => {
            const api = new CRMAPI();
            const result = await api.recovery(values.email, values.login);
            if (result.errorMessages) throw result.errorMessages;
            state.isLoading = false;
            messageApi.destroy();
            messageApi.success('На ваш email отправлено письмо');
        });
    }

    const tabs = [
        {
            label: 'Вход',
            key: 'login',
            children: (
                <Form
                    className="login-tab-content"
                    form={form}
                    onFinish={loginSubmit}
                >
                    <Row>
                        <Form.Item
                            name="login"
                            rules={[
                                {
                                    required: true,
                                    message: 'Пожалуйста, введите логин',
                                },
                                {
                                    pattern: loginMask,
                                    message:
                                        'От 3 до 16 символов, латиница и цифры, первый - буква',
                                },
                            ]}
                        >
                            <Input
                                autoComplete="login username"
                                disabled={state.isLoading}
                                id="username"
                                maxLength={16}
                                onPressEnter={() => {
                                    if (loginPasswordRef.current != null) {
                                        loginPasswordRef.current.focus({ cursor: 'start' });
                                    }
                                }}
                                placeholder="Введите логин"
                                size="large"
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item
                            name="password"
                            rules={[
                                {
                                    required: true,
                                    message: 'Пожалуйста, введите пароль',
                                },
                                {
                                    pattern: passwordMask,
                                    message: 'От 8 до 24 символов, латиница, минимум 1 цифра',
                                },
                            ]}
                        >
                            <Input.Password
                                autoComplete="current-password"
                                disabled={state.isLoading}
                                id="current-password"
                                maxLength={24}
                                placeholder="Введите пароль"
                                ref={loginPasswordRef}
                                size="large"
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item>
                            <Button
                                className="login-tab-submit p3"
                                disabled={state.isLoading}
                                htmlType="submit"
                                loading={state.isLoading}
                            >
                                Войти
                            </Button>
                        </Form.Item>
                    </Row>
                </Form>
            ),
        },
        {
            label: 'Регистрация',
            key: 'register',
            children: (
                <Form
                    className="login-tab-content"
                    form={form}
                    onFinish={registerSubmit}
                >
                    <Row>
                        <Form.Item
                            name="email"
                            rules={[
                                {
                                    required: true,
                                    message: 'Пожалуйста, введите email',
                                },
                                {
                                    type: 'email',
                                    max: 40,
                                    min: 5,
                                    pattern: emailMask,
                                    message: 'От 5 до 40 символов, email-формат',
                                },
                            ]}
                        >
                            <Input
                                autoComplete="email"
                                disabled={state.isLoading}
                                id="email"
                                maxLength={40}
                                onPressEnter={() => {
                                    if (registerLoginRef.current != null) {
                                        registerLoginRef.current.focus({ cursor: 'start' });
                                    }
                                }}
                                placeholder="Введите email"
                                size="large"
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item
                            name="login"
                            rules={[
                                {
                                    required: true,
                                    message: 'Пожалуйста, введите логин',
                                },
                                {
                                    pattern: loginMask,
                                    message:
                                        'От 3 до 16 символов, латиница и цифры, первый - буква',
                                },
                            ]}
                        >
                            <Input
                                autoComplete="username"
                                disabled={state.isLoading}
                                id="username"
                                maxLength={16}
                                onPressEnter={() => {
                                    if (registerPasswordRef.current != null) {
                                        registerPasswordRef.current.focus({ cursor: 'start' });
                                    }
                                }}
                                placeholder="Введите логин"
                                ref={registerLoginRef}
                                size="large"
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item
                            name="password"
                            rules={[
                                {
                                    required: true,
                                    message: 'Пожалуйста, введите пароль',
                                },
                                {
                                    pattern: passwordMask,
                                    message: 'От 8 до 24 символов, латиница, минимум 1 цифра',
                                },
                            ]}
                        >
                            <Input.Password
                                autoComplete="new-password"
                                disabled={state.isLoading}
                                id="new-password"
                                maxLength={24}
                                placeholder="Введите пароль"
                                ref={registerPasswordRef}
                                size="large"
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item>
                            <Button
                                className="login-tab-submit p3"
                                disabled={state.isLoading}
                                htmlType="submit"
                                loading={state.isLoading}
                            >
                                Зарегистрироваться
                            </Button>
                        </Form.Item>
                    </Row>
                </Form>
            ),
        },
        {
            label: 'Восстановление доступа',
            key: 'recovery',
            children: (
                <Form
                    className="login-tab-content"
                    form={form}
                    onFinish={recoverySubmit}
                >
                    <Row>
                        <Form.Item
                            name="email"
                            rules={[
                                {
                                    required: true,
                                    message: 'Пожалуйста, введите email',
                                },
                                {
                                    type: 'email',
                                    max: 40,
                                    min: 5,
                                    pattern: emailMask,
                                    message: 'От 5 до 40 символов, email-формат',
                                },
                            ]}
                        >
                            <Input
                                autoComplete="email"
                                disabled={state.isLoading}
                                id="email"
                                maxLength={40}
                                onPressEnter={() => {
                                    if (recoveryLoginRef.current != null) {
                                        recoveryLoginRef.current.focus({ cursor: 'start' });
                                    }
                                }}
                                placeholder="Введите email"
                                size="large"
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item
                            name="login"
                            rules={[
                                {
                                    required: true,
                                    message: 'Пожалуйста, введите логин',
                                },
                                {
                                    pattern: loginMask,
                                    message:
                                        'От 3 до 16 символов, латиница и цифры, первый - буква',
                                },
                            ]}
                        >
                            <Input
                                autoComplete="username"
                                disabled={state.isLoading}
                                id="username"
                                maxLength={16}
                                placeholder="Введите логин"
                                ref={recoveryLoginRef}
                                size="large"
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item>
                            <Button
                                className="login-tab-submit p3"
                                disabled={state.isLoading}
                                htmlType="submit"
                                loading={state.isLoading}
                            >
                                Восстановить доступ
                            </Button>
                        </Form.Item>
                    </Row>
                </Form>
            ),
        },
    ];

    return (
        <div className="login-container">
            {contextHolder}
            <Row className="login-main-row">
                <Col className="logo-column">
                    <Logo />
                </Col>
                <Col>
                    <Tabs
                        className="p3"
                        defaultActiveKey="login"
                        items={tabs}
                        type="card"
                    />
                </Col>
            </Row>
        </div>
    );
};

export default Login;
