import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { IngameTimeSettings } from 'types/ingame';
import { TSimulation } from 'types/simulation/simulation';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { GanttNodeInner, GanttUseCases, TSimTaskGanttExtend } from '@components/gantt/gantt';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import timelineNode from './timelineNode';
import {
    CoordinateExtent,
    Node,
    ReactFlow,
    ReactFlowProvider,
    useNodesState,
    useViewport,
} from '@xyflow/react';
import { useReactive } from 'ahooks';
import { Button, Checkbox, Col, Row } from 'antd';
import { useEffect, useRef } from 'react';
import { DefaultTimeSettings } from '@store/ingame/data';
import Colors from '@classes/colors';
import _ from 'lodash';
import { TaskUtils } from '@classes/taskUtitlty';
import CustomBackground from '@components/gantt/background';
import { TSessionTaskProgress } from 'types/session/sessionTaskProgress';

import '@xyflow/react/dist/style.css';
import './timeline.scss';

type TProps = {
    className?: string;
    currentDay?: number;
    daysInAWeek: IngameTimeSettings['daysInAWeek'];
    onTaskOpen: (task_uid: TSimTask['uid']) => void;
    onWorkerOpen: (worker_uid: TSimWorker['uid']) => void;
    simulation: TSimulation | null;
    STP: TSessionTaskProgress[];
    tasks: TSimTaskGanttExtend[] | TSessionTaskExtended[];
    useCase: GanttUseCases;
    weeksInSimulation: TSimulation['weeks'];
    workers: TSimWorker[] | TSessionWorkerExtended[];
};

const nodeTypes = { timelineNode: timelineNode };

type TimelineNode = Node<
    GanttNodeInner & {
        onTaskClick: (task_uid: TSimTask['uid']) => void | null;
        type: 'task' | 'free';
    }
>;

type TState = {
    tempTasks: GanttNodeInner[];
};

const TimelineContent = ({
    className,
    currentDay,
    daysInAWeek,
    onTaskOpen,
    onWorkerOpen,
    simulation,
    STP,
    tasks,
    useCase,
    weeksInSimulation,
    workers,
}: TProps): JSX.Element => {
    const state = useReactive<TState>({
        tempTasks: [],
    });
    const [
        nodes,
        setNodes,
    ] = useNodesState<TimelineNode>([]);
    const { x, y, zoom } = useViewport();
    const timelineColRef = useRef<HTMLDivElement>(null);
    const nodeHeight = 28;
    const rowPadding = 8;
    const widthPerDay = 32;
    const nodeStartX = widthPerDay * 4;
    const nodeStartY = nodeHeight + rowPadding;

    function workersToNodes() {
        let nodes: TimelineNode[] = [];

        // Перебор ПШЕ
        if (useCase == GanttUseCases.Constructor) {
            state.tempTasks = [];
        } else {
            const tt = TaskUtils.tasksToTimeline(
                daysInAWeek,
                DefaultTimeSettings.workDayHours - +DefaultTimeSettings.workDayLunchSkip,
                simulation,
                STP,
                tasks,
                workers,
            ).map((tti) => {
                return {
                    ...tti,
                    onTaskClick: onTaskOpen,
                };
            });

            state.tempTasks = tt;
            nodes = tt.map((tti, i) => {
                return {
                    id: `${tti.id}:${tti.uid}:${i}`,
                    position: {
                        x: nodeStartX + tti.column * widthPerDay,
                        y:
                            nodeStartY +
                            tti.row * nodeHeight +
                            (tti.row > 0 ? tti.row : 0) * rowPadding,
                    },
                    style: {
                        width: tti.curDuration * widthPerDay,
                    },
                    data: {
                        ...tti,
                    },
                    type: 'timelineNode',
                };
            });
            setNodes(nodes);
        }
    }

    useEffect(() => {
        if (simulation == null) return;
        workersToNodes();
    }, [
        simulation,
        tasks,
        workers,
        useCase,
    ]);

    function makeTimelineBoundaries() {
        const yStart = 0;
        const rowNum = workers.length == 0 ? 1 : workers.length;
        const yEnd = yStart + nodeStartY + rowNum * nodeHeight + (rowNum - 1) * rowPadding;
        const xStart = 0;
        const days = weeksInSimulation * DefaultTimeSettings.daysInAWeek;
        const xEnd = xStart + nodeStartX + days * widthPerDay;
        if (timelineColRef == null) {
            return [
                [xStart, yEnd],
                [xEnd, yStart],
            ] as CoordinateExtent;
        } else {
            return [
                [
                    xStart,
                    yEnd < timelineColRef?.current?.clientHeight
                        ? timelineColRef?.current?.clientHeight
                        : yEnd,
                ],
                [
                    xEnd < timelineColRef?.current?.clientWidth
                        ? timelineColRef?.current?.clientWidth
                        : xEnd,
                    yStart,
                ],
            ] as CoordinateExtent;
        }
    }

    function makeDefaultViewport() {
        const gb = makeTimelineBoundaries();
        const yMiddle = Math.ceil((gb[1][1] + gb[0][1]) / 2);
        return { x: 0, y: yMiddle, zoom: 1 };
    }

    return (
        <div className={`timeline-container ${className}`}>
            <Row>
                <Col
                    className="timeline-flow-col full-width"
                    ref={timelineColRef}
                >
                    {simulation == null ||
                        (workers.length == 0 && (
                            <div className="no-sim-or-workers">
                                <span className="p3">Нет симуляции или ПШЕ</span>
                            </div>
                        ))}
                    {simulation != null && nodes.length > 0 && (
                        <ReactFlow
                            className="timeline-flow"
                            nodes={nodes}
                            edges={[]}
                            nodeTypes={nodeTypes}
                            proOptions={{ hideAttribution: true }}
                            /// Настроечки
                            // Настройки стартовой позиции и зума вью-порта
                            defaultViewport={makeDefaultViewport()}
                            // Запрет удаления
                            deleteKeyCode={null}
                            // Не фокусить связи
                            edgesFocusable={false}
                            // Отруб подсветки
                            elevateEdgesOnSelect={false}
                            // Отключение автозума
                            fitView={false}
                            // Ограничение зума
                            maxZoom={1}
                            minZoom={1}
                            // Запрет мультивыбора
                            multiSelectionKeyCode={null}
                            // Не соединять узлы
                            nodesConnectable={false}
                            // Не перемещать узлы
                            nodesDraggable={false}
                            // Не фокусить узлы
                            nodesFocusable={false}
                            // Рендер только видимых элементов
                            onlyRenderVisibleElements={true}
                            // Перемещение скроллом
                            panOnScroll={true}
                            // Снятие запрета на скролл
                            preventScrolling={false}
                            // Запрет выбора
                            selectionKeyCode={null}
                            // Задание границ вью-порта
                            translateExtent={makeTimelineBoundaries()}
                            // Отключение зума двойным кликом
                            zoomOnDoubleClick={false}
                            // Отключение зума на тач-девайсе
                            zoomOnPinch={false}
                            // Отключение зума скроллом
                            zoomOnScroll={false}
                        >
                            <CustomBackground
                                baseColor={Colors.Neutral[0]}
                                daysInAWeek={daysInAWeek}
                                splitColor={Colors.Neutral[10]}
                                weeksInSimulation={simulation.weeks}
                                widthPerDay={widthPerDay}
                            />
                            <div
                                id="worker-list"
                                style={{
                                    paddingTop: `${nodeStartY}px`,
                                    rowGap: rowPadding,
                                    width: widthPerDay * 4,
                                }}
                            >
                                {workers.map((worker: TSimWorker | TSessionWorkerExtended) => (
                                    <Row
                                        key={`worker-${worker.id}`}
                                        className="p3"
                                        onClick={() => {
                                            if (useCase == GanttUseCases.Constructor) {
                                                onWorkerOpen((worker as TSimWorker).uid);
                                            } else {
                                                onWorkerOpen(
                                                    (worker as TSessionWorkerExtended).worker_uid,
                                                );
                                            }
                                        }}
                                        style={{
                                            height: `${nodeHeight}px`,
                                        }}
                                    >
                                        <Col className="worker-avatar">
                                            {worker.picture == null && (
                                                <div className="p2-strong">
                                                    {worker.name.slice(0, 2)}
                                                </div>
                                            )}
                                        </Col>
                                        <Col className="worker-name">
                                            <Button type="link">{worker.name}</Button>
                                        </Col>
                                    </Row>
                                ))}
                                {/*<Row>
                                    <Col style={{
                                        alignItems: 'center',
                                        display: 'flex',
                                        flexDirection: 'column',
                                    }}>
                                        <Row>
                                            <Checkbox
                                                checked={state.testMode}
                                                onChange={(e) => state.testMode = e.target.checked}
                                            />
                                        </Row>
                                        <Row className="p3">
                                            Тест-данные
                                        </Row>
                                    </Col>
                                </Row>*/}
                            </div>
                            {useCase != GanttUseCases.Constructor && currentDay != null && (
                                <div
                                    id="today-marker"
                                    style={{
                                        transform: `translate(${nodeStartX + (currentDay - 1) * widthPerDay + x}px, calc(-100% - 28px))`,
                                        width: `${widthPerDay}px`,
                                    }}
                                >
                                    <div className="p2-strong today-text">Сегодня</div>
                                </div>
                            )}
                            <div
                                id="week-numbering"
                                style={{
                                    transform: `translate(${nodeStartX + x}px, -${
                                        currentDay != null
                                            ? timelineColRef?.current.clientHeight
                                            : 28
                                    }px) scale(1)`,
                                    width: `${weeksInSimulation * daysInAWeek * widthPerDay}px`,
                                }}
                            >
                                {Array.from({ length: weeksInSimulation }).map((_, i) => (
                                    <Col
                                        key={`week-num-${i}`}
                                        style={{
                                            width: `${daysInAWeek * widthPerDay}px`,
                                        }}
                                    >
                                        <Row>
                                            <Col className="left-thingie" />
                                            <Col className="p1">{i + 1}</Col>
                                            <Col className="right-thingie" />
                                        </Row>
                                    </Col>
                                ))}
                            </div>
                        </ReactFlow>
                    )}
                </Col>
            </Row>
        </div>
    );
};

const Timeline = ({
    className = '',
    currentDay = null,
    daysInAWeek,
    onTaskOpen,
    onWorkerOpen,
    simulation,
    STP,
    tasks,
    useCase,
    weeksInSimulation,
    workers,
}: TProps): JSX.Element => {
    return (
        <ReactFlowProvider>
            <TimelineContent
                className={className}
                currentDay={currentDay}
                daysInAWeek={daysInAWeek}
                onTaskOpen={onTaskOpen}
                onWorkerOpen={onWorkerOpen}
                simulation={simulation}
                STP={STP}
                tasks={tasks}
                useCase={useCase}
                weeksInSimulation={weeksInSimulation}
                workers={workers}
            />
        </ReactFlowProvider>
    );
};

export { Timeline };
