@use '/src/styles/colors';

.timeline-container {
    > .ant-row {
        height: 100%;
    }
    .timeline-flow-col {
        width: 70%;

        &.full-width {
            width: 100%;
        }
        .no-sim-or-workers {
            align-items: center;
            display: flex;
            flex-direction: row;
            height: 100%;
            justify-content: center;
            width: 100%;
        }
        #worker-list {
            background: colors.$neutral10;
            border-right: 1px solid colors.$neutral25;
            box-shadow: 4px 0px 4px 0px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            height: 100%;
            padding-left: 12px;
            position: relative;
            z-index: 101;

            > .ant-row {
                align-items: center;
                column-gap: 8px;
                cursor: pointer;
                justify-content: flex-start;

                .worker-avatar {
                    align-items: center;
                    background-color: colors.$accentC50;
                    border-radius: 50%;
                    color: colors.$neutral900;
                    display: flex;
                    flex-direction: column;
                    height: 28px;
                    justify-content: center;
                    min-width: 28px;
                    max-width: 28px;
                }
                .worker-name .ant-btn-link {
                    height: auto;
                    padding: 0;

                    span {
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        max-height: 20px;
                        min-height: 20px;
                        max-width: 75px;
                        overflow: hidden;
                        white-space: normal;
                        word-break: break-all;
                    }
                }
            }
        }
        #week-numbering {
            background-color: colors.$neutral0;
            border-top: 1px solid colors.$neutral25;
            box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: row;
            height: 28px;
            position: relative;
            transform-origin: 0 0;
            z-index: 100;

            > .ant-col {
                align-items: center;
                display: flex;
                flex-direction: column;

                > .ant-row {
                    justify-content: space-between;
                    width: 100%;

                    .left-thingie,
                    .right-thingie {
                        border-bottom: 1px solid colors.$neutral200;
                        height: 18px;
                        margin-bottom: 10px;
                        width: 28px;
                    }
                    .left-thingie {
                        border-left: 1px solid colors.$neutral200;
                        border-bottom-left-radius: 8px;
                    }
                    .right-thingie {
                        border-right: 1px solid colors.$neutral200;
                        border-bottom-right-radius: 8px;
                    }
                }
            }
        }
        #today-marker {
            background-color: colors.$successW300;
            height: calc(100% - 28px);
            opacity: 0.4;
            pointer-events: none;
            position: relative;
            transform-origin: 0 0;
            z-index: 4;

            .today-text {
                bottom: 16px;
                color: colors.$successW800;
                left: 36px;
                position: absolute;
                text-shadow: 1px 1px 1px colors.$successC925;
            }
        }
    }
}

.react-flow.timeline-flow {
    /* Custom Variables */
    --xy-theme-selected: #f57dbd;
    --xy-theme-hover: #b5c5b5;
    --xy-edge-stroke: #7c869c;
    --xy-edge-stroke-selected: #272c35;
    --xy-theme-edge-hover: #69758e;
    --xy-edge-stroke-width-default: 2px;
    --xy-theme-color-focus: #e8e8e8;

    /* Built-in Variables see https://reactflow.dev/learn/customization/theming */
    --xy-node-border-default: 1px solid #ededed;

    --xy-node-boxshadow-default:
        0px 3.54px 4.55px 0px #00000005, 0px 3.54px 4.55px 0px #0000000d,
        0px 0.51px 1.01px 0px #0000001a;

    --xy-node-border-radius-default: 4px;

    --xy-handle-background-color-default: colors.$accentW0;
    --xy-handle-border-color-default: #aaaaaa;

    --xy-edge-label-color-default: #505050;

    /* Customizing Default Theming */
    .react-flow__node {
        align-items: center;
        background-color: colors.$accentW200;
        border-radius: var(--xy-node-border-radius-default);
        box-shadow: var(--xy-node-boxshadow-default);
        color: var(--xy-node-color, var(--xy-node-color-default));
        cursor: pointer;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        height: 28px;
        justify-content: center;
        text-align: center;

        &:has(.free) {
            cursor: default;
        }

        .timeline-node-wrapper {
            border-radius: var(--xy-node-border-radius-default);
            height: 100%;
            width: 100%;

            .timeline-node-inner {
                border-radius: var(--xy-node-border-radius-default);
                padding: 2px 3px;

                .task-number {
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;
                    width: 100%;
                }
                &.task {
                    border: 1px solid colors.$neutral500;
                }
            }
        }
    }
}
