@use '/src/styles/colors';

.custom-schedule,
.custom-schedule-ingame {
    height: 100%;
    width: 100%;

    .hours-col {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding-top: 34px;
        row-gap: 76px;

        .hour-notation {
            cursor: default;
            padding-right: 20px;
        }
    }
    .main-col {
        height: 100%;

        .days-row {
            width: 100%;

            .day-col {
                height: 100%;

                .day-name {
                    align-items: center;
                    border: 1px solid colors.$neutral200;
                    border-bottom: none;
                    border-left: none;
                    cursor: default;
                    height: 44px;
                    justify-content: center;
                }
                &:first-child .day-name {
                    border-left: 1px solid colors.$neutral200;
                    border-radius: 4px 0 0 0;
                }
                &:last-child .day-name {
                    border-radius: 0 4px 0 0;
                }
                .day-slot {
                    border-right: 1px solid colors.$neutral200;
                    height: 48px;

                    &:last-child {
                        border-bottom: 1px solid colors.$neutral200;
                    }
                    &:nth-child(2n) {
                        border-top: 1px solid colors.$neutral200;
                    }
                    .filled-slot-inner {
                        background: rgba(191, 255, 111, 0.4);
                        border-left: 2px solid colors.$accentW0;
                        border-right: 2px solid colors.$accentW0;
                        border-radius: 0 0 6px 6px;
                        cursor: pointer;
                        padding: 4px;
                        padding-top: 0;
                        width: 100%;
                        z-index: 100;

                        .colored-divider {
                            background-color: colors.$successW500;
                            height: 4px;
                            width: 100%;
                        }
                        &.locked-slot {
                            background: rgba(96, 96, 96, 0.08);

                            .colored-divider {
                                background-color: colors.$neutral500;
                            }
                        }
                        .event-time {
                            color: colors.$neutral800;
                            padding-left: 6px;
                            padding-top: 4px;
                        }
                        .event-name {
                            color: colors.$neutral950;
                            padding-left: 6px;
                            overflow: hidden;
                            white-space: normal;
                            word-break: break-all;
                        }
                    }
                    .empty-slot-inner {
                        cursor: pointer;
                        height: 100%;
                        width: 100%;

                        &:hover {
                            background-color: colors.$neutral10;
                        }
                    }
                }
                &:first-child .day-slot {
                    border-left: 1px solid colors.$neutral200;
                }
            }
        }
        .week-selector-row {
            height: 44px;
            width: 100%;

            .week-col,
            .week-col-selected {
                align-items: center;
                border: 1px solid colors.$neutral200;
                border-left: none;
                border-top: none;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                justify-content: center;

                &:first-child {
                    border-left: 1px solid colors.$neutral200;
                    border-radius: 0 0 0 4px;
                }
                &:last-child {
                    border-radius: 0 0 4px 0;
                }
                .week-number {
                }
                .week-stats {
                    column-gap: 3px;
                }
                .week-current {
                }
            }
            .week-col {
                &:hover {
                    background-color: colors.$neutral10;
                }
            }
            .week-col-selected {
                background-color: colors.$successW10;

                &:hover {
                    background-color: colors.$successW50;
                }
            }
        }
    }
}

.sim-schedule-event-dialog {
    .ant-modal {
        top: 20px;
        width: 900px !important;
    }
    .sim-schedule-event-dialog-header {
        h4 {
            margin: 0;
        }
    }
    .sim-schedule-event-dialog-body {
        display: flex;
        flex-direction: column;
        padding: 24px 0;
        row-gap: 12px;

        .event-position-row {
            align-items: center;
            column-gap: 12px;
            justify-content: space-between;

            > .ant-col {
                > .ant-row {
                    align-items: center;
                    column-gap: 4px;
                }
            }
        }
        .labeled-input {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
            width: 100%;

            .split-label {
                align-items: center;
                flex-wrap: nowrap;
                justify-content: space-between;
            }
        }
        .split-row {
            column-gap: 20px;

            > .ant-col {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                row-gap: 8px;
                width: calc(50% - 10px);

                .span-gap {
                    column-gap: 4px;
                }
            }
            .labeled-input .ant-input-affix-wrapper,
            .labeled-input .ant-input-number {
                width: 100%;
            }
        }
        .schedule-event-type-selector {
            padding-top: 8px;
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 12px;
                width: 100%;

                .ant-select {
                    width: 100%;
                }
                .schedule-event-type-info {
                    border: 1px solid colors.$neutral200;
                    border-radius: 6px;
                    padding: 8px;
                    width: 100%;

                    > .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                        width: 100%;
                    }
                }
            }
        }
        .ant-input-affix-wrapper-disabled,
        textarea.ant-input-disabled,
        .ant-input-number-disabled,
        .ant-select-disabled .ant-select-selector,
        .ant-radio-button-wrapper-disabled:not(.ant-radio-button-wrapper-checked),
        .ant-btn-primary:disabled {
            background-color: colors.$neutral10;
            color: colors.$neutral800;
        }
        .ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked,
        .ant-btn-default:disabled {
            background-color: colors.$neutral25;
            color: colors.$neutral800;
        }
    }
    .sim-schedule-event-dialog-footer {
        justify-content: space-between;
        row-gap: 8px;

        > .ant-col > .ant-row {
            column-gap: 16px;
        }
        .ant-btn {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral25;
            border-radius: 4px;
            color: colors.$neutral950;
            height: 48px;

            &:disabled {
                background: colors.$neutral25;
                color: colors.$neutral300;
            }
            &:not(:disabled):hover {
                background: colors.$accentW10;
                border: 2px solid colors.$accentW10;
                color: colors.$accentW500;
            }
        }
    }
}
.schedule-event-type-option {
    align-items: center;
    column-gap: 12px;

    > .ant-col > .ant-row {
        column-gap: 4px;
    }
}
