import UniLayout from '@components/ui/uniLayout/uniLayout';
import { observer } from 'mobx-react';
import { ChatPageUseCase, ChatsFullpage } from './chatsFullpage';

import './chatsPage.scss';

const ChatsPage = observer((): JSX.Element => {
    return (
        <UniLayout
            activeTab="Чаты"
            additionalClass="uni-fixed-height"
            tabSet={null}
        >
            <div className="chats-container">
                <ChatsFullpage useCase={ChatPageUseCase.Default} />
            </div>
        </UniLayout>
    );
});

export default ChatsPage;
