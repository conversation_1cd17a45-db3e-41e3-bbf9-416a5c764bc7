@use '/src/styles/colors';

.new-chat-dialog {
    .ant-modal {
        top: 40px;
    }
    .new-chat-dialog-header {
        align-items: center;
        display: flex;
        flex-direction: row;
        padding: 16px 0;

        h4 {
            margin: 0;
        }
    }
    .new-chat-dialog-body {
        display: flex;
        flex-direction: column;
        row-gap: 12px;
        width: 100%;

        .mode-selector .ant-radio-group,
        .drawer-filter-row .ant-select,
        .ant-form-item {
            width: 100%;
        }
        .drawer-filter-row {
            .ant-select-selection-item {
                align-items: center;
                height: 36px;

                .filter-btn:hover {
                    box-shadow: none;
                }
            }
        }
        .chat-name-input {
        }
        .user-selection-list {
            background: colors.$neutral25;
            max-height: 480px;
            min-height: 480px;
            overflow-y: auto;
            padding: 10px;

            .ant-list-empty-text {
                background: colors.$accentW0;
                color: colors.$neutral950;

                .ant-col {
                    align-items: center;
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                }
                .ant-row {
                    justify-content: center;
                }
                .ant-btn {
                    background: colors.$accentW0;
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px;
                    color: colors.$neutral950;
                    height: 48px;
                    padding: 0 36px;

                    &:hover {
                        background: colors.$accentW10;
                        border: 2px solid colors.$accentW10;
                        color: colors.$accentW500;
                    }
                }
            }
            .ant-spin-container .ant-list-items {
                display: flex;
                flex-direction: column;
                row-gap: 10px;

                .drawer-card {
                    align-items: center;
                    background: colors.$accentW0;
                    column-gap: 10px;
                    flex-wrap: nowrap;
                    padding: 16px;
                    width: 100%;

                    .drawer-card-avatar {
                        background: url('/src/assets/worker_image.png');
                        background-color: colors.$accentW0;
                        background-repeat: no-repeat;
                        background-size: contain;
                        border-radius: 5px;
                        height: 106px;
                        width: 106px;
                    }
                    .user-card-main-col {
                        display: flex;
                        flex-direction: column;
                        flex-wrap: nowrap;
                        row-gap: 12px;

                        h5 {
                            margin: 0;
                            &.bold {
                                font-weight: 700;
                            }
                            &:not(.bold) {
                                font-weight: 400;
                            }
                        }
                        .filter-row {
                            column-gap: 8px;
                            flex-wrap: wrap;
                            row-gap: 8px;
                        }
                    }
                    .drawer-card-select-col {
                        margin-left: auto;
                    }
                }
            }
        }
    }
    .new-chat-dialog-footer {
        column-gap: 16px;
        justify-content: flex-end;
        padding-top: 16px;

        .ant-btn {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral25;
            border-radius: 4px;
            color: colors.$neutral950;
            height: 48px;
            padding: 0 36px;

            &:hover {
                background: colors.$accentW10;
                border: 2px solid colors.$accentW10;
                color: colors.$accentW500;
            }
        }
        .ant-btn:disabled,
        .ant-btn:disabled:hover {
            background: colors.$neutral25;
            border: 2px solid colors.$neutral25;
            color: colors.$neutral300;
        }
    }
}
@media only screen and (orientation: portrait) {
    .new-chat-dialog {
        .ant-modal {
            top: 12px;
        }
        .new-chat-dialog-body {
            .user-selection-list {
                padding: 4px;

                .ant-spin-container .ant-list-items {
                    .drawer-card {
                        column-gap: 6px;
                        padding: 6px;

                        .filter-row {
                            max-height: 72px;
                            overflow-y: auto;
                        }
                    }
                }
            }
        }
    }
}
