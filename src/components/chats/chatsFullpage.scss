@use '/src/styles/colors';
@use '/src/styles/icons';

.chats-fullpage {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    max-height: 100%;
    min-height: 100%;
    width: 100%;

    .search-icon {
        @include icons.icon-search('#FFFFFF');
        background-repeat: no-repeat;
        background-size: contain;
        height: 16px;
        width: 16px;
    }
    .more-icon {
        @include icons.icon-three-dots('#FFFFFF');
        background-repeat: no-repeat;
        background-size: contain;
        height: 16px;
        width: 16px;
    }
    .chat-list {
        border-radius: 4px 0 0 4px;
        border-right: 1px solid colors.$neutral200;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        max-width: 430px;
        min-width: 430px;
        padding-top: 12px;
        row-gap: 24px;

        .chat-list-search {
            align-items: center;
            justify-items: space-between;
            padding: 0 20px;

            .chat-list-search-input {
                .ant-input {
                    border: 2px solid colors.$neutral25;
                    border-radius: 4px 0 0 4px;
                    height: 44px;
                }
                .ant-btn {
                    // AntD primary button color
                    border: 2px solid #1677ff;
                    border-radius: 0 4px 4px 0;
                    height: 44px;
                }
                .search-icon {
                    height: 20px;
                    width: 20px;
                }
            }
        }
        .chat-group-list {
            height: calc(100% - 69px);
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                max-height: 100%;
                row-gap: 24px;
                width: 100%;

                .chat-group-list-header {
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 20px;

                    .chat-group-list-header-new {
                        align-items: center;
                        column-gap: 8px;
                        height: 40px;

                        .new-messages-text {
                            .mode-select-label {
                                color: colors.$neutral300;
                                column-gap: 8px;

                                .chevron-down-icon {
                                    @include icons.icon-arrow('#8F98AA');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    height: 24px;
                                    transform: rotate(270deg);
                                    width: 24px;
                                }
                            }
                        }
                        .new-messages-badge {
                            align-items: center;
                            background: colors.$accentC25;
                            border-radius: 6px;
                            cursor: pointer;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            padding: 0 8px;

                            .ant-row {
                                align-items: center;
                                column-gap: 4px;
                                height: 24px;
                                justify-content: center;

                                .new-messages-badge-dot {
                                    background: colors.$accentW600;
                                    border-radius: 50%;
                                    height: 8px;
                                    width: 8px;
                                }
                                .new-messages-badge-text {
                                    color: colors.$accentW600;
                                }
                            }
                        }
                    }
                    .chat-group-list-header-edit {
                        column-gap: 4px;

                        .new-chat-btn,
                        .edit-chats-btn {
                            background: colors.$neutral25;
                            border: none;
                            border-radius: 6px;
                            height: 40px;
                            width: 40px;

                            .plus-icon {
                                // colors.$neutral950
                                @include icons.icon-plus('#1A1D24');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 20px;
                                width: 20px;
                            }
                            .edit-icon {
                                // colors.$neutral950
                                @include icons.icon-edit('#1A1D24');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 20px;
                                width: 20px;
                            }
                            &:not(:disabled):hover {
                                .plus-icon {
                                    @include icons.icon-plus('#2E96DF');
                                }
                                .edit-icon {
                                    @include icons.icon-edit('#2E96DF');
                                }
                            }
                            &.editing {
                                background: colors.$accentW10;

                                .cross-icon {
                                    @include icons.icon-plus('#35ABFF');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    height: 20px;
                                    transform: rotate(45deg);
                                    width: 20px;
                                }
                                &:hover {
                                    background: colors.$accentW50;

                                    .cross-icon {
                                        @include icons.icon-plus('#2E96DF');
                                    }
                                }
                            }
                        }
                    }
                }
                .chat-group-list-body {
                    align-items: flex-start;
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    height: 100%;
                    overflow-y: auto;
                    width: 100%;

                    .group-card-chat-list {
                        padding-right: 22px;
                        width: 100%;

                        .ant-list-empty-text {
                            background: colors.$accentW0;
                            color: colors.$neutral950;
                            width: 100%;

                            .ant-col {
                                align-items: center;
                                display: flex;
                                flex-direction: column;
                            }
                        }
                        .chat-card {
                            align-items: center;
                            column-gap: 12px;
                            cursor: pointer;
                            flex-wrap: nowrap;
                            padding: 12px 20px;
                            width: 100%;

                            &.chat-card-selected {
                                background: colors.$accentW25;
                                &:hover {
                                    background: colors.$accentW25;
                                }
                            }
                            &:hover {
                                background-color: colors.$accentW10;
                            }
                            .chat-avatar {
                                align-items: center;
                                background-color: colors.$accentC50;
                                border-radius: 50%;
                                color: colors.$neutral900;
                                display: flex;
                                flex-direction: column;
                                height: 40px;
                                justify-content: center;
                                min-width: 40px;
                                max-width: 40px;
                            }
                            .chat-name-text {
                                display: flex;
                                flex-direction: column;
                                row-gap: 4px;
                                width: 100%;

                                .chat-name {
                                    -webkit-line-clamp: 1;
                                    -webkit-box-orient: vertical;
                                    color: colors.$neutral300;
                                    display: -webkit-box;
                                    max-height: 20px;
                                    min-height: 20px;
                                    max-width: 263px;
                                    overflow: hidden;
                                    white-space: normal;
                                    word-break: break-all;
                                }
                                .chat-text {
                                    align-items: center;

                                    .chat-text-sender,
                                    .chat-text-body {
                                        -webkit-line-clamp: 1;
                                        -webkit-box-orient: vertical;
                                        display: -webkit-box;
                                        max-height: 16px;
                                        min-height: 16px;
                                        overflow: hidden;
                                        white-space: normal;
                                        word-break: break-all;
                                    }
                                    .chat-text-sender {
                                        color: colors.$neutral950;
                                        max-width: 128px;
                                    }
                                    .chat-text-semicolon {
                                        color: colors.$neutral950;
                                        padding-right: 4px;
                                    }
                                    .chat-text-body {
                                        color: colors.$neutral900;
                                        max-width: 128px;

                                        &.my-message {
                                            max-width: 255px;
                                        }
                                        &.no-messages {
                                            color: colors.$neutral200;
                                            max-width: 263px;
                                        }
                                    }
                                }
                            }
                            .chat-status {
                                align-items: flex-end;
                                display: flex;
                                flex-direction: column;
                                row-gap: 4px;
                                width: 40px;

                                .chat-status-datetime {
                                    color: colors.$neutral300;
                                }
                                .chat-status-badge {
                                    align-items: center;
                                    background-color: colors.$accentW400;
                                    border-radius: 6px;
                                    column-gap: 4px;
                                    cursor: pointer;
                                    display: flex;
                                    flex-direction: row;
                                    flex-wrap: nowrap;
                                    height: 24px;
                                    justify-content: center;
                                    padding: 0 8px;

                                    .chat-status-badge-dot {
                                        background: colors.$accentW0;
                                        border-radius: 50%;
                                        height: 8px;
                                        width: 8px;
                                    }
                                    .chat-status-badge-text {
                                        color: colors.$accentW0;
                                    }
                                }
                            }
                            .restore-btn,
                            .delete-btn {
                                height: 36px;
                                padding: 6px;
                                width: 36px;
                            }
                            .delete-icon {
                                @include icons.icon-delete('#D6352A');
                            }
                            .restore-icon {
                                @include icons.icon-restore('#68C0FF');
                            }
                            .delete-icon,
                            .restore-icon {
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 24px;
                                width: 24px;
                            }
                        }
                        .chat-card.action {
                            .chat-name-text {
                                .chat-name {
                                    max-width: 215px;
                                }
                                .chat-text {
                                    .chat-text-sender {
                                        max-width: 104px;
                                    }
                                    .chat-text-semicolon {
                                        padding-right: 4px;
                                    }
                                    .chat-text-body {
                                        max-width: 104px;

                                        &.my-message {
                                            max-width: 207px;
                                        }
                                        &.no-messages {
                                            max-width: 215px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .chat-list-minimized {
        .chat-list-search {
            .chat-list-search-button {
            }
        }
    }
    .no-chat-selected {
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;

        .no-chat-text {
            justify-content: center;
        }
    }
    .chat-inner {
        display: flex;
        flex-direction: column;
        //padding-bottom: 12px;
        width: 100%;

        .chat-header {
            align-items: center;
            background: colors.$accentW0;
            border-bottom: 1px solid colors.$neutral200;
            border-top-right-radius: 8px;
            cursor: pointer;
            max-height: 80px;
            min-height: 80px;
            justify-content: space-between;
            padding: 12px 40px;

            .chat-header-info {
                > .ant-row {
                    align-items: center;
                    column-gap: 8px;

                    .chat-info-back {
                        .info-back-btn {
                            .back-icon {
                                // colors.$neutral300;
                                @include icons.icon-arrow('#8F98AA');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 24px;
                                width: 24px;
                            }
                            &:not(:disabled):hover .back-icon {
                                @include icons.icon-arrow('#2E96DF');
                            }
                        }
                    }
                    .chat-avatar {
                        align-items: center;
                        background-color: colors.$accentC50;
                        border-radius: 50%;
                        color: colors.$neutral900;
                        display: flex;
                        flex-direction: column;
                        height: 40px;
                        justify-content: center;
                        min-width: 40px;
                        max-width: 40px;
                    }
                    .chat-name {
                        color: colors.$neutral300;
                    }
                }
                &.search {
                    width: 100%;

                    > .ant-row {
                        align-items: center;
                        column-gap: 8px;

                        .chat-search-back {
                            .back-icon {
                                // colors.$neutral300;
                                @include icons.icon-arrow('#8F98AA');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 24px;
                                width: 24px;
                            }
                            &:not(:disabled):hover .back-icon {
                                @include icons.icon-arrow('#2E96DF');
                            }
                        }
                        .chat-search {
                            width: calc(100% - 40px);
                        }
                    }
                }
            }
            .chat-header-actions {
                .ant-row {
                    align-items: center;
                    column-gap: 24px;

                    .chat-header-search {
                        .search-icon {
                            // colors.$neutral300;
                            @include icons.icon-search('#8F98AA');
                            height: 24px;
                            width: 24px;
                        }
                        &:not(:disabled):hover .search-icon {
                            @include icons.icon-search('#2E96DF');
                        }
                    }
                    .chat-header-more {
                        .more-icon {
                            // colors.$neutral300;
                            @include icons.icon-three-dots('#8F98AA');
                            height: 24px;
                            width: 24px;
                        }
                        &:not(:disabled):hover .more-icon {
                            @include icons.icon-three-dots('#2E96DF');
                        }
                    }
                }
            }
        }
        .chat-messages-scroll {
            background: colors.$neutral10;
            height: 100%;
            max-height: calc(100% - 160px);
            min-height: calc(100% - 395px);
            overflow-y: auto;

            &.search {
                max-height: calc(100% - 80px);
                min-height: calc(100% - 80px);
            }
            .no-messages-in-chat {
                align-items: center;
                display: flex;
                flex-direction: row;
                justify-content: center;
                width: 100%;
            }
            .messages-scroll-inner {
                display: flex;
                flex-direction: column-reverse;
                height: 100%;
                overflow: auto;
                width: 100%;

                &::-webkit-scrollbar {
                    display: none;
                }
                .messages-scroll-inf {
                    display: flex;
                    flex-direction: column-reverse;
                    padding: 0 18px;
                    row-gap: 8px;
                    transition: all 50ms linear;

                    .messages-scroll-inf-loader {
                        align-items: center;
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                    }
                    .chat-message-item {
                        column-gap: 16px;
                        flex-wrap: nowrap;
                        max-width: 462px;

                        &:first-child {
                            padding-bottom: 12px;
                        }
                        &:last-child {
                            padding-top: 12px;
                        }
                        .sender-avatar-col {
                            align-items: start;
                            display: flex;
                            flex-direction: column;
                            max-width: 36px;
                            min-width: 36px;

                            .sender-avatar {
                                align-items: center;
                                border-radius: 50%;
                                color: colors.$neutral900;
                                cursor: default;
                                display: flex;
                                flex-direction: column;
                                height: 36px;
                                justify-content: center;
                                max-width: 36px;
                                min-width: 36px;
                            }
                        }
                        .message-sender-text {
                            display: flex;
                            flex-direction: column;
                            min-width: 72px;
                            row-gap: 4px;

                            .message-sender {
                                -webkit-line-clamp: 1;
                                -webkit-box-orient: vertical;
                                cursor: default;
                                display: -webkit-box;
                                max-height: 20px;
                                min-height: 20px;
                                max-width: 388px;
                                min-width: 72px;
                                overflow: hidden;
                                white-space: normal;
                                word-break: break-all;
                            }
                            .ant-col {
                                align-items: end;
                                flex-wrap: nowrap;
                                max-width: 388px;
                                min-width: 72px;
                                padding: 12px;
                                padding-bottom: 16px;
                                white-space: normal;
                                width: fit-content;
                                word-break: break-all;

                                .message-time-status {
                                    align-items: center;
                                    display: flex;
                                    flex-direction: row;
                                    white-space: normal;
                                    word-break: keep-all;

                                    .message-time {
                                        cursor: default;
                                        font-size: 12px;
                                        line-height: 16px;
                                    }
                                }
                            }
                        }
                        &.mine {
                            align-self: flex-end;
                            flex-direction: row-reverse;

                            .sender-avatar-col .sender-avatar {
                                background: colors.$successW50;
                            }
                            .message-sender-text {
                                align-items: flex-end;

                                .message-sender {
                                    color: colors.$neutral950;
                                }
                                .message-text {
                                    background: colors.$accentW25;
                                    border-radius: 16px 2px 16px 16px;

                                    .message-time-status {
                                        column-gap: 3px;
                                        margin-bottom: -16px;
                                        justify-content: flex-end;

                                        .message-time {
                                            color: colors.$neutral950;
                                        }
                                        .message-status svg {
                                            color: colors.$neutral950;
                                            height: 15px;
                                            width: 15px;
                                        }
                                    }
                                }
                            }
                        }
                        &.not-mine {
                            align-self: flex-start;

                            .sender-avatar-col .sender-avatar {
                                background-color: colors.$accentC50;
                            }
                            .message-sender-text {
                                .message-sender {
                                    color: colors.$neutral500;
                                }
                                .message-text {
                                    background: colors.$accentW200;
                                    border-radius: 2px 16px 16px 16px;

                                    .message-time-status {
                                        justify-content: flex-end;
                                        margin-bottom: -12px;

                                        .message-time {
                                            color: colors.$neutral800;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .chat-inputs {
            align-items: center;
            background: colors.$accentW0;
            border-bottom-right-radius: 8px;
            border-top: 1px solid colors.$neutral200;
            flex-wrap: nowrap;
            padding: 20px 30px;

            .emoji-input {
                align-self: end;
                display: flex;
                flex-direction: column;
                padding: 4px 20px 4px 0;

                .emoji-button {
                    .emoji-icon {
                        // colors.$neutral950
                        @include icons.icon-emoji('#1A1D24');
                        background-repeat: no-repeat;
                        background-size: contain;
                        height: 24px;
                        width: 24px;
                    }
                    &:not(:disabled):hover .emoji-icon {
                        @include icons.icon-emoji('#2E96DF');
                    }
                }
                .emoji-button:disabled {
                    background-color: colors.$neutral25;

                    .emoji-icon {
                        // colors.$neutral300
                        @include icons.icon-emoji('#8F98AA');
                    }
                }
            }
            .main-input {
                display: flex;
                flex-direction: column;
                flex-grow: auto;
                width: calc(100% - 159px);

                .main-input-textarea {
                }
            }
            .attach-files-send {
                align-self: end;
                display: flex;
                flex-direction: column;
                padding-left: 20px;

                .ant-row {
                    align-items: end;
                    column-gap: 15px;
                    flex-wrap: nowrap;

                    .attach-files {
                        padding: 4px 0;

                        .attach-image {
                            .image-icon {
                                // colors.$neutral950
                                @include icons.icon-image('#1A1D24');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 24px;
                                width: 24px;
                            }
                            &:not(:disabled):hover .image-icon {
                                @include icons.icon-image('#2E96DF');
                            }
                        }
                        .attach-image:disabled {
                            background-color: colors.$neutral25;

                            .image-icon {
                                // colors.$neutral300
                                @include icons.icon-image('#8F98AA');
                            }
                        }
                    }
                    .send-btn {
                        align-items: center;
                        display: flex;
                        flex-direction: column;

                        .too-much-text {
                            color: colors.$errorC600;
                        }
                        .send-message {
                            border-radius: 50%;
                            height: 40px;
                            width: 40px;

                            .send-arrow-icon {
                                @include icons.icon-arrow('#FFFFFF');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 24px;
                                transform: rotate(180deg);
                                width: 24px;
                            }
                        }
                        .send-message:disabled {
                            background-color: colors.$neutral25;

                            .send-arrow-icon {
                                // colors.$neutral300
                                @include icons.icon-arrow('#8F98AA');
                            }
                        }
                    }
                }
            }
        }
        .chat-info {
            background: colors.$neutral10;
            height: 100%;
            max-height: calc(100% - 80px);
            min-height: calc(100% - 80px);
            overflow-y: auto;

            > .ant-col {
                display: flex;
                flex-direction: column;
                padding: 24px;
                row-gap: 12px;
                width: 100%;

                .chat-name-row {
                    column-gap: 8px;

                    .ant-btn {
                        .confirm-icon,
                        .cancel-icon {
                            background-repeat: no-repeat;
                            background-size: contain;
                            height: 24px;
                            width: 24px;
                        }
                        .confirm-icon {
                            @include icons.icon-confirm('#68C0FF');
                        }
                        .cancel-icon {
                            @include icons.icon-plus('#D6352A');
                            transform: rotate(45deg);
                        }
                        &:disabled {
                            background-color: colors.$neutral25;

                            .confirm-icon {
                                @include icons.icon-confirm('#8F98AA');
                            }
                            .cancel-icon {
                                @include icons.icon-plus('#8F98AA');
                            }
                        }
                    }
                }
                .chat-users {
                    > .ant-col {
                        display: flex;
                        flex-direction: column;
                        row-gap: 16px;
                        width: 100%;

                        .chat-users-label {
                            align-items: center;
                        }
                        .chat-users-body {
                            .chat-user-list {
                                width: 100%;

                                .ant-list-items {
                                    display: flex;
                                    flex-direction: column;
                                    row-gap: 8px;
                                }
                                .chat-user-card {
                                    align-items: center;
                                    background: colors.$accentW0;
                                    border-radius: 8px;
                                    justify-content: space-between;
                                    padding: 8px;
                                    width: 100%;

                                    .chat-user-avatar-name {
                                        align-items: center;
                                        cursor: default;
                                        column-gap: 8px;
                                        flex-wrap: nowrap;

                                        .chat-user-avatar {
                                            align-items: center;
                                            background-color: colors.$accentC50;
                                            border-radius: 50%;
                                            color: colors.$neutral900;
                                            display: flex;
                                            flex-direction: column;
                                            height: 40px;
                                            justify-content: center;
                                            min-width: 40px;
                                            max-width: 40px;
                                        }
                                        .chat-user-name {
                                            display: flex;
                                            flex-direction: column;
                                            row-gap: 4px;
                                            width: 100%;

                                            .user-name {
                                                -webkit-line-clamp: 1;
                                                -webkit-box-orient: vertical;
                                                color: colors.$neutral300;
                                                display: -webkit-box;
                                                max-height: 20px;
                                                min-height: 20px;
                                                overflow: hidden;
                                                white-space: normal;
                                                word-break: break-all;
                                            }
                                        }
                                    }
                                    .chat-user-actions {
                                        column-gap: 8px;

                                        .unadmin-btn,
                                        .ban-btn {
                                            svg {
                                                color: colors.$errorC600;
                                            }
                                            &:disabled {
                                                svg {
                                                    color: colors.$neutral300;
                                                }
                                            }
                                        }
                                        .open-btn,
                                        .admin-btn,
                                        .unban-btn {
                                            svg {
                                                color: colors.$accentW300;
                                            }
                                            &:disabled {
                                                svg {
                                                    color: colors.$neutral300;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .chat-users-add {
                    justify-content: center;
                    width: 100%;

                    .chat-user-add-btn {
                        background: colors.$accentW0;
                        border-radius: 4px;
                        border: 2px solid colors.$neutral25;
                        color: colors.$neutral950;
                        height: 48px;

                        .plus-icon {
                            @include icons.icon-plus('#1A1D24');
                            background-repeat: no-repeat;
                            background-size: contain;
                            height: 24px;
                            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                            width: 24px;
                        }
                        &:not(:disabled):hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;

                            .plus-icon {
                                @include icons.icon-plus('#35ABFF');
                            }
                        }
                        &:disabled {
                            background: colors.$neutral25;
                            color: colors.$neutral300;

                            .plus-icon {
                                @include icons.icon-plus('#8F98AA');
                            }
                        }
                    }
                }
            }
        }
    }
}

.ant-popover-inner:has(.message-actions),
.ant-popover-inner:has(.chat-actions) {
    padding: 4px;

    .message-actions,
    .chat-actions {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        row-gap: 4px;

        .ant-btn {
            padding: 4px;
        }
    }
    .chat-actions {
        .info-icon {
            @include icons.icon-gear('#68C0FF');
        }
        .delete-icon {
            @include icons.icon-delete('#D6352A');
        }
        .restore-icon {
            @include icons.icon-restore('#68C0FF');
        }
        .ant-btn {
            justify-content: flex-start;
            padding: 2px;

            &:disabled {
                background-color: colors.$neutral25;

                .info-icon {
                    @include icons.icon-gear('#8F98AA');
                }
                .delete-icon {
                    @include icons.icon-delete('#8F98AA');
                }
                .restore-icon {
                    @include icons.icon-restore('#8F98AA');
                }
            }
        }
        .info-icon,
        .delete-icon,
        .restore-icon {
            background-repeat: no-repeat;
            background-size: contain;
            height: 16px;
            width: 16px;
        }
    }
}
