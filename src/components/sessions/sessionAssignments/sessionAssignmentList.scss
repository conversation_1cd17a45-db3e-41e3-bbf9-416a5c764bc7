@use '/src/styles/colors';
@use '/src/styles/icons';

.assignment-list-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    min-width: 556px;
    row-gap: 16px;
    width: 100%;

    .radio-group {
        column-gap: 12px;
        display: flex;
        flex-direction: row;
        padding: 24px 24px 12px 40px;

        &.and-tag {
            padding-bottom: 0;
        }
    }
    .assignment-list {
        display: flex;
        flex-direction: column;
        min-width: 556px;
        padding: 24px 40px 140px 38px;
        row-gap: 16px;
        width: 100%;

        &:has(.tag-section) {
            padding-top: 0;
        }
        .tag-section {
            .ant-tag {
                align-items: center;
                display: inline-flex;
                flex-direction: row;
                padding: 8px;
            }
        }
        .ant-list-empty-text {
            background: colors.$accentW0;
            color: colors.$neutral950;

            .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
            }
            .ant-row {
                justify-content: center;
            }
        }
        .ant-list .ant-spin-nested-loading .ant-spin-container > .ant-row {
            gap: 16px 16px;
            min-width: 480px;
            width: 100%;

            > div {
                max-width: unset !important;
                width: unset !important;
            }
        }
        .assignment-list-add,
        .assignment-list-card {
            background: colors.$accentW0;
            border: 2px solid colors.$neutral100;
            border-radius: 4px;
            height: 286px;
            width: 480px;
        }
        .assignment-list-add {
            align-items: center;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .add-icon {
                @include icons.icon-plus('#272C35');
                background-repeat: no-repeat;
                background-size: contain;
                height: 32px;
                width: 32px;
            }
            &:hover {
                border: 2px solid colors.$accentW500;
                .add-icon {
                    @include icons.icon-plus('#35ABFF');
                }
            }
        }
        .assignment-list-card {
            display: flex;
            flex-direction: column;
            padding: 16px 16px;

            &.shorter {
                height: 258px;
            }
            .ant-btn-link {
                height: 20px;
                padding: 0;
            }
            > .ant-col:last-child {
                display: flex;
                flex-direction: column;
                height: 100%;
                row-gap: 10px;

                .body-row .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;

                    > .ant-row {
                        align-items: center;
                        justify-content: space-between;
                    }
                    .event-card-controls {
                        column-gap: 24px;
                        justify-self: flex-end;
                        padding-bottom: 0;

                        .ant-btn {
                            background: colors.$accentW0;
                            border: 2px solid colors.$neutral25;
                            border-radius: 4px;
                            color: colors.$neutral950;
                            height: 48px;

                            &:hover {
                                background: colors.$accentW10;
                                border: 2px solid colors.$accentW10;
                                color: colors.$accentW500;
                            }
                        }
                    }
                }
            }
        }
        .pagination-row {
            column-gap: 16px;
            justify-content: center;
        }
    }
}
@media only screen and (orientation: portrait) {
    .assignment-list-container {
        min-width: 334px;

        .radio-group {
            flex-wrap: wrap;
            padding: 8px 0 4px 16px;
            row-gap: 8px;

            .ant-radio-group {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                row-gap: 8px;

                .ant-radio-button-wrapper {
                    padding-inline: 8px;
                }
            }
        }
        .assignment-list {
            max-width: 330px;
            min-width: 330px;
            padding: 12px 8px 8px 8px;
            width: 100%;

            .ant-list {
                min-width: 318px;

                .ant-spin-nested-loading .ant-spin-container > .ant-row {
                    gap: 16px 0;
                    min-width: 318px;
                }
                .assignment-list-card {
                    height: 330px;
                    width: 318px;

                    .header-row .p2-strong {
                        max-width: 240px;
                    }
                }
            }
        }
    }
}
