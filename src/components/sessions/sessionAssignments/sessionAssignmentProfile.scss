@use '/src/styles/colors';

.session-assignment-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .session-assignment-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        .session-info-row,
        .simulation-info-row,
        .assignment-info-row {
            width: 100%;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                width: 100%;

                .header-row {
                    align-items: center;
                    column-gap: 20px;
                    justify-content: space-between;
                    width: 100%;

                    h4 {
                        color: colors.$neutral950;
                        margin-top: 0;
                    }
                    > .ant-col .ant-row {
                        flex-wrap: nowrap;
                    }
                }
                .body-row {
                    .labeled-input {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                        width: 100%;

                        .creator-row {
                            align-items: center;
                            column-gap: 12px;
                            cursor: pointer;
                            flex-wrap: nowrap;
                            width: fit-content;

                            .creator-avatar {
                                align-items: center;
                                background-color: colors.$accentC50;
                                border-radius: 50%;
                                color: colors.$neutral900;
                                display: flex;
                                flex-direction: column;
                                height: 40px;
                                justify-content: center;
                                min-width: 40px;
                                max-width: 40px;
                            }
                            .creator-name {
                                .creator-btn {
                                    padding: 0;
                                }
                            }
                        }
                        .lighter-tone {
                            color: colors.$neutral700;
                        }
                    }
                }
            }
        }
        .session-info-row,
        .simulation-info-row {
            border-bottom: 1px solid colors.$neutral300;
            padding-bottom: 16px;
        }
        .session-info-row,
        .assignment-info-row {
            .body-row {
                column-gap: 20px;

                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: calc(50% - 10px);
                }
            }
        }
        .controls-row {
            column-gap: 24px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            row-gap: 8px;

            > .ant-col {
                display: flex;
                flex-direction: column;
                row-gap: 8px;

                > .ant-row {
                    column-gap: 24px;
                    row-gap: 8px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 48px;

                        &:hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;
                        }
                    }
                }
            }
        }
    }
}
@media only screen and (orientation: portrait) {
    .session-assignment-profile {
        .session-assignment-card {
            padding: 16px;
        }
    }
}
