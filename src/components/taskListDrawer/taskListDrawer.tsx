import { faXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { rootStore } from '@store/instanse';
import { But<PERSON>, Col, Drawer, List, Row, Table, Tooltip } from 'antd';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSessionWorkerExtended } from 'types/session/sessionWorker';
import { TSimulation } from 'types/simulation/simulation';
import { TSimTask } from 'types/simulation/simulationTask';
import { IngameWorkerSWAlinks, SWAlinkStatus } from 'types/ingame';

import '@components/drawers/drawers.scss';

type TProps = {
    isOpen: boolean;
    onClose: () => void;
    onDeselect: (task_uid: TSimTask['uid']) => void;
    onSelect: (task_uid: TSimTask['uid']) => void;
    onWorkerOpen?: (worker_uid: TSessionWorkerExtended['worker_uid']) => void;
    simulation: TSimulation | null;
    SWAlinks: IngameWorkerSWAlinks | null;
    tasks: TSessionTaskExtended[];
    worker: TSessionWorkerExtended | null;
};

const TaskListDrawer = ({
    isOpen,
    onClose,
    onDeselect,
    onSelect,
    onWorkerOpen,
    simulation,
    SWAlinks,
    tasks,
    worker,
}: TProps): JSX.Element => {
    function prepareData() {
        const ds = [...tasks].map((ti) => {
            const tLink =
                SWAlinks == null
                    ? null
                    : Object.values(SWAlinks).find(
                          (swali) => swali != null && swali.task_id == ti.order_id,
                      );
            return { ...ti, link: tLink };
        });
        return ds.sort((a, b) => {
            const indexA = worker?.current_tasks.findIndex((cti) => cti == a.id);
            const indexB = worker?.current_tasks.findIndex((cti) => cti == b.id);
            if (indexA >= 0 && indexB >= 0) return indexA - indexB;
            else if (indexA >= 0 && indexB == -1) return -1;
            else if (indexA == -1 && indexB >= 0) return 1;
            else return 0;
        });
    }

    function genCardCols() {
        const cols = [];
        for (let i = 0; i < simulation?.skills.length; i++) {
            cols.push({
                title: simulation.skills[i],
                dataIndex: `skills${i}`,
                key: `skill-${i}`,
            });
        }
        return cols;
    }

    function genCardRows(item: TSessionTaskExtended) {
        const row = {
            key: `task-requirements-${item.id}`,
        };
        for (let i = 0; i < item.stats_req.length; i++) {
            row[`skills${i}`] = item.stats_req[i];
        }
        return [row];
    }

    function genWorkerRows() {
        const row = {
            key: `worker-stats`,
        };
        for (let i = 0; i < worker?.current_stats.length; i++) {
            row[`skills${i}`] = worker?.current_stats[i];
        }
        return [row];
    }

    return (
        <Drawer
            className="template-drawer sim-task-drawer"
            closeIcon={null}
            onClose={onClose}
            open={isOpen}
            placement="right"
            title={
                <Row className="drawer-header">
                    <Col>
                        <h4>Выбрать задачу</h4>
                    </Col>
                    <Col>
                        <Button
                            icon={<FontAwesomeIcon icon={faXmark} />}
                            onClick={onClose}
                        />
                    </Col>
                </Row>
            }
            width={'auto'}
        >
            <Col>
                <Row className="worker-info">
                    <Col>
                        <Row className="worker-avatar-name">
                            <Col>
                                <Row>
                                    <Col className="worker-avatar">
                                        {worker?.picture == null && (
                                            <div className="p1-strong">
                                                {worker?.name[0] ?? '-'}
                                            </div>
                                        )}
                                    </Col>
                                    <Col className="worker-name">
                                        <span className="p2">{worker?.name}</span>
                                    </Col>
                                </Row>
                            </Col>
                            {onWorkerOpen != undefined && (
                                <Col>
                                    <Button
                                        onClick={() => onWorkerOpen(worker?.worker_uid)}
                                        size="small"
                                        type="default"
                                    >
                                        Перейти
                                    </Button>
                                </Col>
                            )}
                        </Row>
                        <Row className="worker-rate-percentage">
                            <Col className="labeled-input">
                                <Row>
                                    <span className="p3">Занятость на проекте:</span>
                                </Row>
                                <Row>
                                    <span className="p3 lighter-tone">
                                        {`${worker?.project_percentage}%`}
                                    </span>
                                </Row>
                            </Col>
                            <Col className="labeled-input">
                                <Row>
                                    <span className="p3">Почасовая ставка:</span>
                                </Row>
                                <Row>
                                    <span className="p3 lighter-tone">
                                        {`${worker?.hourly_rate}/час`}
                                    </span>
                                </Row>
                            </Col>
                        </Row>
                        <Row className="worker-stats">
                            <Table
                                bordered
                                columns={genCardCols()}
                                dataSource={genWorkerRows()}
                                pagination={false}
                                size="small"
                            />
                        </Row>
                    </Col>
                </Row>
                <Row className="drawer-list">
                    <List
                        dataSource={prepareData()}
                        itemLayout="vertical"
                        renderItem={(item) => (
                            <Row className="drawer-card sim-task-card">
                                <Col>
                                    <Row>
                                        <Col>
                                            <div className="p2-strong">
                                                {`${item.order_id}. ${item.name}`}
                                            </div>
                                            <div className="desc-l">
                                                {rootStore.ingameStore.taskStartToEnd(item)}
                                            </div>
                                        </Col>
                                        <Col>
                                            {item.end_day == null && item.end_tick == null ? (
                                                item.link != null ? (
                                                    item.link.status ==
                                                        SWAlinkStatus.AwaitingAssign ||
                                                    item.link.status == SWAlinkStatus.Current ? (
                                                        <Button
                                                            danger
                                                            disabled={onDeselect == null}
                                                            onClick={() =>
                                                                onDeselect(item.task_uid)
                                                            }
                                                            size="small"
                                                            type="default"
                                                        >
                                                            {item.link.status ==
                                                            SWAlinkStatus.AwaitingAssign
                                                                ? 'Отменить'
                                                                : 'Убрать'}
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            disabled={onSelect == null}
                                                            onClick={() => onSelect(item.task_uid)}
                                                            size="small"
                                                            type="default"
                                                        >
                                                            {item.link.status ==
                                                            SWAlinkStatus.AwaitingCancel
                                                                ? 'Вернуть'
                                                                : 'Назначить'}
                                                        </Button>
                                                    )
                                                ) : worker?.current_tasks.find(
                                                      (cti) => cti == item.id,
                                                  ) ? (
                                                    <Button
                                                        danger
                                                        disabled={onDeselect == null}
                                                        onClick={() => onDeselect(item.task_uid)}
                                                        size="small"
                                                        type="default"
                                                    >
                                                        Убрать
                                                    </Button>
                                                ) : (
                                                    <Button
                                                        disabled={onSelect == null}
                                                        onClick={() => onSelect(item.task_uid)}
                                                        size="small"
                                                        type="default"
                                                    >
                                                        Назначить
                                                    </Button>
                                                )
                                            ) : (
                                                <Tooltip title="Задача завершена">
                                                    <Button
                                                        disabled
                                                        size="small"
                                                        type="default"
                                                    >
                                                        {worker?.current_tasks.find(
                                                            (cti) => cti == item.id,
                                                        )
                                                            ? 'Убрать'
                                                            : 'Назначить'}
                                                    </Button>
                                                </Tooltip>
                                            )}
                                            {item.link != null &&
                                                item.link.status ==
                                                    SWAlinkStatus.AwaitingAssign && (
                                                    <div className="desc-l">Со след. дня</div>
                                                )}
                                            {item.link != null &&
                                                item.link.status ==
                                                    SWAlinkStatus.AwaitingCancel && (
                                                    <div className="desc-l">До конца дня</div>
                                                )}
                                        </Col>
                                    </Row>
                                    <Row className="requirements-table">
                                        <Table
                                            bordered
                                            columns={genCardCols()}
                                            dataSource={genCardRows(item)}
                                            pagination={false}
                                            size="small"
                                        />
                                    </Row>
                                </Col>
                            </Row>
                        )}
                        style={{
                            maxHeight: '100%',
                            minHeight: `${146 * 5 - 8}px`,
                        }}
                    />
                </Row>
            </Col>
        </Drawer>
    );
};

export { TaskListDrawer };
