@use '/src/styles/colors';
@use '/src/styles/icons';

.stores-container,
.network-container {
    background: colors.$accentW0;
    border: 2px solid colors.$neutral100;
    border-radius: 4px;
    width: 100%;

    .radio-group {
        column-gap: 12px;
        display: flex;
        flex-direction: row;
        padding: 24px 0 12px 40px;
    }
    .stores-inner,
    .network-inner {
        column-gap: 12px;
        display: flex;
        flex-direction: row;
        padding: 20px 40px 40px 38px;
        width: 100%;
    }
    .stores-inner,
    .network-inner {
        .fit-height {
            max-height: 600px;
            min-width: 350px;
            overflow-y: scroll;
            padding: 0 12px;
        }
        .store-controls {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
        }
    }
    .network-inner {
        .group-selector {
            display: flex;
            flex-direction: column;
            row-gap: 8px;
            width: 260px;

            svg {
                height: 20px;
                width: 20px;
            }
        }
    }
}
.table-statuses-profile {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;

    .table-statuses-card {
        background: colors.$accentW0;
        border: 2px solid colors.$neutral100;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 40px 44px 45px 38px;
        width: 100%;

        > .ant-col {
            display: flex;
            flex-direction: column;
            row-gap: 16px;
            width: 100%;

            .header-row {
                align-items: center;
                column-gap: 20px;
                justify-content: space-between;
                width: 100%;

                h4 {
                    color: colors.$neutral950;
                    margin-top: 0;
                }
            }
            .body-row {
                > .ant-col {
                    display: flex;
                    flex-direction: column;
                    row-gap: 8px;
                    width: 100%;

                    .table-actions {
                        align-items: center;
                        border: 1px solid colors.$neutral10;
                        border-radius: 8px;
                        column-gap: 8px;
                        min-height: 58px;
                        padding: 12px;
                        row-gap: 8px;
                        width: 100%;

                        .export-btn {
                            &:not(:disabled) {
                                background: colors.$successW400;
                            }

                            .table-icon {
                                @include icons.icon-item-table('#1A1D24');
                                background-repeat: no-repeat;
                                background-size: contain;
                                height: 19px;
                                width: 19px;
                            }
                        }
                    }
                    .table-container {
                        width: 100%;

                        .ant-table-wrapper {
                            width: 100%;

                            .ant-table-placeholder .ant-table-cell {
                                background: colors.$accentW0;
                                color: colors.$neutral950;

                                .ant-col {
                                    display: flex;
                                    flex-direction: column;
                                    row-gap: 8px;
                                }
                                .ant-row {
                                    justify-content: center;
                                }
                            }
                            .ant-table-tbody .ant-table-row {
                                .no-filter-name {
                                    color: colors.$errorC200;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
