import UniLayout from '@components/ui/uniLayout/uniLayout';
import { observer } from 'mobx-react';
import { Button, Col, Popconfirm, Radio, Row } from 'antd';
import { useReactive } from 'ahooks';
import { TUser } from 'types/user/user';
import { rootStore } from '@store/instanse';
import { useEffect } from 'react';
import { CopyButton } from '@components/ui/copyButton/copyButton';

import './tests.scss';
import { Loader } from '@components/ui/loader/loader';

type TState = {
    isLoading: boolean;
    mode: 'currentUserStore' | 'ingameStore' | 'socketStore';
    currentUserStoreDump: TUser | null;
    ingameStoreDump: any | null;
    socketStoreDump: any | null;
};

const StoresTest = observer((): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        mode: 'currentUserStore',
        currentUserStoreDump: null,
        ingameStoreDump: null,
        socketStoreDump: null,
    });

    function loadCurrentUserStoreDump() {
        state.currentUserStoreDump = rootStore.currentUserStore.getUser;
    }

    function loadIngameStoreDump() {
        state.ingameStoreDump = rootStore.ingameStore.dump();
    }

    function loadSocketStoreDump() {
        state.socketStoreDump = rootStore.socketStore.dump();
    }

    useEffect(() => {
        loadCurrentUserStoreDump();
        loadIngameStoreDump();
        loadSocketStoreDump();
    }, []);

    return (
        <UniLayout
            activeTab="stores"
            tabSet="dev"
        >
            <div className="stores-container">
                {state.isLoading && <Loader />}
                <div className="radio-group">
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.mode = e.target.value;
                        }}
                        options={[
                            { label: 'CurrentUser', value: 'currentUserStore' },
                            { label: 'Ingame', value: 'ingameStore' },
                            { label: 'SocketStore', value: 'socketStore' },
                        ]}
                        optionType="button"
                        value={state.mode}
                    />
                </div>
                {state.mode == 'currentUserStore' && (
                    <div className="stores-inner">
                        <Col className="fit-height desc-l">
                            <pre>{JSON.stringify(state.currentUserStoreDump, null, 4)}</pre>
                        </Col>
                        <Col className="store-controls p2">
                            <Row>
                                <Button
                                    onClick={() => {
                                        loadCurrentUserStoreDump();
                                    }}
                                    type="primary"
                                >
                                    Обновить
                                </Button>
                            </Row>
                            <Row>
                                <CopyButton
                                    textToCopy={JSON.stringify(state.currentUserStoreDump, null, 4)}
                                    textToShow="CurrentUser скопирован"
                                    size={32}
                                />
                            </Row>
                        </Col>
                    </div>
                )}
                {state.mode == 'ingameStore' && (
                    <div className="stores-inner">
                        <Col className="fit-height desc-l">
                            <pre>{JSON.stringify(state.ingameStoreDump, null, 4)}</pre>
                        </Col>
                        <Col className="store-controls p2">
                            <Row>
                                <Button
                                    onClick={() => {
                                        loadIngameStoreDump();
                                    }}
                                    type="primary"
                                >
                                    Обновить
                                </Button>
                            </Row>
                            <Row>
                                <CopyButton
                                    textToCopy={JSON.stringify(state.ingameStoreDump, null, 4)}
                                    textToShow="IngameStore скопирован"
                                    size={32}
                                />
                            </Row>
                            <Row>
                                <Popconfirm
                                    title="А может бахнем?"
                                    okText="Обязательно бахнем. Весь мир в труху!"
                                    cancelText="Но потом."
                                    onConfirm={() => {
                                        rootStore.ingameStore.clearStore();
                                        loadIngameStoreDump();
                                    }}
                                >
                                    <Button
                                        danger
                                        type="default"
                                    >
                                        Сброс
                                    </Button>
                                </Popconfirm>
                            </Row>
                        </Col>
                    </div>
                )}
                {state.mode == 'socketStore' && (
                    <div className="stores-inner">
                        <Col className="fit-height desc-l">
                            <pre>{JSON.stringify(state.socketStoreDump, null, 4)}</pre>
                        </Col>
                        <Col className="store-controls p2">
                            <Row>
                                <Button
                                    onClick={() => {
                                        loadSocketStoreDump();
                                    }}
                                    type="primary"
                                >
                                    Обновить
                                </Button>
                            </Row>
                            <Row>
                                <Button
                                    onClick={async () => {
                                        state.isLoading = true;
                                        await rootStore.socketStore.fetchChatList();
                                        loadSocketStoreDump();
                                        state.isLoading = false;
                                    }}
                                    type="primary"
                                >
                                    Подгрузка чатов
                                </Button>
                            </Row>
                            <Row>
                                <CopyButton
                                    textToCopy={JSON.stringify(state.socketStoreDump, null, 4)}
                                    textToShow="SocketStore скопирован"
                                    size={32}
                                />
                            </Row>
                            <Row>
                                <Popconfirm
                                    title="А может бахнем?"
                                    okText="Обязательно бахнем. Весь мир в труху!"
                                    cancelText="Но потом."
                                    onConfirm={() => {
                                        rootStore.socketStore.clearStore();
                                        loadSocketStoreDump();
                                    }}
                                >
                                    <Button
                                        danger
                                        type="default"
                                    >
                                        Сброс
                                    </Button>
                                </Popconfirm>
                            </Row>
                        </Col>
                    </div>
                )}
            </div>
        </UniLayout>
    );
});

export default StoresTest;
