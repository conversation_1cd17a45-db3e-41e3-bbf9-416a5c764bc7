import { SettingsManager } from '@classes/settingsManager';
import { CRMAPI } from './crmApi';
import { RequestResult } from './responseModels/requestResult';
import { BulkResponse } from './responseModels/bulkResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { GlobalConstants } from '@classes/constants';

class CRMAPIManager {
    /**
     * Вызывает метод из API
     * @param method Вызов метода из API
     * @param noAuth Пропуск аутентификации
     * @returns Результат вызова метода
     */
    static async request<T>(
        method: (api: CRMAPI) => Promise<RequestResult<T>>,
        noAuth = false,
    ): Promise<RequestResult<T>> {
        let ca: CRMAPI;
        const creds = SettingsManager.getConnectionCredentials();
        if (creds?.accessToken == null && noAuth == false) {
            return {
                statusCode: -1,
                errorCode: '-1',
                errorMessages: ['Срок действия сессии закончился.'],
            };
        } else {
            ca = new CRMAPI(noAuth ? null : creds.accessToken);
        }

        const result = await method(ca);
        return result;
    }

    /**
     * Отправка и поллинг bulk-запросов
     * @param body Тело с изменениями
     * @param sendMethod Метод отправки
     * @param resultMethod Метод опроса результата
     * @returns Результат, либо ошибка
     */
    static async bulkRequest<T>(
        body: TBulkUpdate<T>,
        sendMethod: (api: CRMAPI, body: TBulkUpdate<T>) => Promise<RequestResult<BulkResponse>>,
        resultMethod: (
            api: CRMAPI,
            task_id: string,
        ) => Promise<RequestResult<BulkResultResponse<any>>>,
    ): Promise<RequestResult<BulkResultResponse<any>>> {
        let ca: CRMAPI;
        const creds = SettingsManager.getConnectionCredentials();
        if (creds?.accessToken == null) {
            return {
                statusCode: -1,
                errorCode: '-1',
                errorMessages: ['Срок действия сессии закончился.'],
            };
        } else {
            ca = new CRMAPI(creds.accessToken);
        }

        const sendResult = await sendMethod(ca, body);
        if (!sendResult.data.task_id) {
            return {
                statusCode: 500,
                errorCode: 'ServerError',
                errorMessages: ['Не получен task_id'],
            };
        }
        let resultRecieved = false;
        let timeoutReached = false;
        let result = null;
        const bulkTimeout = setTimeout(() => {
            timeoutReached = true;
        }, GlobalConstants.BulkRequestTimeout);
        while (!(resultRecieved || timeoutReached)) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
            const bulkResult = await resultMethod(ca, sendResult.data.task_id);
            if (bulkResult.errorCode) {
                clearTimeout(bulkTimeout);
                resultRecieved = true;
                result = bulkResult;
            } else if (
                bulkResult.data?.status &&
                (bulkResult.data?.status == 'FAILURE' || bulkResult.data?.status == 'SUCCESS')
            ) {
                clearTimeout(bulkTimeout);
                resultRecieved = true;
                result = bulkResult;
            }
        }
        if (timeoutReached && !resultRecieved) {
            return {
                statusCode: 408,
                errorCode: 'RequestTimeout',
                errorMessages: ['Ответ не пришёл до таймаута'],
            };
        }
        if (resultRecieved) {
            return result;
        }
    }

    /**
     * Загружает все сущности по meta.total
     * @param method getList-метод
     * @returns Результат вызова метода
     */
    static async getAll<T>(
        method: (api: CRMAPI, page: number, per_page: number) => Promise<RequestResult<T>>,
    ): Promise<RequestResult<T>> {
        let ca: CRMAPI;
        const creds = SettingsManager.getConnectionCredentials();
        if (creds?.accessToken == null) {
            return {
                statusCode: -1,
                errorCode: '-1',
                errorMessages: ['Срок действия сессии закончился.'],
            };
        } else {
            ca = new CRMAPI(creds.accessToken);
        }

        const probeResult = await method(ca, 1, 1);
        const probeTotal = probeResult.data.meta?.total;
        if (probeTotal == null) {
            return {
                statusCode: 404,
                errorCode: 'NotFound',
                errorMessages: ['Не найдено'],
            };
        } else {
            const totalResult = await method(ca, 1, probeTotal);
            return totalResult;
        }
    }
}

export { CRMAPIManager };
