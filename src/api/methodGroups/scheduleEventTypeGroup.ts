import { CRMAPIBase } from '@api/crmApiBase';
import { RequestResult } from '@api/responseModels/requestResult';
import { ScheduleEventTypeListResp } from '@api/responseModels/scheduleEventType/scheduleEventTypeListResponse';
import { ScheduleEventTypeResp } from '@api/responseModels/scheduleEventType/scheduleEventTypeResponse';
import { ScheduleEventTypeListParams } from 'types/api/listParams';
import { TScheduleEventType } from 'types/simulation/simulationScheduleEvent';

class ScheduleEventTypeGroup extends CRMAPIBase {
    async getScheduleEventTypeList(
        params: ScheduleEventTypeListParams,
    ): Promise<RequestResult<ScheduleEventTypeListResp>> {
        const result = await this.get<ScheduleEventTypeListResp>(
            '/simulation-schedule-event-types',
            { ...params },
        );
        return result;
    }

    async getScheduleEventType(
        schedule_event_type_id: TScheduleEventType['id'],
    ): Promise<RequestResult<ScheduleEventTypeResp>> {
        const result = await this.get<ScheduleEventTypeResp>(
            `/simulation-schedule-event-types/${schedule_event_type_id}`,
        );
        return result;
    }

    async createScheduleEventType(
        scheduleEventType: TScheduleEventType,
    ): Promise<RequestResult<ScheduleEventTypeResp>> {
        const result = await this.post<ScheduleEventTypeResp>('/simulation-schedule-event-types', {
            ...scheduleEventType,
        });
        return result;
    }

    async updateScheduleEventType(
        scheduleEventType: TScheduleEventType,
    ): Promise<RequestResult<ScheduleEventTypeResp>> {
        const result = await this.put<ScheduleEventTypeResp>(
            `/simulation-schedule-event-types/${scheduleEventType.id}`,
            { ...scheduleEventType },
        );
        return result;
    }

    async removeScheduleEventType(
        schedule_event_type_id: TScheduleEventType['id'],
    ): Promise<RequestResult<ScheduleEventTypeResp>> {
        const result = await this.delete<ScheduleEventTypeResp>(
            `/simulation-schedule-event-types/${schedule_event_type_id}`,
        );
        return result;
    }

    async restoreScheduleEventType(
        schedule_event_type_id: TScheduleEventType['id'],
    ): Promise<RequestResult<ScheduleEventTypeResp>> {
        const result = await this.post<ScheduleEventTypeResp>(
            `/simulation-schedule-event-types/${schedule_event_type_id}/restore`,
        );
        return result;
    }
}

export { ScheduleEventTypeGroup };
