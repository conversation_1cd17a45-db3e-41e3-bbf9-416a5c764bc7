import { CRMAPIBase } from '@api/crmApiBase';
import { RequestResult } from '@api/responseModels/requestResult';
import { SessionListResp } from '@api/responseModels/sessions/sessionListResponse';
import { SessionResp } from '@api/responseModels/sessions/sessionResponse';
import { SessionListParams } from 'types/api/listParams';
import { TSession } from 'types/session/session';
import { TSimulation } from 'types/simulation/simulation';

class SessionGroup extends CRMAPIBase {
    async createSession(simulation_id: TSimulation['id']): Promise<RequestResult<SessionResp>> {
        const result = await this.post<SessionResp>('/sessions', { simulation_id });
        return result;
    }

    async getSessionList(params: SessionListParams): Promise<RequestResult<SessionListResp>> {
        const result = await this.get<SessionListResp>('/sessions', { ...params });
        return result;
    }

    async getSession(session_id: TSession['id']): Promise<RequestResult<SessionResp>> {
        const result = await this.get<SessionResp>(`/sessions/${session_id}`);
        return result;
    }

    async deleteSession(session_id: TSession['id']): Promise<RequestResult<SessionResp>> {
        const result = await this.delete<SessionResp>(`/sessions/${session_id}`);
        return result;
    }
}

export { SessionGroup };
