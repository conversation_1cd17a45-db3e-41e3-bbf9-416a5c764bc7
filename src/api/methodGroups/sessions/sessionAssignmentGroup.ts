import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { SessionAssignmentListResp } from '@api/responseModels/sessionAssignments/sessionAssignmentListResponse';
import { SessionAssignmentResp } from '@api/responseModels/sessionAssignments/sessionAssignmentResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { SessionAssignmentListParams } from 'types/api/listParams';
import { TSession } from 'types/session/session';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { TUser } from 'types/user/user';

class SessionAssignmentGroup extends CRMAPIBase {
    async getSessionAssignmentList(
        params: SessionAssignmentListParams,
    ): Promise<RequestResult<SessionAssignmentListResp>> {
        const result = await this.get<SessionAssignmentListResp>('/session-assignments', {
            ...params,
        });
        return result;
    }

    async createSessionAssignment(
        session_id: TSession['id'],
        user_id: TUser['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.post<SessionAssignmentResp>('/session-assignments', {
            session_id,
            user_id,
        });
        return result;
    }

    async getSessionAssignment(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.get<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}`,
        );
        return result;
    }

    async deleteSessionAssignment(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.delete<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}`,
        );
        return result;
    }

    async SAstart(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.post<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}/start`,
        );
        return result;
    }

    async SApause(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.post<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}/pause`,
        );
        return result;
    }

    async SAresume(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.post<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}/resume`,
        );
        return result;
    }

    async SAstop(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.post<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}/stop`,
        );
        return result;
    }

    async SAfinish(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.post<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}/finish`,
        );
        return result;
    }

    async SAinfo(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SessionAssignmentResp>> {
        const result = await this.get<SessionAssignmentResp>(
            `/session-assignments/${session_assignment_id}/info`,
        );
        return result;
    }

    async bulkSessionAssignment(
        items: TBulkUpdate<
            | {
                  session_id: TSessionAssignment['session_id'];
                  user_id: TSessionAssignment['user_id'];
              }
            | {
                  id: TSessionAssignment['id'];
              }
        >,
    ): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/session-assignments/bulk', { items });
        return result;
    }

    async bulkResultSessionAssignment(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TSessionAssignment>>> {
        const result = await this.get<BulkResultResponse<TSessionAssignment>>(
            `/session-assignments/bulk/${task_id}`,
        );
        return result;
    }
}

export { SessionAssignmentGroup };
