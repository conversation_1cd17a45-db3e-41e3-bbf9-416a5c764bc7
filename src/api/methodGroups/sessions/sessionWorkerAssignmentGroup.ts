import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { SessionWorkerAssignmentListResp } from '@api/responseModels/sessionWorkerAssignments/sessionWorkerAssignmentListResponse';
import { SessionWorkerAssignmentResp } from '@api/responseModels/sessionWorkerAssignments/sessionWorkerAssignmentResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { SessionWorkerAssignmentListParams } from 'types/api/listParams';
import { TSessionWorkerAssignment } from 'types/session/sessionWorkerAssignment';

class SessionWorkerAssignmentGroup extends CRMAPIBase {
    async getSessionWorkerAssignmentList(
        params: SessionWorkerAssignmentListParams,
    ): Promise<RequestResult<SessionWorkerAssignmentListResp>> {
        const result = await this.get<SessionWorkerAssignmentListResp>(
            '/session-worker-assignments',
            { ...params },
        );
        return result;
    }

    async getSessionWorkerAssignment(
        session_worker_assignment_id: TSessionWorkerAssignment['id'],
    ): Promise<RequestResult<SessionWorkerAssignmentResp>> {
        const result = await this.get<SessionWorkerAssignmentResp>(
            `/session-worker-assignments/${session_worker_assignment_id}`,
        );
        return result;
    }

    async SWAassign(
        session_assignment_id: TSessionWorkerAssignment['id'],
        worker_uid: TSessionWorkerAssignment['worker_uid'],
        task_id: TSessionWorkerAssignment['task_id'],
    ): Promise<RequestResult<SessionWorkerAssignmentResp>> {
        const result = await this.post<SessionWorkerAssignmentResp>(
            '/session-worker-assignments/assign',
            {
                session_assignment_id,
                worker_uid,
                task_id,
            },
        );
        return result;
    }

    async SWAcancel(
        id: TSessionWorkerAssignment['id'],
        session_assignment_id: TSessionWorkerAssignment['session_assignment_id'],
    ): Promise<RequestResult<SessionWorkerAssignmentResp>> {
        const result = await this.post<SessionWorkerAssignmentResp>(
            '/session-worker-assignments/cancel',
            {
                session_assignment_id,
                session_worker_assignment_id: id,
            },
        );
        return result;
    }

    async SWAprioritize(
        id: TSessionWorkerAssignment['id'],
        session_assignment_id: TSessionWorkerAssignment['session_assignment_id'],
    ): Promise<RequestResult<SessionWorkerAssignmentResp>> {
        const result = await this.post<SessionWorkerAssignmentResp>(
            '/session-worker-assignments/prioritize',
            {
                session_assignment_id,
                session_worker_assignment_id: id,
            },
        );
        return result;
    }

    async bulkSessionWorkerAssignment(
        items: TBulkUpdate<TSessionWorkerAssignment>,
    ): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/session-worker-assignments/bulk', { items });
        return result;
    }

    async bulkResultSessionWorkerAssignment(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TSessionWorkerAssignment>>> {
        const result = await this.get<BulkResultResponse<TSessionWorkerAssignment>>(
            `/session-worker-assignments/bulk/${task_id}`,
        );
        return result;
    }
}

export { SessionWorkerAssignmentGroup };
