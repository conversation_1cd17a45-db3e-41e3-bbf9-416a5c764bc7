import { CRMAPIBase } from '@api/crmApiBase';
import { RequestResult } from '@api/responseModels/requestResult';
import { CTWResp } from '@api/responseModels/sessionAssignmentInfo/currentTaskWorkersResponse';
import { CWTResp } from '@api/responseModels/sessionAssignmentInfo/currentWorkerTasksResponse';
import { SAIResp } from '@api/responseModels/sessionAssignmentInfo/sessionAssignmentInfoResponse';
import { STExtListResp } from '@api/responseModels/sessionAssignmentInfo/sessionTaskListExtendedResponse';
import { SWExtListResp } from '@api/responseModels/sessionAssignmentInfo/sessionWorkerListExtendedResponse';
import { STPResp } from '@api/responseModels/sessionAssignmentInfo/sesssionTaskProgressResponse';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { DayTick } from 'types/common';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { TSessionTaskExtended } from 'types/session/sessionTask';
import { TSimWorker } from 'types/simulation/simulationWorker';

class SessionAssignmentInfoGroup extends CRMAPIBase {
    async SAIgetCWT(
        session_assignment_id: TSessionAssignment['id'],
        worker_uid: TSimWorker['uid'],
        day: DayTick['day'] | null,
    ): Promise<RequestResult<CWTResp>> {
        const result = await this.get<CWTResp>('/session-assignment-info/current-worker-tasks', {
            session_assignment_id,
            worker_uid,
            day,
        });
        return result;
    }

    async SAIgetCTW(
        session_assignment_id: TSessionAssignment['id'],
        task_id: TSessionTaskExtended['id'],
    ): Promise<RequestResult<CTWResp>> {
        const result = await this.get<CTWResp>('/session-assignment-info/current-task-workers', {
            session_assignment_id,
            task_id,
        });
        return result;
    }

    async SAIgetInfo(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SAIResp>> {
        const result = await this.get<SAIResp>('/session-assignment-info/info', {
            session_assignment_id,
        });
        return result;
    }

    async SAIgetWorkersExtended(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SWExtListResp>> {
        const result = await this.get<SWExtListResp>('/session-assignment-info/workers-extended', {
            session_assignment_id,
        });
        return result;
    }

    async SAIgetTasksExtended(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<STExtListResp>> {
        const result = await this.get<STExtListResp>('/session-assignment-info/tasks-extended', {
            session_assignment_id,
        });
        return result;
    }

    async SAIgetSimulation(
        session_assignment_id: TSessionAssignment['id'],
    ): Promise<RequestResult<SimulationResp>> {
        const result = await this.get<SimulationResp>('/session-assignment-info/simulation', {
            session_assignment_id,
        });
        return result;
    }

    async SAIgetSTP(
        session_assignment_id: TSessionAssignment['id'],
        //week: number | null,
        //day: number | null,
        //task_id: TSessionTask["order_id"] | null,
        //worker_uid: TSessionWorker["worker_uid"] | null,
        //event_uid: TSimEvent["id"] | null,
    ): Promise<RequestResult<STPResp>> {
        const result = await this.get<STPResp>(
            '/session-assignment-info/task-progresses',
            { session_assignment_id },
            true,
        );
        return result;
    }
}

export { SessionAssignmentInfoGroup };
