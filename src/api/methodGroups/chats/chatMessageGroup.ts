import { CRMAPIBase } from '@api/crmApiBase';
import { ChatMessageListResp } from '@api/responseModels/chatMessage/chatMessageListResponse';
import { ChatMessageResp } from '@api/responseModels/chatMessage/chatMessageResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { ChatMessageListParams } from 'types/api/listParams';
import { TChat } from 'types/chats/chats';

class ChatMessageGroup extends CRMAPIBase {
    async getChatMessageList(
        params: ChatMessageListParams,
    ): Promise<RequestResult<ChatMessageListResp>> {
        const result = await this.get<ChatMessageListResp>('/chat-messages', { ...params });
        return result;
    }

    async sendMessage(
        chat_id: TChat['id'],
        text: string,
        attachments: string[],
        statuses: string[],
    ): Promise<RequestResult<ChatMessageResp>> {
        const result = await this.post<ChatMessageResp>('/chat-messages/send-message', {
            chat_id,
            text,
            attachments,
            statuses,
        });
        return result;
    }
}

export { ChatMessageGroup };
