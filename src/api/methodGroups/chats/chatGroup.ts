import { CRMAPIBase } from '@api/crmApiBase';
import { ChatListResp } from '@api/responseModels/chat/chatListResponse';
import { ChatResp } from '@api/responseModels/chat/chatResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { ChatListParams } from 'types/api/listParams';
import { TChat } from 'types/chats/chats';
import { TUser } from 'types/user/user';

class ChatGroup extends CRMAPIBase {
    async getChatList(params: ChatListParams): Promise<RequestResult<ChatListResp>> {
        const result = await this.get<ChatListResp>('/chats', { ...params });
        return result;
    }

    async createChat(data: {
        id: TChat['id'];
        chat_name: TChat['chat_name'];
        creator_id: TChat['creator_id'];
        type: TChat['type'];
        users: TUser['id'][];
    }): Promise<RequestResult<ChatResp>> {
        const result = await this.post<ChatResp>('/chats', {
            ...data,
        });
        return result;
    }

    async getChat(id: TChat['id']): Promise<RequestResult<ChatResp>> {
        const result = await this.get<ChatResp>(`/chats/${id}`);
        return result;
    }

    async updateChat(data: {
        id: TChat['id'];
        chat_name: TChat['chat_name'];
        creator_id: TChat['creator_id'];
        type: TChat['type'];
        users: TUser['id'][];
    }): Promise<RequestResult<ChatResp>> {
        const result = await this.put<ChatResp>(`/chats/${data.id}`, {
            ...data,
        });
        return result;
    }

    async deleteChat(id: TChat['id']): Promise<RequestResult<ChatResp>> {
        const result = await this.delete<ChatResp>(`/chats/${id}`);
        return result;
    }

    async restoreChat(id: TChat['id']): Promise<RequestResult<ChatResp>> {
        const result = await this.post<ChatResp>(`/chats/${id}/restore`);
        return result;
    }

    async registerChatSocket(
        socket_id: string,
    ): Promise<RequestResult<{ data: { id: string; socket_id: string } }>> {
        const result = await this.post<{ data: { id: string; socket_id: string } }>(
            '/chats/register-socket',
            {},
            { socket_id },
        );
        return result;
    }

    async authChat(): Promise<RequestResult<{ data: { auth: string } }>> {
        const result = await this.post<{ data: { auth: string } }>('/chats/auth-chat');
        return result;
    }

    async chatWithManager(): Promise<RequestResult<ChatResp>> {
        const result = await this.get<ChatResp>('/chats/chat-with-manager');
        return result;
    }
}

export { ChatGroup };
