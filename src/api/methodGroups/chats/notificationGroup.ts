import { CRMAPIBase } from '@api/crmApiBase';
import { NotificationListResp } from '@api/responseModels/notifications/notificationListResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { NotificationListParams } from 'types/api/listParams';

class NotificationGroup extends CRMAPIBase {
    async getNotificationList(
        params: NotificationListParams,
    ): Promise<RequestResult<NotificationListResp>> {
        const result = await this.get<NotificationListResp>('/notifications', { ...params });
        return result;
    }
}

export { NotificationGroup };
