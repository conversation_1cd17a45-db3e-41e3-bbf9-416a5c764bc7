import { CRMAPIBase } from '@api/crmApiBase';
import { ChatUserListResp } from '@api/responseModels/chatUser/chatUserListResponse';
import { ChatUserResp } from '@api/responseModels/chatUser/chatUserResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { ChatUserListParams } from 'types/api/listParams';
import { TChat } from 'types/chats/chats';
import { TUser } from 'types/user/user';

class ChatUserGroup extends CRMAPIBase {
    async getChatUserList(params: ChatUserListParams): Promise<RequestResult<ChatUserListResp>> {
        const result = await this.get<ChatUserListResp>('/user-chats', { ...params });
        return result;
    }

    async addUserToChat(
        chat_id: TChat['id'],
        user_id: TUser['id'],
    ): Promise<RequestResult<ChatUserResp>> {
        const result = await this.post<ChatUserResp>('/user-chats', { chat_id, user_id });
        return result;
    }

    async banChatUser(
        chat_id: TChat['id'],
        user_id: TUser['id'],
    ): Promise<RequestResult<ChatUserResp>> {
        const result = await this.post<ChatUserResp>('/user-chats/ban', { chat_id, user_id });
        return result;
    }

    async unbanChatUser(
        chat_id: TChat['id'],
        user_id: TUser['id'],
    ): Promise<RequestResult<ChatUserResp>> {
        const result = await this.post<ChatUserResp>('/user-chats/unban', { chat_id, user_id });
        return result;
    }

    async makeUserChatAdmin(
        chat_id: TChat['id'],
        user_id: TUser['id'],
    ): Promise<RequestResult<ChatUserResp>> {
        const result = await this.post<ChatUserResp>('/user-chats/make-admin', {
            chat_id,
            user_id,
        });
        return result;
    }

    async revokeUserChatAdmin(
        chat_id: TChat['id'],
        user_id: TUser['id'],
    ): Promise<RequestResult<ChatUserResp>> {
        const result = await this.post<ChatUserResp>('/user-chats/unmake-admin', {
            chat_id,
            user_id,
        });
        return result;
    }
}

export { ChatUserGroup };
