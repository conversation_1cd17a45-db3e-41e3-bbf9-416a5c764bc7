import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterResp } from '@api/responseModels/filters/filterResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { ListParams } from 'types/api/listParams';
import { TFilter } from 'types/filter';

class FilterGroup extends CRMAPIBase {
    async getFilterList(params: ListParams): Promise<RequestResult<FilterListResp>> {
        const result = await this.get<FilterListResp>('/filters', {
            ...params,
        });
        return result;
    }

    async getFilter(filter_id: number): Promise<RequestResult<FilterResp>> {
        const result = await this.get<FilterResp>(`/filters/${filter_id}`);
        return result;
    }

    async createFilter(filter: TFilter): Promise<RequestResult<FilterResp>> {
        const result = await this.post<FilterResp>('/filters', { ...filter });
        return result;
    }

    async updateFilter(filter: TFilter): Promise<RequestResult<FilterResp>> {
        const result = await this.put<FilterResp>(`/filters/${filter.id}`, { ...filter });
        return result;
    }

    async deleteFilter(filter_id: number): Promise<RequestResult<FilterResp>> {
        const result = await this.delete<FilterResp>(`/filters/${filter_id}`);
        return result;
    }

    async restoreFilter(filter_id: number): Promise<RequestResult<FilterResp>> {
        const result = await this.post<FilterResp>(`/filters/${filter_id}/restore`);
        return result;
    }

    async bulkFilter(items: TBulkUpdate<TFilter>): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/filters/bulk', { items });
        return result;
    }

    async bulkResultFilter(task_id: string): Promise<RequestResult<BulkResultResponse<TFilter>>> {
        const result = await this.get<BulkResultResponse<TFilter>>(`/filters/bulk/${task_id}`);
        return result;
    }
}

export { FilterGroup };
