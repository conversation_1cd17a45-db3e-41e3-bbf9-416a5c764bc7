import { CRMAPIBase } from '@api/crmApiBase';
import { RequestResult } from '@api/responseModels/requestResult';
import { RoleListResp } from '@api/responseModels/roles/roleListResponse';
import { RoleResp } from '@api/responseModels/roles/roleResponse';

class RoleGroup extends CRMAPIBase {
    async getRoleList(): Promise<RequestResult<RoleListResp>> {
        const result = await this.get<RoleListResp>('/roles');
        return result;
    }

    async getRole(role_id: number): Promise<RequestResult<RoleResp>> {
        const result = await this.get<RoleResp>(`/roles/${role_id}`);
        return result;
    }

    async createRole(
        id: number,
        name: string,
        permissions: number[],
    ): Promise<RequestResult<RoleResp>> {
        const result = await this.post<RoleResp>('/roles', { id, name, permissions });
        return result;
    }

    async updateRole(
        id: number,
        name: string,
        permissions: number[],
    ): Promise<RequestResult<RoleResp>> {
        const result = await this.put<RoleResp>(`/roles/${id}`, { id, name, permissions });
        return result;
    }
}

export { RoleGroup };
