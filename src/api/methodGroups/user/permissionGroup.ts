import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { PermissionListResp } from '@api/responseModels/permissions/permissionListResponse';
import { PermissionResp } from '@api/responseModels/permissions/permissionResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { PermissionListParams } from 'types/api/listParams';
import { TPermission } from 'types/user/permissions';

class PermissionGroup extends CRMAPIBase {
    async getPermissionList(
        params: PermissionListParams,
    ): Promise<RequestResult<PermissionListResp>> {
        const result = await this.get<PermissionListResp>('/permissions', {
            ...params,
        });
        return result;
    }

    async getPermission(permission_id: number): Promise<RequestResult<PermissionResp>> {
        const result = await this.get<PermissionResp>(`/permissions/${permission_id}`);
        return result;
    }

    async createPermission(permission: TPermission): Promise<RequestResult<PermissionResp>> {
        const result = await this.post<PermissionResp>('/permissions', { ...permission });
        return result;
    }

    async updatePermission(permission: TPermission): Promise<RequestResult<PermissionResp>> {
        const result = await this.put<PermissionResp>(`/permissions/${permission.id}`, {
            ...permission,
        });
        return result;
    }

    async deletePermission(permission_id: number): Promise<RequestResult<PermissionResp>> {
        const result = await this.delete<PermissionResp>(`/permissions/${permission_id}`);
        return result;
    }

    async restorePermission(permission_id: number): Promise<RequestResult<PermissionResp>> {
        const result = await this.post<PermissionResp>(`/permissions/${permission_id}/restore`);
        return result;
    }

    async bulkPermission(items: TBulkUpdate<TPermission>): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/permissions/bulk', { items });
        return result;
    }

    async bulkResultPermission(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TPermission>>> {
        const result = await this.get<BulkResultResponse<TPermission>>(
            `/permissions/bulk/${task_id}`,
        );
        return result;
    }
}

export { PermissionGroup };
