import { CRMAPIBase } from '@api/crmApiBase';
import { RequestResult } from '@api/responseModels/requestResult';
import { UserListResp } from '@api/responseModels/users/userListResp';
import { UserResp } from '@api/responseModels/users/userResp';
import { UserListParams } from 'types/api/listParams';
import { TUser } from 'types/user/user';

class UserGroup extends CRMAPIBase {
    async currentUser(silent = false): Promise<RequestResult<UserResp>> {
        const result = await this.get<UserResp>('/users/current', null, silent);
        return result;
    }

    async getUser(uid: string): Promise<RequestResult<UserResp>> {
        const result = await this.get<UserResp>(`/users/${uid}`);
        return result;
    }

    async getUserList(params: UserListParams): Promise<RequestResult<UserListResp>> {
        const result = await this.get<UserListResp>('/users', {
            ...params,
            filters: {
                deleted: 'null',
            },
            role:
                params.role == 'staff'
                    ? null
                    : params.role == 'clients'
                      ? 'Roles.Client'
                      : params.role,
        });
        return result;
    }

    async deleteUser(uid: string): Promise<RequestResult<UserResp>> {
        const result = await this.delete<UserResp>(`/users/${uid}`);
        return result;
    }

    async restoreUser(uid: string): Promise<RequestResult<UserResp>> {
        const result = await this.post<UserResp>(`/users/${uid}/restore`);
        return result;
    }

    async updateUser(user: TUser & { password?: string }): Promise<RequestResult<UserResp>> {
        const result = await this.put<UserResp>(`/users/${user.id}`, { ...user });
        return result;
    }

    async banUser(uid: string): Promise<RequestResult<UserResp>> {
        const result = await this.post<UserResp>(`/users/${uid}/ban`);
        return result;
    }

    async unbanUser(uid: string): Promise<RequestResult<UserResp>> {
        const result = await this.post<UserResp>(`/users/${uid}/unban`);
        return result;
    }

    async changeUserRole(uid: string, role: TUser['role']): Promise<RequestResult<UserResp>> {
        const result = await this.post<UserResp>(`/users/${uid}/change-role`, { role: role });
        return result;
    }
}

export { UserGroup };
