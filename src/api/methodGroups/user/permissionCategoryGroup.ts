import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { PermissionCategoryListResp } from '@api/responseModels/permissions/permissionCategoryListResponse';
import { PermissionCategoryResp } from '@api/responseModels/permissions/permissionCategoryResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { PermissionListParams } from 'types/api/listParams';
import { TPermissionCategory } from 'types/user/permissions';

class PermissionCategoryGroup extends CRMAPIBase {
    async getPermissionCategoryList(
        params: PermissionListParams,
    ): Promise<RequestResult<PermissionCategoryListResp>> {
        const result = await this.get<PermissionCategoryListResp>('/permission-categories', {
            ...params,
        });
        return result;
    }

    async getPermissionCategory(
        permission_category_id: number,
    ): Promise<RequestResult<PermissionCategoryResp>> {
        const result = await this.get<PermissionCategoryResp>(
            `/permission-categories/${permission_category_id}`,
        );
        return result;
    }

    async createPermissionCategory(
        permissionCategory: TPermissionCategory,
    ): Promise<RequestResult<PermissionCategoryResp>> {
        const result = await this.post<PermissionCategoryResp>('/permission-categories', {
            ...permissionCategory,
        });
        return result;
    }

    async updatePermissionCategory(
        permissionCategory: TPermissionCategory,
    ): Promise<RequestResult<PermissionCategoryResp>> {
        const result = await this.put<PermissionCategoryResp>(
            `/permission-categories/${permissionCategory.id}`,
            { ...permissionCategory },
        );
        return result;
    }

    async deletePermissionCategory(
        permission_category_id: number,
    ): Promise<RequestResult<PermissionCategoryResp>> {
        const result = await this.delete<PermissionCategoryResp>(
            `/permission-categories/${permission_category_id}`,
        );
        return result;
    }

    async restorePermissionCategory(
        permission_category_id: number,
    ): Promise<RequestResult<PermissionCategoryResp>> {
        const result = await this.post<PermissionCategoryResp>(
            `/permission-categories/${permission_category_id}/restore`,
        );
        return result;
    }

    async bulkPermissionCategory(
        items: TBulkUpdate<TPermissionCategory>,
    ): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/permission-categories/bulk', { items });
        return result;
    }

    async bulkResultPermissionCategory(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TPermissionCategory>>> {
        const result = await this.get<BulkResultResponse<TPermissionCategory>>(
            `/permission-categories/bulk/${task_id}`,
        );
        return result;
    }
}

export { PermissionCategoryGroup };
