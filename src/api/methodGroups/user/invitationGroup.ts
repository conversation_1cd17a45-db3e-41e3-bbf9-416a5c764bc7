import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { InvitationListResp } from '@api/responseModels/invitations/invitationListResponse';
import { InvitationResp } from '@api/responseModels/invitations/invitationResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { LoginResp } from '@api/responseModels/security/loginResp';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { InvitationListParams } from 'types/api/listParams';
import { TInvitation } from 'types/invitations/invitation';

class InvitationGroup extends CRMAPIBase {
    async getInvitationList(
        params: InvitationListParams,
    ): Promise<RequestResult<InvitationListResp>> {
        const result = await this.get<InvitationListResp>('/invitations', { ...params });
        return result;
    }

    async getInvitation(uid: string): Promise<RequestResult<InvitationResp>> {
        const result = await this.get<InvitationResp>(`/invitations/${uid}`);
        return result;
    }

    async registerViaInvitation(uid: string, password: string): Promise<RequestResult<LoginResp>> {
        const result = await this.post<LoginResp>(`/invitations/${uid}/register`, { password });
        return result;
    }

    async createInvitation(inv: TInvitation): Promise<RequestResult<InvitationResp>> {
        const result = await this.post<InvitationResp>('/invitations', { ...inv });
        return result;
    }

    async deleteInvitation(uid: string): Promise<RequestResult<InvitationResp>> {
        const result = await this.delete<InvitationResp>(`/invitations/${uid}`);
        return result;
    }

    async restoreInvitation(uid: string): Promise<RequestResult<InvitationResp>> {
        const result = await this.post<InvitationResp>(`/invitations/${uid}/restore`);
        return result;
    }

    async updateInvitation(inv: TInvitation): Promise<RequestResult<InvitationResp>> {
        const result = await this.put<InvitationResp>(`/invitations/${inv.id}`, { ...inv });
        return result;
    }

    async bulkInvitation(items: TBulkUpdate<TInvitation>): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/invitations/bulk', { items });
        return result;
    }

    async bulkResultInvitation(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TInvitation>>> {
        const result = await this.get<BulkResultResponse<TInvitation>>(
            `/invitations/bulk/${task_id}`,
        );
        return result;
    }

    async resendInvitationEmails(
        invIdList: TInvitation['id'][],
    ): Promise<RequestResult<InvitationListResp>> {
        const result = await this.post<InvitationListResp>('/invitations/resend-emails', {
            invitation_ids: invIdList,
        });
        return result;
    }
}

export { InvitationGroup };
