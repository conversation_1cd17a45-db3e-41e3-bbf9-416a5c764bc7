import { CRMAPIBase } from '@api/crmApiBase';
import { RequestResult } from '@api/responseModels/requestResult';
import { LoginResp } from '@api/responseModels/security/loginResp';

class SecurityGroup extends CRMAPIBase {
    async login(login: string, password: string): Promise<RequestResult<LoginResp>> {
        const result = await this.post<LoginResp>('/security/login', {
            login: login,
            password: password,
        });
        return result;
    }

    async register(email: string, login: string, password: string): Promise<RequestResult<any>> {
        const result = await this.post<any>('/security/register', {
            email: email,
            login: login,
            password: password,
        });
        return result;
    }

    async recovery(email: string, login: string): Promise<RequestResult<any>> {
        const result = await this.post<any>('/security/recovery', { email: email, login: login });
        return result;
    }

    async refresh(refresh: string): Promise<RequestResult<LoginResp>> {
        const result = await this.post<LoginResp>('/security/refresh', { refresh_token: refresh });
        return result;
    }

    async logout(): Promise<RequestResult<any>> {
        const result = await this.post<any>('/security/logout', {});
        return result;
    }

    async confirmRegistration(token: string): Promise<RequestResult<any>> {
        const result = await this.get<any>(`/security/confirm-registration/${token}`);
        return result;
    }
}

export { SecurityGroup };
