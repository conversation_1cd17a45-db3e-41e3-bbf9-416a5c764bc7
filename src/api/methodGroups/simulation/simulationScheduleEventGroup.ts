import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { SimScheduleEventListResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventListResponse';
import { SimScheduleEventResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { SimulationScheduleEventListParams } from 'types/api/listParams';
import { TSimulation } from 'types/simulation/simulation';
import { TSimScheduleEvent } from 'types/simulation/simulationScheduleEvent';

class SimulationScheduleEventGroup extends CRMAPIBase {
    async getSimScheduleEventList(
        params: SimulationScheduleEventListParams,
    ): Promise<RequestResult<SimScheduleEventListResp>> {
        const result = await this.get<SimScheduleEventListResp>(
            '/simulation-schedule-events',
            { ...params },
            true,
        );
        return result;
    }

    async getSimScheduleEvent(
        simulation_id: TSimulation['id'],
        schedule_event_id: TSimScheduleEvent['id'],
    ): Promise<RequestResult<SimScheduleEventResp>> {
        const result = await this.get<SimScheduleEventResp>(
            `/simulation-schedule-events/${schedule_event_id}`,
            { simulation_id },
        );
        return result;
    }

    async createSimScheduleEvent(
        scheduleEvent: TSimScheduleEvent,
    ): Promise<RequestResult<SimScheduleEventResp>> {
        const result = await this.post<SimScheduleEventResp>('/simulation-schedule-events', {
            ...scheduleEvent,
        });
        return result;
    }

    async updateSimScheduleEvent(
        scheduleEvent: TSimScheduleEvent,
    ): Promise<RequestResult<SimScheduleEventResp>> {
        const result = await this.put<SimScheduleEventResp>(
            `/simulation-schedule-events/${scheduleEvent.id}`,
            { ...scheduleEvent },
        );
        return result;
    }

    async removeSimScheduleEvent(
        simulation_id: TSimulation['id'],
        schedule_event_id: TSimScheduleEvent['id'],
    ): Promise<RequestResult<SimScheduleEventResp>> {
        const result = await this.delete<SimScheduleEventResp>(
            `/simulation-schedule-events/${schedule_event_id}`,
            { simulation_id },
        );
        return result;
    }

    async bulkSimScheduleEvent(
        items: TBulkUpdate<TSimScheduleEvent>,
    ): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/simulation-schedule-events/bulk', { items });
        return result;
    }

    async bulkResultSimSheduleEvent(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TSimScheduleEvent>>> {
        const result = await this.get<BulkResultResponse<TSimScheduleEvent>>(
            `/simulation-schedule-events/bulk/${task_id}`,
        );
        return result;
    }
}

export { SimulationScheduleEventGroup };
