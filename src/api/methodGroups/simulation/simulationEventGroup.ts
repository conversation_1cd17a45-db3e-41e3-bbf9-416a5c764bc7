import { CRMAPIBase } from '@api/crmApiBase';
import { RequestResult } from '@api/responseModels/requestResult';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { SimEventResp } from '@api/responseModels/simulations/simulationEvents/simulationEventResponse';
import { SimulationEventListParams } from 'types/api/listParams';
import { TSimEvent } from 'types/simulation/simulationEvent';

class SimulationEventGroup extends CRMAPIBase {
    async getSimEventList(
        params: SimulationEventListParams,
    ): Promise<RequestResult<SimEventListResp>> {
        const result = await this.get<SimEventListResp>('/simulation-events', { ...params });
        return result;
    }

    async getSimEvent(
        simulation_id: number,
        event_uid: string,
    ): Promise<RequestResult<SimEventResp>> {
        const result = await this.get<SimEventResp>(`/simulation-events/${event_uid}`, {
            simulation_id,
        });
        return result;
    }

    async createSimEvent(event: TSimEvent): Promise<RequestResult<SimEventResp>> {
        const result = await this.post<SimEventResp>('/simulation-events', { ...event });
        return result;
    }

    async updateSimEvent(event: TSimEvent): Promise<RequestResult<SimEventResp>> {
        const result = await this.put<SimEventResp>(`/simulation-events/${event.uid}`, {
            ...event,
        });
        return result;
    }

    async removeSimEvent(
        simulation_id: number,
        event_uid: string,
    ): Promise<RequestResult<SimEventResp>> {
        const result = await this.delete<SimEventResp>(`/simulation-events/${event_uid}`, {
            simulation_id,
        });
        return result;
    }

    async restoreSimEvent(
        simulation_id: number,
        event_uid: string,
    ): Promise<RequestResult<SimEventResp>> {
        const result = await this.post<SimEventResp>(`/simulation-events/${event_uid}/restore`, {
            simulation_id,
        });
        return result;
    }
}

export { SimulationEventGroup };
