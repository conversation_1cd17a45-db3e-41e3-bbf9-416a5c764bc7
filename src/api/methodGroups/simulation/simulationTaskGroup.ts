import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { SimTaskResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { SimulationTaskListParams } from 'types/api/listParams';
import { TSimTask } from 'types/simulation/simulationTask';

class SimulationTaskGroup extends CRMAPIBase {
    async getSimTaskList(
        params: SimulationTaskListParams,
    ): Promise<RequestResult<SimTaskListResp>> {
        const result = await this.get<SimTaskListResp>('/simulation-tasks', { ...params });
        return result;
    }

    async getSimTask(simulation_id: number, uid: string): Promise<RequestResult<SimTaskResp>> {
        const result = await this.get<SimTaskResp>(`/simulation-tasks/${uid}`, { simulation_id });
        return result;
    }

    async getSimTaskById(
        simulation_id: number,
        simulation_task_id: number,
    ): Promise<RequestResult<SimTaskResp>> {
        const result = await this.get<SimTaskResp>(
            `/simulation-tasks/${simulation_id}/${simulation_task_id}`,
        );
        return result;
    }

    async createSimTask(st: TSimTask): Promise<RequestResult<SimTaskResp>> {
        const result = await this.post<SimTaskResp>('/simulation-tasks', { ...st });
        return result;
    }

    async updateSimTask(st: TSimTask): Promise<RequestResult<SimTaskResp>> {
        const result = await this.put<SimTaskResp>(`/simulation-tasks/${st.uid}`, { ...st });
        return result;
    }

    async removeSimTask(simulation_id: number, uid: string): Promise<RequestResult<SimTaskResp>> {
        const result = await this.delete<SimTaskResp>(`/simulation-tasks/${uid}`, {
            simulation_id,
        });
        return result;
    }

    async restoreSimTask(simulation_id: number, uid: string): Promise<RequestResult<SimTaskResp>> {
        const result = await this.post<SimTaskResp>(`/simulation-tasks/${uid}/restore`, {
            simulation_id,
        });
        return result;
    }

    async bulkSimTask(items: TBulkUpdate<TSimTask>): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/simulation-tasks/bulk', { items });
        return result;
    }

    async bulkResultSimTask(task_id: string): Promise<RequestResult<BulkResultResponse<TSimTask>>> {
        const result = await this.get<BulkResultResponse<TSimTask>>(
            `/simulation-tasks/bulk/${task_id}`,
        );
        return result;
    }
}

export { SimulationTaskGroup };
