import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { SimulationListResp } from '@api/responseModels/simulations/simulationListResponse';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { SimulationListParams } from 'types/api/listParams';
import { TSimulation } from 'types/simulation/simulation';

class SimulationGroup extends CRMAPIBase {
    async getSimulationList(
        params: SimulationListParams,
    ): Promise<RequestResult<SimulationListResp>> {
        const result = await this.get<SimulationListResp>('/simulations', { ...params });
        return result;
    }

    async getSimulation(id: number): Promise<RequestResult<SimulationResp>> {
        const result = await this.get<SimulationResp>(`/simulations/${id}`);
        return result;
    }

    async createSimulation(sim: TSimulation): Promise<RequestResult<SimulationResp>> {
        const result = await this.post<SimulationResp>('/simulations', { ...sim });
        return result;
    }

    async updateSimulation(sim: TSimulation): Promise<RequestResult<SimulationResp>> {
        const result = await this.put<SimulationResp>(`/simulations/${sim.id}`, { ...sim });
        return result;
    }

    async removeSimulation(id: number): Promise<RequestResult<SimulationResp>> {
        const result = await this.delete<SimulationResp>(`/simulations/${id}`);
        return result;
    }

    async restoreSimulation(id: number): Promise<RequestResult<SimulationResp>> {
        const result = await this.post<SimulationResp>(`/simulations/${id}/restore`);
        return result;
    }

    async bulkSimulation(items: TBulkUpdate<TSimulation>): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/simulations/bulk', { items });
        return result;
    }

    async bulkResultSimulation(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TSimulation>>> {
        const result = await this.get<BulkResultResponse<TSimulation>>(
            `/simulations/bulk/${task_id}`,
        );
        return result;
    }
}

export { SimulationGroup };
