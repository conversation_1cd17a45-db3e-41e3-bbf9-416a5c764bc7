import { CRMAPIBase } from '@api/crmApiBase';
import { BulkResponse } from '@api/responseModels/bulkResponse';
import { RequestResult } from '@api/responseModels/requestResult';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { SimWorkerResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { SimulationWorkerListParams } from 'types/api/listParams';
import { TSimWorker } from 'types/simulation/simulationWorker';

class SimulationWorkerGroup extends CRMAPIBase {
    async getSimWorkerList(
        params: SimulationWorkerListParams,
    ): Promise<RequestResult<SimWorkerListResp>> {
        const result = await this.get<SimWorkerListResp>('/simulation-workers', { ...params });
        return result;
    }

    async getSimWorker(uid: string): Promise<RequestResult<SimWorkerResp>> {
        const result = await this.get<SimWorkerResp>(`/simulation-workers/${uid}`);
        return result;
    }

    async createSimWorker(sw: TSimWorker): Promise<RequestResult<SimWorkerResp>> {
        const result = await this.post<SimWorkerResp>('/simulation-workers', { ...sw });
        return result;
    }

    async updateSimWorker(sw: TSimWorker): Promise<RequestResult<SimWorkerResp>> {
        const result = await this.put<SimWorkerResp>(`/simulation-workers/${sw.uid}`, { ...sw });
        return result;
    }

    async removeSimWorker(
        simulation_id: number,
        uid: string,
    ): Promise<RequestResult<SimWorkerResp>> {
        const result = await this.delete<SimWorkerResp>(`/simulation-workers/${uid}`, {
            simulation_id,
        });
        return result;
    }

    async restoreSimWorker(
        simulation_id: number,
        uid: string,
    ): Promise<RequestResult<SimWorkerResp>> {
        const result = await this.post<SimWorkerResp>(`/simulation-workers/${uid}/restore`, {
            simulation_id,
        });
        return result;
    }

    async bulkSimWorker(items: TBulkUpdate<TSimWorker>): Promise<RequestResult<BulkResponse>> {
        const result = await this.post<BulkResponse>('/simulation-workers/bulk', { items });
        return result;
    }

    async bulkResultSimWorker(
        task_id: string,
    ): Promise<RequestResult<BulkResultResponse<TSimWorker>>> {
        const result = await this.get<BulkResultResponse<TSimWorker>>(
            `/simulation-workers/bulk/${task_id}`,
        );
        return result;
    }
}

export { SimulationWorkerGroup };
