import { CRMAPIBase } from './crmApiBase';
import { InvitationGroup } from './methodGroups/user/invitationGroup';
import { SecurityGroup } from './methodGroups/user/securityGroup';
import { SimulationEventGroup } from './methodGroups/simulation/simulationEventGroup';
import { SimulationGroup } from './methodGroups/simulation/simulationGroup';
import { SimulationTaskGroup } from './methodGroups/simulation/simulationTaskGroup';
import { SimulationWorkerGroup } from './methodGroups/simulation/simulationWorkerGroup';
import { UserGroup } from './methodGroups/user/userGroup';
import { RoleGroup } from './methodGroups/user/roleGroup';
import { PermissionCategoryGroup } from './methodGroups/user/permissionCategoryGroup';
import { PermissionGroup } from './methodGroups/user/permissionGroup';
import { FilterGroup } from './methodGroups/filterGroup';
import { ChatGroup } from './methodGroups/chats/chatGroup';
import { ChatMessageGroup } from './methodGroups/chats/chatMessageGroup';
import { ChatUserGroup } from './methodGroups/chats/chatUserGroup';
import { NotificationGroup } from './methodGroups/chats/notificationGroup';
import { SessionGroup } from './methodGroups/sessions/sessionGroup';
import { SessionAssignmentGroup } from './methodGroups/sessions/sessionAssignmentGroup';
import { SessionWorkerAssignmentGroup } from './methodGroups/sessions/sessionWorkerAssignmentGroup';
import { SessionAssignmentInfoGroup } from './methodGroups/sessions/sessionAssignmentInfoGroup';
import { SimulationScheduleEventGroup } from './methodGroups/simulation/simulationScheduleEventGroup';
import { ScheduleEventTypeGroup } from './methodGroups/scheduleEventTypeGroup';

// TODO разобраться почему это реализовано таким образом
// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging
class CRMAPI extends CRMAPIBase {}

// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging
interface CRMAPI
    extends SecurityGroup,
        UserGroup,
        InvitationGroup,
        RoleGroup,
        PermissionCategoryGroup,
        PermissionGroup,
        FilterGroup,
        ChatGroup,
        ChatMessageGroup,
        ChatUserGroup,
        NotificationGroup,
        SimulationGroup,
        SimulationTaskGroup,
        SimulationWorkerGroup,
        SimulationEventGroup,
        SessionGroup,
        SessionAssignmentGroup,
        SessionWorkerAssignmentGroup,
        SessionAssignmentInfoGroup,
        SimulationScheduleEventGroup,
        ScheduleEventTypeGroup {}

function applyMixins(derivedCtor: any, baseCtors: any[]) {
    baseCtors.forEach((baseCtor) => {
        Object.getOwnPropertyNames(baseCtor.prototype).forEach((name) => {
            if (name !== 'constructor') {
                derivedCtor.prototype[name] = baseCtor.prototype[name];
            }
        });
    });
}

applyMixins(CRMAPI, [
    SecurityGroup,
    UserGroup,
    InvitationGroup,
    RoleGroup,
    PermissionCategoryGroup,
    PermissionGroup,
    FilterGroup,
    ChatGroup,
    ChatMessageGroup,
    ChatUserGroup,
    NotificationGroup,
    SimulationGroup,
    SimulationTaskGroup,
    SimulationWorkerGroup,
    SimulationEventGroup,
    SessionGroup,
    SessionAssignmentGroup,
    SessionWorkerAssignmentGroup,
    SessionAssignmentInfoGroup,
    SimulationScheduleEventGroup,
    ScheduleEventTypeGroup,
]);

export { CRMAPI };
