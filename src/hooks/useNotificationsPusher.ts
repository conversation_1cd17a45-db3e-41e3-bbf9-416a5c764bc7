import { usePusherContext } from '@common/contexts/Pusher';
import { rootStore } from '@store/instanse';
import { message } from 'antd';
import { useEffect } from 'react';
import { TNotification } from 'types/chats/notification';

interface UseNotificationPusherProps {
    userId?: string;
}

export const useNotificationsPusher = ({ userId }: UseNotificationPusherProps) => {
    const { subscribeToChannel } = usePusherContext();

    useEffect((): any => {
        if (!userId) return;
        const notificationChannel = subscribeToChannel(`private-notifications-${userId}`);

        if (!notificationChannel) {
            message.error('UseNotificationPusher: Не инициализирован канал');
            return;
        }

        const handleNotification = (data: TNotification) => {
            if (rootStore.socketStore.verbose) {
                message.info('UseNotificationPusher: Новое сообщение в консоли');
                console.log(data);
            }
            rootStore.socketStore.handleNewNotification(data);
        };

        notificationChannel.bind('pusher:subscription_succeeded', () => {
            if (rootStore.socketStore.verbose) {
                message.info('UseNotificationPusher: Уведомления подключены');
            }
        });
        notificationChannel.bind('received_message', handleNotification);
        notificationChannel.bind('pusher:subscription_error', (error) => {
            message.error('UseNotificationPusher: Ошибка при подключении уведомлений');
            console.log(error);
        });

        return () => {
            if (rootStore.socketStore.verbose) {
                message.info('UseNotificationPusher: Уведомления отключены');
            }
            notificationChannel.unbind_all();
            notificationChannel.unsubscribe();
        };
    }, [userId, subscribeToChannel]);
};
