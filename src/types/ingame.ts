import { TSessionTask } from './session/sessionTask';
import { TSessionWorker } from './session/sessionWorker';

/** Статус сетевых компонентов */
export type TransportStatus = {
    primaryTransport: 'WS' | 'HTTP';
};

/** Правила взаимодействия */
export type IngamePermissions = {
    /** Общий рубильник - для сохранения остального состояния */
    globalReadonly: boolean;

    /** Право старта ядра */
    allowStart: boolean;
    /** Право паузы ядра */
    allowPause: boolean;
    /** Право восстановления ядра */
    allowResume: boolean;
    /** Право остановки ядра */
    allowStop: boolean;

    /** Событие должно блокировать покидание окна */
    eventTopMost: boolean;

    /** Назначить задачу ПШЕ */
    workerAssignTask: boolean;
    /** Отменить назначение задачи ПШЕ */
    workerCancelTask: boolean;
    /** Повысить приоритет назначения */
    workerPrioritizeTask: boolean;
};

/** Настройки счёта времени */
export type IngameTimeSettings = {
    /** Дней в неделе */
    daysInAWeek: number;

    /** Часов в рабочем дне */
    workDayHours: number;
    /** Начало рабочего дня */
    workDayStart: number;
    /** Конец рабочего дня */
    workDayEnd: number;
    /** Пропускать ли час обеда */
    workDayLunchSkip: boolean;

    /** Тиков в часе */
    ticksInAnHour: number;

    /** Часов в дне календаря */
    calendarDayHours: number;
    /** Начало календарного дня */
    calendarDayStart: number;
    /** Конец календарного дня */
    calendarDayEnd: number;
};

export enum SWAlinkStatus {
    AwaitingAssign = 'awaiting-assign',
    AwaitingCancel = 'awaiting-cancel',
    Current = 'current',
    Previous = 'previous',
}

export type IngameSWAlinkRecord = {
    task_id: TSessionTask['order_id'];
    worker_uid: TSessionWorker['worker_uid'];
    status: SWAlinkStatus;
    week: number;
    day: number;
    tick: number;
    priority: number;
};

export type IngameWorkerSWAlinks = {
    [key: TSessionTask['order_id']]: IngameSWAlinkRecord | null;
};

export type IngameTaskSWAlinks = {
    [key: TSessionWorker['worker_uid']]: IngameSWAlinkRecord | null;
};

export type IngameTotalSWAlinks = {
    [key: TSessionTask['order_id']]: IngameTaskSWAlinks;
};
