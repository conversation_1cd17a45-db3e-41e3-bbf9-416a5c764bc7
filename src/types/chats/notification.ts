import { HardDeletable } from 'types/common';
import { TChat, TChatUser } from './chats';
import { TUser } from 'types/user/user';

/** Уведомление */
export type TNotification = HardDeletable & {
    /** Номерной ID уведомления */
    id: number;
    /** Связка с чатом, если относится к чатам/сообщениям */
    chat_id: TChat['id'] | null;
    /** Название чата */
    chat_name: TChat['chat_name'] | null;
    /** Неясный параметр */
    entity_id: any | null;
    /** Отношение к сущности-инициатору уведомления, при типе "chat" - сообщению */
    entity_name: string | null;
    /** Тип сущности-инициатора, "chat" подразумевает новое сообщение */
    entity_type: 'chat' | string;
    /** Текст уведомления (сообщения) */
    text: string;
    /** Криво замапленный тип чата для генерации читаемого отправителя */
    type: TChat['type'] | null;
    /** Пользователь, которому адресовано уведомление */
    user_id: TUser['id'];
    /** Сокращённый вид пользователей чата */
    users: TChatUser[];
};
