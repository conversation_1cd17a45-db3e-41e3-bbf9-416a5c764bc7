import { SoftDeletable } from 'types/common';
import { TUser } from 'types/user/user';

/** Пользователь-участник чата */
export type TChatUserRecord = SoftDeletable & {
    /** Номерной ID записи об участнике */
    id: string;
    /** Связка с чатом */
    chat_id: TChat['id'];
    /** Связка с объектом пользователя */
    user_id: TUser['id'];
    /** Забанен ли в чате */
    is_banned: boolean;
    /** Является ли админом чата */
    is_admin: boolean;
};

/** Сокращённая запись об участнике чата */
export type TChatUser = Pick<TUser, 'id' | 'name'>;

/** WIP вложение сообщения */
export type TChatMessageAttachment = {};

/** Статус сообщения */
export type TChatMessageStatus = {
    /** Связка с сообщением */
    message_id: TChatMessage['id'];
    /** Связка с получателем */
    recipient_id: TUser['id'];
    /** Статус - пока "получено", должен быть и "прочитано" */
    status: 'received' | string;
    /** Дата создания записи о статусе */
    created_at: string;
};

/** Сообщение чата */
export type TChatMessage = SoftDeletable & {
    /** UID чата */
    id: string;
    /** Номерной ID сообщения */
    order_id: number;
    /** Связка с чатом */
    chat_id: TChat['id'];
    /** Отправитель */
    sender_id: TUser['id'];
    /** Текст сообщения */
    text: string;
    /** Статусы */
    statuses: TChatMessageStatus[];
    /** Вложения сообщения */
    attachments: TChatMessageAttachment[];
};

/** Чат */
export type TChat = SoftDeletable & {
    /** UID чата */
    id: string;
    /** Название чата */
    chat_name: string;
    /** Создатель чата */
    creator_id: TUser['id'];
    /** Участники чата в сокращённом виде */
    users: TChatUser[];
    /** Тип чата - диалог или группа */
    type: 'private' | 'group';
    /** Сообщения чата, в списке - последнее */
    messages: TChatMessage[];
};
