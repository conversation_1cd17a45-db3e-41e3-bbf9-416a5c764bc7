import { TSimulation } from 'types/simulation/simulation';

/** Дженерик для объекта bulk-операций */
export type TBulkUpdate<T> = Array<{
    /** Действие с записью - создание, обновление, удаление, восстановление */
    action: 'add' | 'update' | 'remove' | 'restore';
    /** Индекс записи для логики после применения набора изменений или неудачи */
    index: number;
    /** Тело отдельной операции - полный объект для добавления
     *  и обновления, UID при удалении/восстановлении
     */
    value: T | { uid: string } | { simulation_id: TSimulation['id']; id: number };
}>;
