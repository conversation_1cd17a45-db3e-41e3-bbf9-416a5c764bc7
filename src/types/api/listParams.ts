import { TChatMessage } from 'types/chats/chats';
import { TSession } from 'types/session/session';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { TSessionWorkerAssignment } from 'types/session/sessionWorkerAssignment';
import { TSimulation } from 'types/simulation/simulation';
import { TUser } from 'types/user/user';

/** Основной тип параметров списочных запросов */
export type ListParams = {
    /** Значение для поиска в строковых полях */
    query: string | null;
    /** Страница */
    page: number | null;
    /** Записей на страницу */
    per_page: number | null;
    /** Параметр сортировки */
    sort_by: string | null;
    /** Направление сортировки */
    sort_direction: 'asc' | 'desc' | null;
    /** Фильтры */
    filters: {
        /**
         * Фильтр по deleted_at - "null" и null для не удалённых,
         * "all" для любых, "only" для только удалённых
         */
        deleted: 'null' | 'all' | 'only' | null;
    } | null;
};

export type UserListParams = Pick<ListParams, 'query' | 'page' | 'per_page'> & {
    role: TUser['role'] | string | null;
};

export type PermissionListParams = Omit<ListParams, 'query' | 'filters'> & {
    category_name: string | null;
};

export type NotificationListParams = Omit<ListParams, 'query' | 'filters'> & {
    user_id: string | null;
};

export type ChatMessageListParams = Omit<ListParams, 'filters'> & {
    /** Альтернативная пагинация - кол-во записей раньше указанной */
    count: number | null;
    chat_id: string;
    /** Запись, раньше которой стоит искать */
    from_id: TChatMessage['order_id'] | null;
};

export type ChatUserListParams = Pick<ListParams, 'page' | 'per_page'> & {
    user_id: string | null;
    chat_id: string | null;
};

export type ChatListParams = Omit<ListParams, 'query'> & { user_id: string | null };

export type SessionListParams = Omit<ListParams, 'query' | 'filters'> & {
    manager_id: TUser['id'] | null;
    simulation_id: TSimulation['id'] | null;
    filters: {
        created_at: Array<string | null> | null;
    } | null;
};

export type SessionAssignmentListParams = Omit<ListParams, 'query' | 'filters'> & {
    session_id: TSession['id'] | null;
    user_id: TUser['id'] | null;
    state: TSessionAssignment['state'] | null;
    filters: {
        created_at: Array<string | null> | null;
    } | null;
};

export type SessionWorkerAssignmentListParams = Omit<ListParams, 'query' | 'filters'> & {
    session_assignment_id: TSessionAssignment['id'];
    day: TSessionWorkerAssignment['day'] | null;
    filters: {
        created_at: Array<string | null> | null;
    } | null;
};

export type SimulationEventListParams = ListParams & { simulation_id: number };

export type SimulationScheduleEventListParams = Omit<
    ListParams,
    'query' | 'filters' | 'page' | 'per_page' | 'sort_by' | 'sort_direction'
> & {
    simulation_id: number;
    week: number | null;
};

export type SimulationWorkerListParams = Omit<ListParams, 'query'> & { simulation_id: number };

export type SimulationTaskListParams = Omit<ListParams, 'query'> & { simulation_id: number };

export type SimulationListParams = ListParams & {
    status: string | null;
    filters: {
        creator: string | null;
        created_at: Array<string | null> | null;
    };
};

export type InvitationListParams = Omit<ListParams, 'query'> & {
    status: string | null;
    filters: {
        sender: string | null;
        created_at: Array<string | null> | null;
    };
};

export type ScheduleEventTypeListParams = ListParams & {
    max_start: number | null;
    min_start: number | null;
};
