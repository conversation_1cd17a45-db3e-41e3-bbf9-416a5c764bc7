import { TSessionAssignment } from './session/sessionAssignment';
import { TSimulation } from './simulation/simulation';

/** Связка с симуляцией */
type SimLink = {
    simulation_id: TSimulation['id'];
};
/** Дочерние симуляции, номерной ID */
export type SimAndNumId = SimLink & {
    id: number;
};
/** Дочерние симуляции, UID */
export type SimAndUUID = SimLink & {
    uid: string;
};

/** Связка с назначением прохождения */
type SAlink = {
    session_assignment_id: TSessionAssignment['id'];
};
/** Дочерние прохождению, номерной ID */
export type SAandNumId = SAlink & {
    id: number;
};
/** Дочерние прохождению, UID */
export type SAandUUID = SAlink & {
    id: string;
};

/** Для сущностей с днём и тиком */
export type DayTick = {
    day: number;
    tick: number;
};

export type SoftDeletable = {
    created_at: string;
    updated_at: string | null;
    deleted_at: string | null;
};
export type HardDeletable = {
    created_at: string;
    updated_at: string | null;
};
