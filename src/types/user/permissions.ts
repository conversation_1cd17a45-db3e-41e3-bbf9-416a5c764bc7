/** Отдельное право */
export type TPermission = {
    /** Название категории (обычно по сущности) */
    category_name: string;
    /** Локализованное название категории */
    category_name_locale: string;
    /** Номерной ID права */
    id: number;
    /** Локализованное название права */
    name_locale: string;
    /** Техническое название права (обычно по операции) */
    name: string;
};

/** Категория прав */
export type TPermissionCategory = {
    /** Набор прав (обычно по операциям с сущностью в API)  */
    entity_permissions: Array<TPermission>;
    /** Локализованное название категории */
    name_locale: string;
    /** Название категории (обычно по сущности) */
    name: string;
};
