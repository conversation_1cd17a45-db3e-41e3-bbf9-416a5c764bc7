import { SimAndUUID, SoftDeletable } from 'types/common';

/** ПШЕ (полная штатная единица) симуляции, в конструкторе */
export type TSimWorker = SimAndUUID &
    SoftDeletable & {
        /** Номерной ID ПШЕ */
        id: number;
        /** Имя ПШЕ */
        name: string;
        /** Описание ПШЕ */
        description: string;
        /** Картинка, при наличии */
        picture: string | null;
        /** Почасовая ставка */
        hourly_rate: number;
        /** Процент вложения часов “На проекте” [0;100] */
        project_percentage: number;
        /** Характеристики (статы) на старте: ровно 5 цифр в дипазоне [0;6] */
        base_stats: Array<number>;
    };
