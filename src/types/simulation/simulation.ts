import { TUser } from 'types/user/user';
import { TSimTask } from './simulationTask';
import { SoftDeletable } from 'types/common';
import { TFilter } from 'types/filter';
import { TScheduleEventType } from './simulationScheduleEvent';

/** Головной объект симуляции */
export type TSimulation = SoftDeletable & {
    /** Номерной ID */
    id: number;
    /// Ещё не реализовано на бэке, сугубо фронтовый тип
    /** Разрешённые к использованию в прохождении типы календарных событий */
    allowed_schedule_event_types?: TScheduleEventType['id'][];
    /** Отправлена ли в архив, обратимо */
    archived: boolean;
    /** Категория */
    category: string;
    /** Создатель (архитектор/админ) */
    creator: TUser['id'];
    /** Описание */
    description: string;
    /** Фильтры симуляции */
    filters: Pick<TFilter, 'id' | 'is_protected' | 'color_hex' | 'name' | 'target'>[];
    /** Пометка готовности архитектором, обратимо */
    finished: boolean;
    /** ID первой задачи дерева */
    first_task: TSimTask['id'] | null;
    /** Бюджет на оборудование */
    hardware_budget: number;
    /** ID последней задачи дерева */
    last_task: TSimTask['id'] | null;
    /** Название */
    name: string;
    /** Бюджет на прочие расходы */
    other_budget: number;
    /** Опубликована ли для назначений прохождений, необратимо */
    published: boolean;
    /** Название характеристик (ПШЕ/задачи) */
    skills: string[];
    /** Можно ли использовать как шаблон */
    template: boolean;
    /** Прошла ли тест (ссылка на ID положительного теста?) */
    tested: boolean;
    /** Ограничение общего бюджета архитектором */
    total_budget: number;
    /** Количество недель в симуляции */
    weeks: number;
};
