import { SimAndUUID, SoftDeletable } from 'types/common';

/** Вариант ответа события симуляции */
export type TSimEventOption = SimAndUUID & {
    /** Связь с событием симуляции */
    simulation_event_uid: string;
    /** Номерной ID события симуляции */
    id: number;
    /** Реплика варианта ответа - говорит проходящий */
    text: string;
    /** Ответ "отправителя" события на реплику проходящего */
    reply: string;
    /** Реакции ПШЕ и/или "людей" в сценарии на принятое решение */
    reactions: {
        /**
         * Отправитель реакции - ПШЕ (по ID), либо "Система", "Клиент", "Команда"
         */
        reaction_sender_id: string | 'sys' | 'client' | 'team';
        /** Текст реакции */
        reaction_text: string;
    }[];
    /** Эффекты в случае выбора варианта ответа */
    effects: {
        /** Меняемый эффектом параметр */
        param:
            | 'motivation'
            | 'team-communication'
            | 'relation-leader'
            | 'relation-organization'
            | 'client-communication'
            | 'team-spirit'
            | 'cost'
            | 'work-hour';
        /**
         * Модификатор изменения - пока подразумевается просто сумма,
         * потенциально может быть добавлено поле типа операции
         */
        modifier: number;
    }[];
};

/** Событие симуляции */
export type TSimEvent = SimAndUUID &
    SoftDeletable & {
        /** Номерной ID события */
        id: number;
        /**
         * Отправитель события - ПШЕ (по ID), либо "Система", "Клиент", "Команда"
         */
        sender_id: number | 'sys' | 'client' | 'team';
        /** Название события, также оглавление сообщения */
        title: string;
        /** Описание, также тело сообщения */
        description: string;
        /** Варианты ответа */
        options: TSimEventOption[];
        /** Формат триггера - начало задачи или дня */
        trigger_type: 'task' | 'day';
        /** ID задачи/номер дня */
        trigger_arg: number;
    };
