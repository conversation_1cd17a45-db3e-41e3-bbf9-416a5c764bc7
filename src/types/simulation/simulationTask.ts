import { SimAndUUID, SoftDeletable } from 'types/common';

/** Задача (узел) симуляции */
export type TSimTask = SimAndUUID &
    SoftDeletable & {
        /** Номерной ID задачи */
        id: number;
        /** Выводимое название */
        name: string;
        /** Описание (на данный момент только в конструкторе) */
        description: string;
        /**
         * Является ли критической. После добавления динамического
         * высчитывания критического пути перезаписывается.
         */
        milestone: boolean;
        /** Плановое кол-во ПШЕ */
        est_workers: number;
        /** Плановая продолжительность в днях */
        est_duration: number;
        /** Плановый бюджет */
        est_budget: number;
        /**
         * Требования по умениям для ПШЕ
         * Фиксированно 5 чисел, значение ∈ [0; 6]
         */
        stats_req: Array<number>;

        /** Предшествующие задачи по номерному ID */
        previous: Array<number>;
        /** Последующие задачи по номерному ID */
        following: Array<number>;
        /** X на графе */
        grid_x: number;
        /** Y на графе */
        grid_y: number;
    };
