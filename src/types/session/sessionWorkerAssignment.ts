import { TSim<PERSON>or<PERSON> } from 'types/simulation/simulationWorker';
import { TSimTask } from 'types/simulation/simulationTask';
import { DayTick, HardDeletable, SAandUUID } from 'types/common';

/** Запись о назначении ПШЕ на задачу */
export type TSessionWorkerAssignment = SAandUUID &
    HardDeletable &
    DayTick & {
        /** Связка с ПШЕ по UID оригинала */
        worker_uid: TSimWorker['uid'];
        /** Связка с задачей по ID оригинала */
        task_id: TSimTask['id'];
        /** Приоритет в рамках ПШЕ */
        priority: number;
        /**
         * Тип записи - добавление/снятие/приоретизация
         * назначения, снятие по окончании задачи
         */
        action: 'assign' | 'cancel' | 'prioritize' | 'task-end';
        /** Неделя */
        week: number;
    };
