import { DayTick, SAandNumId, SoftDeletable } from 'types/common';
import { TSimTask } from 'types/simulation/simulationTask';
import { TSimWorker } from 'types/simulation/simulationWorker';

/** ПШЕ сессии */
export type TSessionWorker = SAandNumId &
    DayTick &
    SoftDeletable & {
        /** Текущее владение навыками */
        current_stats: TSimWorker['base_stats'];
        /** ID оригинального ПШЕ из конструктора */
        order_id: TSimWorker['id'];
        /** Сквозная связка с оригинальным ПШЕ */
        worker_uid: TSimWorker['uid'];
    };

/** Расширение ПШЕ сессии форвардом полей оригинала */
export type TSessionWorkerExtended = TSessionWorker &
    Pick<TSimWorker, 'description' | 'hourly_rate' | 'name' | 'picture' | 'project_percentage'> & {
        /** Текущие задачи ПШЕ исходя из назначений */
        current_tasks: TSimTask['id'][];
    };
