import { TSimTask } from 'types/simulation/simulationTask';
import { DayTick, SAandNumId, SoftDeletable } from 'types/common';
import { TSimWorker } from 'types/simulation/simulationWorker';

/** Задача сессии */
export type TSessionTask = SAandNumId &
    DayTick &
    SoftDeletable & {
        /** Текущие затраты */
        budget_current: number;
        /** Текущий размер стакана трудочасов */
        current_weight: number;
        /** День выполнения задачи */
        end_day: number | null;
        /** Конкретный тик выполнения задачи */
        end_tick: number | null;
        /** ID оригинальной задачи из конструктора */
        order_id: TSimTask['id'];
        /*
        Прогнозы расчитываются для текущих задач,
        кроме состояний ядра prestarted и stopped
    */
        /** Прогноз бюджета */
        predicted_end_budget: number | null;
        /** Прогноз дня завершения задачи */
        predicted_end_day: number | null;
        /** Прогноз тика завершения задачи */
        predicted_end_tick: number | null;
        /** Прогноз недели завершения задачи */
        predicted_end_week: number | null;
        /** Текущий прогресс в трудочасах от ПШЕ */
        progress: number;
        /** День открытия задачи для прохождения */
        start_day: number | null;
        /** Конкретный тик дня открытия */
        start_tick: number | null;
        /** Сквозная связь по UID с задачей конструктора */
        task_uid: TSimTask['uid'];
    };

/** Расширение задачи сессии форвардом полей оригинала */
export type TSessionTaskExtended = TSessionTask &
    Pick<
        TSimTask,
        | 'description'
        | 'est_budget'
        | 'est_duration'
        | 'est_workers'
        | 'following'
        | 'grid_x'
        | 'grid_y'
        | 'milestone'
        | 'name'
        | 'previous'
        | 'stats_req'
    > & {
        /** Текущие ПШЕ задачи, исходя из назначений */
        current_workers: TSimWorker['uid'][];
    };
