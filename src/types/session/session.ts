import { HardDeletable } from 'types/common';
import { TSimulation } from 'types/simulation/simulation';
import { TUser } from 'types/user/user';

/** Сессия - контейнер для назначений прохождения под контролем менеджера */
export type TSession = HardDeletable & {
    /** UID сессии */
    id: string;
    /** Ответственный менеджер, UID */
    manager_id: TUser['id'];
    /** Ответственный менеджер, читаемое имя */
    manager_name: TUser['name'];
    /** Ответственный менеджер, аватар при наличии */
    manager_picture: TUser['picture'];
    /** Симуляция, для которой будут назначения прохождений */
    simulation_id: TSimulation['id'];
    /** Название симуляции */
    simulation_name: TSimulation['name'];
};
