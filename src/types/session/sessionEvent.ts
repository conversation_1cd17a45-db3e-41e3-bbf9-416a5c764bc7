import { SAandUUID, DayTick, SoftDeletable } from 'types/common';
import { TSimEvent, TSimEventOption } from 'types/simulation/simulationEvent';

/** Сессионная опция решения сценарного события */
export type TSessionEventOption = SAandUUID &
    SoftDeletable & {
        /** Связка с сессионным событием */
        session_event_uid: TSessionEvent['id'];
        /** Связка с оригинальным событием */
        simulation_event_uid: TSimEvent['uid'];
        /** Связка с ответом в оригинальном событии */
        simulation_event_option_uid: TSimEventOption['uid'];
        /** Текст ответа */
        session_event_text: TSimEventOption['text'];
        /** Ответ отправителя события - если это выбранная опция, либо null */
        session_event_reply: TSimEventOption['reply'] | null;
        /**
         * Реакции - только для выбранного ответа действительный набор, для остальных []
         */
        session_event_reactions: TSimEventOption['reactions'];
    };

/** Сессионная версия сценарных событий */
export type TSessionEvent = SAandUUID &
    DayTick &
    SoftDeletable & {
        /** Связка с оригинальным событием */
        simulation_event_uid: TSimEvent['uid'];
        /** Событие показано, либо выбор принят */
        status: 'activated' | 'ended';
        /** Отправитель события */
        simulation_event_sender_id: TSimEvent['sender_id'];
        /** Заголовок сообщения */
        simulation_event_title: TSimEvent['title'];
        /** Тело сообщения */
        simulation_event_description: TSimEvent['description'];
        /** Урезанный набор опций, исключены данные эффектов и недоступные реакции */
        options: TSessionEventOption[];
        /** Связка с выбранной опцией (при наличии) */
        option_picked_uid: TSessionEventOption['id'] | null;
        /** Игровой день принятия выбора */
        option_picked_day: DayTick['day'] | null;
        /** Тик в игровом дне принятия выбора */
        option_picked_tick: DayTick['tick'] | null;
    };
