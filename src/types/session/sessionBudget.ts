import { TSimTask } from 'types/simulation/simulationTask';
import { TSessionTaskExtended } from './sessionTask';
import { TSimWorker } from 'types/simulation/simulationWorker';

/** Запись объекта бюджета */
export type TSessionBudgetRecord = {
    /** Текущие затраты */
    budget_current: number;
    /** Прогноз затрат - для задач */
    budget_prediction: number;
    /** План бюджета - по задаче или отдельным статьям */
    budget_plan: number;
    /** Текущий размер стакана в трудочасах - для задач */
    current_weight: TSessionTaskExtended['current_weight'];
    /** Прогресс в виде выполненных трудочасов - для задач */
    progress: TSessionTaskExtended['progress'];
    /** Связка с задачей, если не отдельная статья */
    task_uid: TSimTask['uid'] | null;
    /** Название статьи бюджета */
    title: string;
    /** Тип записи - задача, оборудование, прочее или итого */
    type: 'task' | 'hardware' | 'other' | 'total';
    /** ПШЕ, числящиеся на задаче */
    workers: {
        name: TSimWorker['name'];
        worker_uid: TSimWorker['uid'];
    }[];
};

/** Общий объект бюджета */
export type TSessionBudget = TSessionBudgetRecord[];
