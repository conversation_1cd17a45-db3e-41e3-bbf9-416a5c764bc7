import { DayTick, SAandNumId, SoftDeletable } from 'types/common';
import { TSessionTaskExtended } from './sessionTask';
import { TSessionWorkerExtended } from './sessionWorker';
import { TSimEvent } from 'types/simulation/simulationEvent';

/** Запись хронологии изменений задач в прохождении */
export type TSessionTaskProgress = SAandNumId &
    DayTick &
    SoftDeletable & {
        /** Связь с задачей */
        task_id: TSessionTaskExtended['order_id'];
        /** Связь с ПШЕ (если он - инициатор изменений) */
        worker_uid: TSessionWorkerExtended['worker_uid'] | null;
        /** Связь со сценарным событием (если оно - инициатор изменений) */
        event_id: TSimEvent['id'] | null;
        /** Изменение бюджета (расширение или затраты) событием */
        event_budget_change: number;
        /** Текущий бюджет задачи */
        task_budget_current: TSessionTaskExtended['budget_current'];
        /** Текущий прогресс задачи */
        task_progress: TSessionTaskExtended['progress'];
        /** Неделя на момент записи */
        week: number;
        /** Списание бюджета за работу ПШЕ */
        worker_cost: TSessionWorkerExtended['hourly_rate'];
        /** Добавленные работой ПШЕ трудочасы прогресса */
        worker_hour: TSessionTaskExtended['progress'];
    };
