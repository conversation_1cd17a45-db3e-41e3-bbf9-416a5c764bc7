import { TUser } from 'types/user/user';
import { TSession } from './session';
import { TSimulation } from 'types/simulation/simulation';
import { DayTick, HardDeletable } from 'types/common';

/** Назначение прохождения симуляции */
export type TSessionAssignment = DayTick &
    HardDeletable & {
        /** UID прохождения */
        id: string;
        /** Ответственный менеджер, UID */
        manager_id: TUser['id'];
        /** Ответственный менеджер, читаемое имя */
        manager_name: TUser['name'];
        /** Ответственный менеджер, аватарка при наличии */
        manager_picture: TUser['picture'];
        /** Связка с сессией */
        session_id: TSession['id'];
        /** Описание симуляции */
        simulation_description: TSimulation['description'];
        /** Название симуляции */
        simulation_name: TSimulation['name'];
        /** Состояние прохождения */
        state: 'prestarted' | 'started' | 'paused' | 'resumed' | 'stopped' | 'finished' | string;
        /** Проходящий пользователь, UID */
        user_id: TUser['id'];
        /** Проходящий пользователь, читаемое имя */
        user_name: TUser['name'];
        /** Проходящий пользователь, аватарка при наличии */
        user_picture: TUser['picture'];
    };
