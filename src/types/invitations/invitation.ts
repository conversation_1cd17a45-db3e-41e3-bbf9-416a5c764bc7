import { SoftDeletable } from 'types/common';
import { TUser } from 'types/user/user';

/** Приглашение в систему */
export type TInvitation = SoftDeletable & {
    /** UID приглашения */
    id: string;
    /** Email отправки письма, он же email создаваемого пользователя */
    email: TUser['email'];
    /** Фильтры, которые унаследует объект пользователя */
    filters: TUser['filters'];
    /** Роль, которую получит пользователь */
    role: TUser['role'];
    /** Логин при регистрации */
    login: TUser['login'];
    /** Отправитель, UID */
    sender_id: TUser['id'];
    /** Читаемое имя отправителя */
    sender_name: TUser['name'];
    /** Отправитель, аватар при наличии */
    sender_picture: TUser['picture'] | null;
    /** Статус приглашения */
    status: 'Отправлено' | 'Принято' | string;
    /** Зарегистрировавшийся пользователь */
    user_id: TUser['id'] | null;
    /** Имя результирующего пользователя */
    user_name: TUser['name'] | null;
    /** Аватарка результирующего пользователя, при наличии */
    user_picture: TUser['picture'] | null;
};
