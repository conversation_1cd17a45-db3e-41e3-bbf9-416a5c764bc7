25/01/25:
- Инициализирован проект: структура, пакеты, конфиги для билда
- Частичное описание типов в файле
6ч

27/01/25:
Добавлены:
- классы: GlobalConstants, CookieManager, SettingsManager
- шрифты: Inter-Regular, Inter-Italic, Inter-Bold, Inter-BoldItalic
- стили: подключение шрифтов, базовые стили шрифтов из фигмы
- страницы: FontsTest, <PERSON>gin (переделать)
6ч

28/01/25:
Исправлены:
- шрифты: переход на Display-версии, добавлен SemiBold и стили под него
3ч

29/01/25:
- шрифты: InterVariable
- компоненты: LeftBar
4ч

30/01/25:
- классы для API
- доработки Logo и LeftBar
4ч + 3ч созвон

31/01/25:
- наброски по сущностям (User, Simulation*)
- разбор конференции, угон файлов
8ч + 2ч

02/02/25:
- доска в Miro (сущности, связи, навигация)
3ч

03/02/25:
>> Погуглить библиотеки для ганта, календаря, построения диаграмм.
>> набить ёжку задачами

08/02/25:
- довёрстан LeftSideBar 
>> (не хватает логики "я тут" и пимпочек сбоку)
- заведена документация API в Postman и проектирование API (security/user)
- SCSS-файл с палитрой
> пимпочки menu-item`ам
> скрафтить бровь
> ЛК
> нарезать тасок в ёжку
> глянуть сервак
> пока не делаем события
> пока не делаем график общий/график ПШЕ
> продумать API: Simulation, Simulation Tasks, Simulation Key Stats, Simulation Workers
12ч

17/02/25:
+ "я тут" в левом сайдбаре
+ хранилище для симуляций/операции: sim, simTask, simKeyStat, simWorker
+ основные роуты
> Симуляция
    + список симуляций
        > фильтры, поиск, функционал
    + профиль симуляции
        > функционал
> API
    > simulationGroup
        + list, by_id, create, update, remove, restore
> ЛК
> переделать функционал поиска и фильтров
6ч

18/02/25:
+ API
    + simulationGroup
        + list, by_id, create, update, remove, restore, bulk
    + simulationTaskGroup
        + list, by_id, create, update, remove, restore, bulk
    + simulationWorkerGroup
        + list, by_id, create, update, remove, restore, bulk
    + simulationKeyStatGroup
        + list, by_id, create, update, remove, restore, bulk
    + Postman synch
+ SimulationStore
    + simulation, simulationTasks, simulationKeyStats, simulationWorkers

3ч

20/02/25
+ API занесено в Postman

23-24/02/25
+ генерация ПШЕ, статов, задач
+ кнопка добавления в списке симуляций
+ страницы: создание симуляции, узлы (список, профиль), ПШЕ (список, профиль)
> Посмотреть какие компоненты можно заменить AntD

06/03/25
> Добавлен компонент инпутов с подписью снизу "Максимум символов"/"своё"
> Исправлено масштабирование по ширине карты в SimList
> Исправлена вёрстка по макету: 
+ ConstructorWorkerProfile, 
+ ConstructorSimulationProfile, 
+ SimulationProfile
> Исправлены листы
+ ConstructorWorkerList
+ ConstructorNodeList
> Граф PRIORITY:
<ОТЛОЖЕНО
- слоты на сетке
- snap to grid
// Нужно настраивать zustand-провайдер
- выравнивание горизонтально/вертикально из нижней панели
>ОТЛОЖЕНО
Визуал:
    + движение узлов на графе
    + скруглённые коннекторы
    + входящие/исходящие
    + подсветка входящих-исходящих
    - решить проблему наложения линий
    + стилизация протягиваемого коннектора
    + тулбар edit/delete
    + выдвигаемый справа сайдбар узла
    + название узла подрезается, если слишком длинное
    + выдвигаемая слева табличка
    + для численных инпутов таблицы убраны стрелки
    + фиксированная шапка таблицы
Логика:
    + регистрация добавления/удаления/передвижения узлов
    + регистрация удаления/добавления связей
Операции:
    + удаление узлов
    + налаживание связей
    - добавление узлов вытягиванием
    + добавление узлов таблицей
    + добавление узлов нижним меню
    + открытие в сайдбаре из тулбара
    + сохранение изменений
> Диаграммы сессии PRIORITY:
    - Таблица + Гант
    - график справа
    + чаты
    - крутилка слева

10/03/25
Спроектирована система прав и категорий прав
Систематизирован список исправлений и вопросов к дейли

11-12/03/25 
https://www.youtube.com/playlist?list=OLAK5uy_nLbsse0a04Aw9RUes8pZnfBjnQ4-DbuMk
1. Правки Игоря (--ФронтИгорь):
    +1. Сеть: тултипы нижнее меню
    +6. Сеть: rightBottom "ReactFlow"
    +8. ConstructorSim: fix button group wrap
    +9. ConstructorSim: убрать "thingies" сверху ID
    +10. MenuSideBar: -onHover type=="blank"
    +11. ConstructorSim: previous/following node onClick
    +12. ConstructorNodeProfile: fix edit
    +13. ConstructorNodeList: "save created" routing fail
    +14. ConstructorWorkerProfile: fix edit
    +15. ConstructorWorkerList: "save created" routing fail
    +16. ConstructorSim: fix edit
    +17. ConstructorSim: navBar "Описание симуляции" wrong route -> ConstructorGraph
2. Page<Login> rework (--ФронтДима:[0.1a, 0.2a]):
    a) MUI to AntD
    b) API actions, validation
    c) onEnter
    d) стили + портретный вид
3. Page<ConstructorSim> rework (--ФронтДима:0.2d)
    a) стили + портретный вид
4. DevMenu вызывается нажатием на лого:
    - смена текущей роли
    - переключение API/заглушки (все группы запросов покрыты заглушками - до сдачи проекта схема сохраняется)
5. Включено API для групп Security/Users/<USER>
6. Локальное хранилище для пользователей
7. Тип, группа запросов, хранилище для приглашений, страница завершения регистрации (пока ручками по URL)
8. Локальная реализация ролей и прав:
    + хранение списка категорий и отдельный действий
    + локализация ru-RU
    + структурированный список прав по ролям
    + механизм проверки
    - пока без интеграции в страницах
9. Изменения сущностей, будут добавлены в Postman:
9.1 Новая группа: Invitations
9.1.a GET /invitations (параметры пока не придумал)
9.1.b GET /invitations/{invitation_uid}
9.1.c POST /invitations { invitation: {
    uid: string;
    // "Отозвать" инвайт можно удалением
    status: "Отправлено" | "Принято";
    // UID отправителя
    sender_uid: string;
    // Читаемое имя отправителя, пока просто логин
    sender_name: string;
    email: string;
    login: string;
}}
9.1.d DELETE /invitations/{invitation_uid}
9.1.e POST /invitations/{invitation_uid}/restore
9.1.f PUT /invitations/{invitation_uid} { invitations: {...} }
// Регистрация по инвайту - email и login фиксируются создателем инвайта, пусть меняют после реги;
// email/login из invitation, password из тела - создать пользователя, открыть сессию; 
// статус инвайта на "принято", ответ как при security/login;
9.1.g POST /invitations/{invitation_uid}/register { password: string }

9.2 Сущность User:
    +id: number превращается в uid: string (поправить ручки)
9.3 Сущность Simulation:
    +creator: number превращается в creator_uid: string (поправить ручки)

21/03/25
Postman: 
1. Users UPD (new params): list, delete, get, current, invite, restore, update
2. Invitations NEW (new group): list, get, create, delete, restore, update, register
3. Simulations UPD (creator number>string): list, get, create, update, delete, restore, bulk
Фронт-репка:
1. Поправил devMenu.
2. sim_id => simulation_id API GROUPS.
3. UserList/UserProfile

22-23/03/25
1. Правка типов Simulation, SimulationWorker, SimulationTask под ТЗ/пример. Упразднение SimulationKeyStat (сложен в Simulation).
1.a Также исправлены логика и страницы под эти изменения.
2. Добавление SimulationEvent, SimulationEventOption - тип, частичный набор стартовых данных.
3. Пример Ростелекома частично внесён в заглушки:
- Simulation, SimulationWorker, SimulationTask перенесены полностью;
- SimulationEvent перенесены частично (приостановка по смене приоритетов);
4. Кнопка "Выход", закрывающая сессию, в ЛК (личный профиль, нижняя кнопка левого меню).
5. Список приглашений (Менеджмент/Приглашения), профиль.
6. Список назначений сессии, механизм назначения с выбором клиента в боковом меню.
7. Исправления прошлого набора:
- ФИгорь.7 Сеть: создание узла: newId (local) -> newId (global) для предотвращения возврата связей удалённой ноды новой при same ID
8. События: хранилище, операции, список, профиль.

28/03/25
1. API front <> back coupling (ФД.17):
- Invitations
- Simulation Tasks
- Simulation Workers
- Users
2. Добавление/удаление запчастей ConstructorEventProfile (ФД.13)
3. Численные поля, где положено (ФИ.32):
- ConstructorEventProfile
- ConstructorWorkerProfile
- ConstructorProfile
- ConstructorTaskProfile
- ConstructorGraph (кроме таблицы)
4. Рефакторинг графа:
- вынесены NodeTableDrawer, NodeInfoDrawer, graphLowerMenu (1к -> 550 строк)

05-07/04/25
1. Переработка "Конструктор/Сеть":
- хранение в Zustand и распутывание Dagre (ФД.2)
- исправление стилей связей: выделение связи, выделение ноды (и идущих к ней связей), при добавлении новой связи (ФД.[1, 10], ФИ.2, ФС.7)
- настройка первого/последнего узла, удаление и блокировка некорректных связей (ФД.11, ФИ.[3, 4])
- перенесены кнопки увеличения/уменьшения масштаба и центрирования из примера
- связи можно редактировать, перетягивая начало или конец к другому узлу
- новые элементы добавляются в центр рабочей области
- предотвращение создания связей-циклов - сам к себе, либо через несколько узлов (ФД.5)
- по клику на узел выделение, по двойному клику открытие сайдбара (ФС.2)
- удаление связей и узлов по Backspace/Delete, в сайдбаре кнопка удаления узла для тач-девайсов (ФС.[1, 4])
- закрытие сайдбара по снятию выделения с узла (ФС.3)
- отключён мультивыбор (неясна логика взаимодействия)
- миникарта в сайдбаре
- сбор внесённых изменений производится в момент нажатия "Сохранить" (снижение нагрузки на обработчики узлов/связей)
2. Дополнение API со стороны фронта:
- Security: confirm-registration
- Users: ban, unban, change-role
- Invitations: bulk, bulk-result
- Simulations: bulk, bulk-result
- Simulation Tasks: bulk, bulk-result
- Simulation Workers: bulk, bulk-result
- Simulation Events: list, get, create, update, delete, restore
- Roles: list, get, create, update

Оставшиеся вопросы:
- валидация полей таблицы на графе (из-за обёртки редактируемых полей есть сложности)
- требование при сохранении указать первый/последний узел (их пометка на таблице?)
- предотвращение покидания страницы графа при наличии изменений (ФС.8) - осложнено особенностями React Router
- переключение логики Simulation Event (список/профиль) на API (группа описана, не имплементирована)

12-15/04/25
1. Новые компоненты:
- сайд-меню и шапка "в симуляции"
- страница Симуляция/Бюджет (переход через "Управление" и клик по карточке)
- страница Симуляция/Задача
- страницы Симуляция/Список-ПШЕ и Симуляция/Профиль-ПШЕ
2. Обратно внесён пример по РТК (симуляция, узлы, ПШЕ - события пока работают не совсем корректно)
3. Переключение событий из конструктора на API: список, профиль, создание, удаление, обновление
4. Исправлены баги: 
- некорректно обрабатывалось удаление связи на графе
- создание/редактирование связи позволяло создать дубликат
- автовыравнивание в отдельности не регистрировалось как изменение датасета
- выпадающий список параметра последствия выбора события не скачет по длине (ФД.29)
- редактирование узлов в таблице вызывало белый экран (ФД.25, ФИ.31)
- валидация полей таблицы на графе (ФД.23)
5. Добавлены улучшения:
- некорректно показывался баланс входящих/исходящих
- отрезан избыток текста в нодах в превью
- переход между узлами по клику на них в превью

Идеи/исправления на потом:
- Профиль Узла, "Предыдущие узлы"/"Следующие узлы" - конфликт ID-UID
- раскрывающиеся блоки в просмотре симуляции наполнить мини-карточками сущностей с навигацией

20-28/04/25
1. Сетевые группы:
- Filters
- Permissions, Permission Categories
- Chat Messages, Chat Users, Chats, Notifications
2. Вкладка "Гант" в конструкторе:
- постройка Ганта (не хватает ограничителей ширина-высота и фона с подписью недель)
- пересчёт длины задач при изменении "Дней" в таблице или набора ПШЕ
3. Исправления:
- вне зоны "прохождение" сайдбар остаётся свёрнутым при переходах, по умолчанию свёрнут (ФМ.2, ФС.6a)
- наложение счётчика TextArea в "Реакции" на "Ответ" в профиле события (ФМ.7a)

03-06/05/25
1. Сетевые группы: Sessions, Session Assignments, Session Worker Assignments
2. Исправления:
- централизованная обработка ошибок: редирект на логин при 401, вывод в читаемом виде
- был некорректный учёт крайнего ID на графе - влиял на создание/сохранение (ФМ.9)
- ingameBudget: угол закрашен (ФМ.6)
- переработаны вкладки в верхнем меню (централизация, MUI -> AntD, ФД.0.2)
- ingame левый сайдбар разворачивается, состояние хранится между страницами

16/05/25
1. Хранилище для данных прохождения, в стадии наброска.
2. Переделан TopBar: замена оставшихся MUI-компонентов на AntD (ФД.0.2).
3. Страница уведомлений.
4. Список симуляций: 
- распределение по вкладкам из свойств (ФИ.18), 
- восстановление для удалённых, 
- генерация тегов,
- корректное сообщение, если список пуст
5. Вырезаны заглушки для: симуляций (и запчастей), пользователей, приглашений, назначений.
6. Добавлена корзина, восстановление, быстрое копирование ID и названий (и обрезка текста где необходимо):
- список симуляций
- список событий
- список узлов (задач)
- список ПШЕ
- список приглашений
- список сотрудников/клиентов
7. Боковое меню сворачивается по клику в свободную область (ФС.6).
8. Переход по узлам в профиле задачи починен

18/05/25
1. Обновлён набор категорий прав и самих прав со стороны фронта.
2. По клику на логотип теперь открывается группа инженерных страниц:
- тест шрифтов
- тест палитры цветов (новое) - по клику копируется HEX
- страница для проверки/модификации хранилищ (новое) - дампы, сброс, копирование
- страница для проверки списочных запросов групп API - автоматический сбор ответов, учёт отсутствия родителя
3. Добавлен класс с палитрой цветов (для случаев, когда их надо задать оттуда не через стили).
4. В списке ПШЕ добавлен вывод их скиллов.
5. Добавлен список для сессий (Session).

22/05/25 (3ч от 19.05)
1. Провайдер и хук для Pusher.
2. Хранилище для socket-сущностей (уведомления, чаты, сообщения чатов).
3. Вкладка в тест-странице "Хранилище" под SocketStore.
4. Хуки на уведомления и сообщения чата, компоненты-холдеры.
5. Страница чатов:
- список

Todo: 
- режим свёрнутого списка (наезжающая слева область для вертикальных экранов)
- шапка чата в режиме поиска и вывод найденных заместо списка чатов (портретный вид?)
- экран редактирования чата

27/05/25
1. Функционал страницы чатов:
- подгрузка списка чатов
- создание новых чатов (диалоги/группы)
- отправка сообщений (не все эмодзи поддерживаются)
- подписка на канал сообщений (обработка отправляемых и получаемых сообщений)
- частично работает подгрузка скроллом (почему-то не больше 10 за раз)
2. Благодаря Толе заработал централизованный вывод ошибок API в уведомлениях.
3. Глобальная валидация токена при новом открытии.
4. Исправлен баг с зацикленным редиректом на логине при битом токене.
5. Составлены доработки API прохождения под нужды страниц клиента.

29/05/25
1. Обновлён профиль симуляции в конструкторе.
2. Обновлены карточки симуляции в списке: 
- соотношение столбцов изменено с 50-50 на 70-30
- добавлена кнопка "Сессии" - переход в список сессий с фильтром
- набор кнопок соответствует правам (просмотр, открытие конструктора, найти сессии)
3. Обновлён readonly профиль симуляции:
- переход в список сессий
- создание сессии
4. Добавлен профиль сессии:
- покрыто правами
- вывод данных о сессии, создателе, симуляции и назначениях
- readonly режим если создатель был удалён/забанен, либо симуляция удалена
- опции перехода в профиль создателя/симуляцию (с предупреждением о потере несохранённых данных) и удаления
- табличный CRUD для работы с назначениями:
-- фильтрация/сортировка
-- выбор доступен для новых, не начатых и удалённых назначений
-- назначение сессии списку выбранных пользователей
-- привязка списка выбранных назначений к выбранному пользователю
-- удаление
-- создание пачками (сейчас 1-5-10, диапазон [1, 20])
5. Список сессий: фильтр при переходе из списка/readonly профиля симуляции.
6. Список назначений: актуальные назначения.
7. Класс-менеджер API дополнен методом для поллинга и обработки исходов bulk-запросов.
8. Интеграция прав:
- список симуляций: просмотр списка, просмотр отдельной симуляции, переход в конструктор, восстановление, создание
- readonly профиль симуляции: просмотр, создание сессии, редактирование
- профиль симуляции в конструкторе: просмотр, создание, изменение, удаление, восстановление, тестирование
- вкладки верхнего меню
- вкладки сайдбара
- холдеры подключения к каналам чата/уведомлений
- профиль сессии: просмотр, получение симуляции, получение создателя, получение списка пользователей, CRUD назначений

01-02/06/25
1. Пофикшены ошибочные запросы в профилях события и ПШЕ.
2. Переработаны типы:
- унифицированы SoftDelete/HardDelete параметры и комбинации "родитель"+id/uid
- добавлены параметры в Session, SessionAssignment
- добавлены TSessionTask, TSessionTaskExtended, TSessionWorker, TSessionWorkerExtended
3. Дополнена SessionWorkerAssignmentGroup (SWAprioritize), добавлена SessionAssignmentInfoGroup.
4. Игровое хранилище:
- инициализация (переход в назначение с подгрузкой данных)
- менеджмент прав на использование UI по отношению к прохождению (пользователь/менеджер) и состоянию
- управление состоянием назначения
- подгрузка списков/сущностей (ПШЕ, задачи, назначения, информация ядра)
- методы работы с назначениями (добавление, снятие, приоритизация)

03/06/25
1. Список сессий: добавлен переход к списку назначений с фильтром
2. Профиль сессии:
- добавлена аналогичная кнопка
- строка с кнопками разделена
3. Список назначений:
- карточке для читаемости добавлены/переработаны поля о симуляции, менеджере, пользователе, состоянии и времени назначения;
- добавлен фильтр по сессии-родителю;
- у клиента скрывается информация о сессии, высота карточки уменьшена;
4. Добавлен профиль назначения:
- при наличии прав выводится информация о сессии (с возможностью перехода), а для симуляции ID и возможность перехода;
- выводится краткая информация о симуляции;
- выводится информация о назначении
- в разделённом виде, соответственно правам и состоянию назначения выводятся:
— переход в прохождение (хранилище прохождения инициализируется, закрепляя это назначение, затем переход в дашборд, информирование в случае ошибок);
— управление состоянием назначения: Старт, Пауза, Продолжить, Завершить, Остановить;
— переходы к связанным объектам: Симуляции, Сессии;
— удаление назначения (при наличии прав, если назначение не было начато, с предупреждением что восстановлению не подлежит);
4.1 вид у пользователя (клиента):
- отсутствует информация о сессии;
- скрыт ID симуляции;
- отключены переходы к связанным объектам;
- запрещено удаление и прямое воздействие на состояние назначения (прохождения);
--- Отправлено: http://78.155.202.182/simbatech/fe_simba/-/merge_requests/20 ---

03-04/06/25
1. Гант:
- сделан отдельным встраиваемым компонентом
- три юзкейса: 
-- конструктор (таблица + назначения), 
-- прохождение (без таблицы, с назначениями),
-- миниатюра (readonly, для дашборда)
- заблокирован зум, динамически по набору задач вычисляются высота и ширина контейнера, перемещение перетягиванием или скроллом
- выводятся соотносящиеся с позицией на канвасе обозначения задач и недель
- выводится "сегодня" для прохождения
- пофикшены цвета и вывод миниатюр ПШЕ, вывод данных в таблице
- Гант подключен в конструкторе
- Гант в ограниченном режиме подключён в прохождении
2. Пикер ПШЕ исправлен: шапка с информацией о задаче (люди, продолжительность, требования), переход к задаче.

05/06/25
1. К хранилищу подключены страницы ПШЕ, задачи и бюджета.
2. Добавлены таймлайны ПШЕ (стиль и функционал схожий с Гантом):
- выводятся таймлайны задач по ПШЕ
- вне тестового режима порядок следует последовательности задач на графе
- цветовая схема аналогична Ганту
- есть переходы в ПШЕ и задачи соответственно
- свободное место заполняется неактивным узлом
3. Убрана вкладка "Прохождения" из группы "Управление". Отныне переходы только в настоящие прохождения.
--- Отправлено: http://78.155.202.182/simbatech/fe_simba/-/merge_requests/21 ---
4. Добавлен сетевой график (граф конструктора, но грустный).
5. Добавлена заглушка для неготовых страниц (дашборд, календарь).
6. Адаптирование под патч бэкенда - дополнение типов, удаление костылей для расширения объектов задач, получение симуляции напрямую.
--- Отправлено: http://78.155.202.182/simbatech/fe_simba/-/merge_requests/22 ---

09-11/06/25
1. Конструктор.Задачи.Список - weight вычислимый.
2. Конструктор.Задачи.Профиль - исправлена вёрстка (поля, кнопки), убраны ненужные параметры.
3. Конструктор.ПШЕ.Список - батарейки скиллов на колбаски.
4. Конструктор.ПШЕ.Профиль - исправлена вёрстка (поля, кнопки), убраны ненужные параметры.
5. Конструктор.Событие.Профиль - исправления:
- ограничителем триггера типа "день" является конец последней задачи
- у одного варианта ответа не может быть несколько последствий с одним параметром
- аналогично не может быть реакций с одинаковым отправителем
- валидация сохранения
- скорректирован набор кнопок.
6. Операции преобразования списка задач вынесены в класс TaskUtils:
- формирование данных для ганта (Гант в конструкторе и прохождении)
- расчёт критического пути и резервного времени задач (Гант, список задач)
- формирование данных для таймлайна (страница-список ПШЕ в прохождении)
- вычисление общей длительности дерева задач (профиль симуляции)
7. На графе конструктора исправлена операция сохранения и дополнена валидация:
- обязательно указание первой/последней задачи
- недопустимы задачи без связей, либо не сходящиеся между первой и последней
8. Покрытие проверками на права:
- Конструктор.Гант
- Конструктор.Граф
- Конструктор.ПШЕ - список и профиль
- Конструктор.Симуляция
- Конструктор.События - список и профиль
- Конструктор.Узлы - список и профиль
--- Отправлено: http://78.155.202.182/simbatech/fe_simba/-/merge_requests/23 ---
9. Исправление списков и профилей (минимальная ширина, починка responsive grid):
- Конструктор.Гант/Прохождение.Гант (945px рабочая область)
- Конструктор.Граф (945px рабочая область)
- Конструктор.ПШЕ - список, профиль (652px)
- Конструктор.Симуляция (652px)
- Конструктор.События - список, профиль (652px)
- Конструктор.Узлы - список, профиль (652px)
- Назначения - список, профиль  (652px)
- Пользователи - список (652px)
- Приглашения - список и профиль (652px)
- Симуляции - список, профиль (652px)
- Сессии - список (652px), профиль (945px)
10. Ввод характеристик в профиле ПШЕ/задачи (и drawer на Графе) на Slider.
11. Критический путь на сетевой карте. В сетевой карте и на Ганте линии между критическими своего цвета.
12. Для набора вкладок сверху добавлен draggable-scroll (для портретного вида и если не вписывается по ширине).
13. Конструктор.Узлы.Профиль и Симуляция.Профиль - Accordeon из MUI заменён на Collapse из AntD.
14. Симуляция.Профиль - Collapse наполнены данными.
15. Для экранов в портретном виде скорректирован шаблон (кроме прохождения) - уже левое меню (при раскрытии фуллскрин), меньше верхнее, меньше отступы.

11/06/25
1. Портретная адаптация (эксперимент, 350px, отступы 16px):
- Логин и регистрация инвайта
- Симуляции - профиль и список
- Конструктор.[Гант, Граф, Симуляция]
- Конструктор.[ПШЕ, событие, узел].профиль
- Назначения - список, профиль
- Пользователи - список
- Приглашения - список, профиль
- Сессия - список, профиль
- Уведомления
2. Исправление списков и профилей (минимальная ширина, починка responsive grid):
- Уведомления (652px)
--- Отправлено: http://78.155.202.182/simbatech/fe_simba/-/merge_requests/25 ---

12/06/25
1. Слияние роутов: Менеджмент.[Клиенты, Сотрудники] -> Менеджмент.Пользователи
2. Профиль сессии - в таблице назначений добавлен переход к пользователю.
3. Приглашения:
- добавлен табличный режим (переключение в селекторе сверху) по аналогии с профилем сессии
- удалить (отозвать) можно новые (создано, не сохранено) и отправленные, нельзя принятые
- ввод логина и email (для него валидация по маске)
- предупреждение о несохранённых изменениях при переходе к пользователю, смене режима на списочный, отмене
- покрыто проверками прав
- адаптировано под портретный вид, насколько это возможно
4. Менеджмент - добавлена страница "Роли":
- просмотр существующих ролей
- создание новой роли
- просмотр наличия категорий прав и прав на сущности у роли
- обновление прав роли
5. Пользователи:
- добавлен табличный режим
- доступны действия (кроме админа) - удалить, восстановить, смена имени/роли
- предупреждение о несохранённых изменениях
- проверки прав
- кое-как портретный вид
6. Менеджмент - добавлена страница "Фильтры":
- табличный режим
- доступны действия - удалить, восстановить, добавить, смена названия/цвета/группы сущностей
- предупреждения о несохранённых
- проверка прав
- криво-портретный вид
7. Исправления:
- тултип левому меню
- диапазон ставки ПШЕ (конструктор/прохождение) - [100, 10к]
- в пикере пользователей крестик оставлен только для портретного вида
- Сессия.Профиль: кнопка "+1 Каждому выбранному" => "Назначить выбранным"
- Симуляция.Профиль - подсвечивается вкладка сверху
- Гант - фон исправлен

13/06/25
1. Фильтры.таблица: 
- сортировка по названию, фильтр по группе сущностей
- размер пачки 1-20 => 1-10
2. Приглашения.таблица:
- размер пачки 1-20 => 1-50
- автогенерация логина и email (до отмены требования почты)
- экспорт в CSV
3. Сессия.профиль:
- размер пачки (временно) 1-20 => 1-10
4. Пользователи.таблица:
- столбец фильтров, фильтрация по ним
- подключено изменение фильтров, быстрое создание новых
5. Унификация фильтров (внешний вид, авто-подбор белый/чёрный текст):
- Сессия.Профиль
- Фильтры.таблица
- Приглашения.таблица
- Пользователи.таблица
- Симуляция - конструктор, список, профиль
6. Для всех таблиц унифицированы статусы (хранение цветов в одном месте), посмотреть их можно на собственной инженерной странице.

14-16/06/25
1. Пикер пользователей:
- мультивыбор (выбрать всех, отмена)
- фильтр по фильтрам (и по клику на фильтр)
2. Добавлены тултипы:
- Конструктор.Симуляция: Утвердить, Опубликовать, В архив, Из архива, Бюджет на оборудование, Бюджет на прочие расходы
- Конструктор.Узлы.список: N часов
- Конструктор.События.список: Связь
3. Конструктор.Граф:
- выравнивание исправлено под ориентирование на верхний левый угол (как на сетевой карте прохождения)
- валидация по сумме бюджетов и количеству недель, опция "Исправить" (выставление подходящих параметров)
4. Обработка 404 (редирект к списку):
- Конструктор.[Гант, Граф, ПШЕ, Событие, Симуляция, Узел].профиль
- Назначение.профиль
- Пользователь.профиль
- Приглашение.профиль
- Регистрация по приглашению
- Сессия.профиль
- Симуляция.профиль
5. Пользователи.профиль:
- режим просмотра
- режим редактирования (поля, фильтры)
- режим безопасности (смена login, email, пароля, выйти на всех устройствах)
- просмотр прав пользователя
- бан/разбан/удалить/восстановить
- переход к чату (кроме клиента) - только для другого, не удалённого пользователя
- переход к симуляциям/сессиям/назначениям
- адаптирован под портретный вид
6. Пользователи.[список, таблица]:
- для списка переход к диалогу с пользователем (вкладка "Текущие")
- для таблицы переход к диалогу/группе с пользовател(-ем/-ями):
-- нет несохранённых изменений
-- не открыта корзина
-- выбрано (включая работающего пользователя) не более 50 записей
7. Симуляции.список:
- набор вкладок изменён на [Готовые, Разработка, Архив, Все]
8. [Симуляции, Сессии, Назначения].список:
- добавлена возможность перехода с назначенным фильтром по создателю (проходящему для назначений)
9. Уведомления.список:
- исправлено пересечение карточек
10. Страницы прохождения, использующие TaskUtils:
- исправлен баг со связыванием дерева по SessionTaskExtended.id (подмена на order_id при fetch)
11. Пикер фильтров:
- возможность фиксировать группу сущностей
12. Вывод времени локализован:
- чаты
- Приглашения
- уведомления
- симуляции
13. Чаты:
- добавлена минимальная ширина сообщения
- перенос ломает слова без пробелов

17-18/06/25
1. Профиль назначения:
- исправлен баг с зависающим лоадером при ошибке инициализации
- скорректирована доступность опций управления состоянием
- исправлена невозможность остановки
- исправлено удаление назначений
2. QoL + Подключение фич из патча бэка 0.0.8.4:
2.1 Права в модалке/странице роли отображаются по категории полностью
2.2 В таблице/списке/профилях приглашений выводится пользователь (если принято), 
2.3 В профиле пользователя:
- добавлено "Пригласил(а): такой-то"
- для клиента "Чат с менеджером"
- поправлен бан/разбан
2.4 Повтор письма в профиле/таблице приглашений
2.5 Фильтры в профиле/списке/таблице приглашений, выбор набора фильтров для создаваемых пачкой
2.6 Таблица пользователей - поле "Пригласил(а)", массовый бан/разбан
2.7 Таблица фильтров - выбор группы сущностей для создаваемых пачкой
2.8. Таблица/список приглашений:
- фильтр по отправителю
2.9 Профиль сессии - управление состоянием в таблице назначений
2.10 Фильтры в профиле, списке, конструкторе симуляции
3. Прохождение:
3.1 Гант - операции назначения и снятия исправлены
3.2 Таймлайн - баг подбора задач исправлен
3.3 Профиль задачи:
- исправлена ошибка подгрузки ПШЕ
- назначение/снятие ПШЕ
- стили таблицы исправлены под макет
3.4 Профиль ПШЕ:
- исправлена ошибка подгрузки задачи
- стили таблицы исправлены под макет
- добавление/приоретизация/удаление назначения на задачу (приоретизация временно не меняет порядок, баг на бэке)
3.5 Добавлено запоминание прохождения:
- при обновлении страницы реинициализация хранилища
- при выходе на страницы "снаружи" есть возможность вернуться
3.6 Выставлена минимальная ширина: Бюджет, профили ПШЕ и задач
3.7 Сверху выводятся таймер (календарное время не обновляется, баг на бэке) и кнопки управления состоянием
3.8 Ввиду HTTP-режима объект информации о прохождении подтягивается при запросе списков (либо при обновлении страницы)

20-24/06/25
1. Страница логина и регистрации по приглашению - добавлены маски, тултипы, макс. длина и валидация полей
2. Страница чатов:
- подключена в режиме прохождения
- создание чата для клиентов ограничено
- карточки пользователей в диалоге создания заменены по образцу пикера
- просмотр корзины чатов
- удаление, восстановление чатов и поиск по их списку
- в списке чатов упразднены группы, пофикшены прогрузка и поиск
- поиск по сообщениям в чате
- страница информации о чате (по клику в шапку, либо через три точки)
- управление участниками чата: список, переход, бан/разбан (пока без разбора), повышение/понижение прав (пока без разбора), добавление
- заготовка меню с опциями для сообщения (по клику на него)
- вывод полного времени отправки сообщения по наведению на него
3. Прохождение.Дашборд - мини-Гант, чаты, 4 графика колесом (пока без графика)
4. Испрален глобальный баг компаратора для табличного представления списков (удаление записи об изменении при возврате к оригиналу)
5. Прохождение: разрешена работа с назначениями на предстарте и паузе
6. Поиск и решение локальных багов на страницах прохождения

25-26/06/25
1. Копирование инвайт-ссылки из таблицы и профиля приглашений
2. Страница регистрации приглашения: если приглашение уже использовано, об этом пишется, предлагается перейти в логин
3. Рабочий стол прохождения
4. Предотвращение покидания при несохранённых изменениях (и для софт-навигации внутри приложения, и для хард-навигации сменой URL/закрытием вкладки):
- Конструктор: профиль События/Узла/ПШЕ/Симуляции, Граф
- Пользователи: таблица, профиль
- Приглашения: таблица, профиль
- Роли
- Сессии.профиль
- Таблица фильтров
5. Исправление багов:
- Конструктор.Граф - неверный ID для новых
- Конструктор.Граф - ошибка при сохранении после удаления
- Конструктор.Граф - не строились связи при ID узла выше последнего
- Конструктор.Событие - проблема при создании/сохранении с реакциями на ответ
- Симуляции.список - отключение перехода в конструктор и сессию для удалённой симуляции
- Симуляции.профиль - отключение аккордеонов для удалённой симуляции, добавление опции восстановления
- Симуляции.профиль - отключён аккордеон Ганта, если не заданы первая и последняя задачи
- Конструктор.Гант - вывод сообщения, если симуляции не заданы первая или последняя задача
- Регистрация инвайта - не задавался creds?.user_id
6. Предотвращение закрытия страницы прохождения

27-29/06/25
1. Исправление багов:
- Прохождение.Сеть - цикличная перезагрузка
- Прохождение.Задача - не выводилась текущая продолжительность и производительность
- хранилище прохождения - ускорена прогрузка страниц через снижение повторных запросов (таймаут 30 секунд для запроса состояния ядра)
- Гант - исправлен возникавший отступ от верха контейнера
2. Отчёты:
- добавлена страница для перехода к отчётам (пока только в виде графиков)
- добавлена навигация к ней через сайдбар прохождения и рабочий стол
- добавлен график "План и затраты по задачам" - мини-версия в дашборде и полная отдельной страницей
3. Исправления в рамках тестирования:
3.1 В случае не валидного токена затирается хранимая копия объекта текущего пользователя
3.2 Исправлена возможность перехода в инженерные страницы с логина и страницы регистрации приглашения
3.3 Добавлен редирект при попытке загрузить повреждённый inviteId на странице регистрации приглашения
3.4 Кнопки на странице логина заблокированы на время запроса
3.5 Тултип пароля в логине отражает необходимость цифр
3.6 Тултип логина отражает обязательность буквы первым символом
3.7 Добавлена обработка кода ошибки 400 (InvitationIsAlreadyActivated)
3.8 Обработка некорректного строкового ID симуляции (редирект в список)
3.9 Запрещено создание сессии для архивированной симуляции
3.10 В профиле симуляции текущая вкладка соответствует флагам.
3.11 Закрыта возможность перехода из профиля симуляции в конструктор через аккордеоны при отсутствии прав.
3.12 Для карточки ПШЕ в профиле симуляции ограничена длина выводимого имени
DEPR 3.13 Минимальная высота для Ганта в профиле симуляции и прокрутка
3.14 Запрет изменения системных фильтров при создании/редактировании симуляции
3.15 Проверка на корректный формат ID для Конструктор.Симуляция
3.16 Создание системных фильтров ограничено для их страницы
3.17 Ограничено расширение TextArea описания симуляции
3.18 Название характеристики симуляции ограничено 20-ю символами
3.19 Добавлена подсветка обязательных полей для Конструктор.Симуляция
3.20 Добавлена проверка на ввод бюджетов на оборудование и прочие расходы для Конструктор.Симуляция
3.21 Исправлено ложное предотвращение покидания для создания новой симуляции
3.22 Исправлен gap между карточками в списке симуляций
3.23 В узле Конструктор.Граф убран отступ по горизонтали для баланса входящих/исходящих
3.24 Конструктор.Граф - исправлено позиционирование превью узлов
3.25 Везде, где редактируемы узлы, установлен макс. план_людей 5, план_дней 50
3.26 В таблице графа добавлен вывод бюджета симуляции
3.27 Конструктор.Симуляция: тултипы кнопок поясняют особенности статусов
3.28 Конструктор.Симуляция: для "Готова" добавлена возможность вернуть в разработку
3.29 Запрещён поиск и создание сессий для симуляций, которые не опубликованы, архивированы или удалены.
3.30 Конструктор.Симуляция: запрещено редактирование архивных, удалённых, готовых, опубликованных
3.31 Для удалённых симуляций открытие страниц конструктора запрещено
3.32 Запрещено редактирование готовых, опубликованных и архивных симуляций
3.33 Исправлен баг графа с невозможностью добавить задачи после первой
3.34 Исправлен баг подсчёта длины дерева для набора из только новых узлов
3.35 Исправлен баг - не сбрасывалось подключение Pusher при выходе из УЗ
3.36 Исправлен отступ справа на Ганте
3.37 Проверка на валидность ID симуляции в URL для страниц симуляции
3.38 Исправлен счётчик остатка ПШЕ в узле Ганта для 4+
--- Отправлено: http://78.155.202.182/simbatech/fe_simba/-/merge_requests/35 ---

30/06/25
1. Исправления:
1.1 Нет прав на запрос категорий прав - пользователь видит только свои права в профиле
1.2 Разрешён ввод цифр в имени пользователя
1.3 Для любой не удалённой симуляции можно создать редактируемый дубликат
1.4 Роли в приглашениях
1.5 Только несистемные пользовательские фильтры пользователи/приглашения
1.6 Исправлен компаратор изменений таблицы пользователей
1.7 Исправлен баг совмещения списков входящих/корзины в чатах
1.8 Кол-во ПШЕ на задаче прохождения ограничено пятью
1.9 Исправлено обновление информации прохождения после действий с состоянием
1.10 Исправлен фон и "Сегодня" Ганта дашборда
1.11 Исправлено некорректное отображение длины задач на Ганте для завершённых.
1.12 Исправлена недоступность смены роли в профиле пользователя (был баг), сама смена выключена для неподтверждённых
1.13 Исправлено избыточное предотвращение покидания в профиле сессии
1.14 По клику на лого в рабочем столе открытие дашборда
1.15 Исправлены отступы для групп селекторов в списках.
1.16 Убраны заглушки в списке уведомлений
1.17 Вывод имения пользователя в шапке вне прохождения (если нет поиска)
1.18 Баг позиционирования "Сегодня" и нумерации недель Ганта на дашборде
1.19 Ограничение ширины имени ПШЕ на таймлайне
1.20 Скорректирована фактическая длительность задачи в профиле и пикере
1.21 Запрет редактирования назначений завершённых задач
1.22 Прокрутка сайдбара с навигацией при необходимости
1.23 Во время запроса в сущностях конструктора отключены кнопки
1.24 Исправлена валидация поля недель в симуляции
1.25 В ганте прохождения сделана возможность дробной длины задач

01-02/07/25
1. Исправления
1.1 Центрирование навигационной группы в сайдбаре.
1.2 Возврат к оригинальным фильтрам в профиле пользователя при закрыти пикера.
1.3 Исправлена проверка на возможность менять роль в профиле.
1.4 Исправлена отправка новых+в_корзине для bulk назначений в профиле сессии
1.5 Производительность задачи статично 100%-0%, выполненная работа план - по прошедшему времени, факт - по исчерпанию стакана
1.6 Корректный вывод приоритетов ПШЕ (ждёт выдачи полных SWA бэком)
1.7 Убран лишний "Roles." при создании роли
1.8 Исправлен паттерн на таймлайне
1.9 Прогресс узлов на сетевой карте
1.10 Убрано зависание прелоадера на дашборде
1.11 Тултип и позиция точки на графике задач скорректирована
1.12 Открытие сайдбара задачи на графе конструктора при блокированной симуляции
1.13 Ширина узлов на Ганте прохождения учитывает также длительность в часах.
1.14 Скорректировано округление продолжительности на графике и в профиле задачи (с учётом часов)

04/07/25
1. Исправления
1.1 Поправлены правила, определяющие кол-во элементов на узле Ганта в зависимости от ширины
1.2 Исправлено перестроение списка задач ПШЕ после операций приоретизации
1.3 Проходящему закрыто управление статусом ядра
1.4 Менеджеру закрыты манипуляции с назначениями ПШЕ на узлы
1.5 Менеджеру разрешено редактирование видимого имени и описания пользователей, кроме админа
1.6 Быстрое снятие ПШЕ с задачи (отключено для завершённых симуляций и узлов)
--- Отправлено http://78.155.202.182/simbatech/fe_simba/-/merge_requests/39 ---

04-14/07/25
1. Исправления
1.1 Slack - фон прозрачный, граница цветом
1.2 Задачи и ПШЕ - диапазон требований/навыков [0, 6] => [0, 5] по данным и интерфейсам
1.3 Упразднено ограничение в 5 ПШЕ на задаче - временно лимит выставлен в 16 для конструктора

2. Аннотации и комментарии в коде:
2.1 Типы: 
- API: булк, метадата, параметры списочных запросов
- Конструктор: События, Календарь, Симуляция, Задача, ПШЕ
- Прохождение: События, Сессия, Назначение, SAI, Бюджет, Задача, ПШЕ, назначения ПШЕ на задачи
- Чаты: пользователь-участник, статус сообщения, сообщение, чат, уведомление
- Менеджмент: приглашения, права и категории, роль, пользователь, фильтр
- Внутренние сущности хранилища прохождения
2.2 Дополнен README.md, добавлен FE_Doc.md
3. События симуляции:
3.1 Сессионная типизация событий симуляции
4. Конструктор.Календарь:
4.1 Типизация календарных событий симуляции
4.2 Работа с типами календарных событий
4.3 работа с календарём симуляции
5. Использование данных прогноза при построении узлов на Ганте
6. Использование данных STP для формирования таймлайна ПШЕ
7. Supress 404: simulation-schedule-events, task-progresses (no-pagination empty on 404)
8. Исправлено позиционирование "Сегодня" на Ганте (полная страница)

15-18/07/25
1. Исправление выводимых назначений ПШЕ <-> задачи:
- разделение на 4 типа: текущие, прошлые, ожидающие назначения, ожидающие снятия
- учёт мгновенного применения в состоянии "предстарт" и для будущих задач
- опции "Вернуть" для прошлых и ожидающих снятия (кроме завершённых задач)
- цветовая маркировка и подписи соответственно статусу
- затронутые страницы и компоненты:
-- профиль ПШЕ и пикер задач
-- профиль задачи и пикер ПШЕ
-- полноразмерный Гант прохождения
-- бюджет
2. Доработка "Календаря" конструктора:
- проверка наличия прав на работу с сущностями календаря
- редирект для удалённых симуляций
- запрет редактирования для архивных, готовых, опубликованных

21/07/25
1. Добавление отображения изначального плана на Ганте (в конструкторе и прохождении):
1.1 Добавление и расчёт параметров по плановому крит. пути в TaskUtils
1.2 Добавление аннотаций в классе TaskUtils
1.3 Вывод планового положения и длины задач серыми узлами под актуальными
2. Исправлена сортировка групп назначений (по категории) в профилях ПШЕ/задачи, доступность действий

22/07/25
1. Исправления:
1.1 Исправил видимую длительность событий календаря без типа
1.2 Предотвращение покидания в календаре конструктора - при наличии изменений
1.3 Валидация для создаваемых/редактируемых типов событий

05/08/25
SIM-24:
- ID и логин видны админ/менеджер
- email виден себе или админу
SIM-26: 
- в списке симуляций до 3 фильтров, дальше +N с выпадающим списком
- в профиле симуляции (чтение) - до 5
- в профиле симуляции (конструктор) - до 5
- в списке пользователей - до 3
- в таблице пользователей - до 3
- в профиле пользователя - до 4
- в пикере пользователей - до 3
- в списке приглашений - до 2
- в таблице приглашений - до 3
- в профиле приглашения - до 4
- в профиле сессии - до 3

Todo:
- валидация календаря конструктора
- Правила вмещения кругляшков на Ганте
- Сценарные события симуляции:
    ? хранение данных о них в хранилище прохождения
    ? операции формирования набора событийных чатов и их сообщений
    ? вёрстка чатов игрового типа
- адаптация нового формата ответа 422 ошибок (валидация полей)
- предупреждение о потере изменений в модалке ScheduleEventType
- проверить заполненность полей в профиле задачи при прохождении
- Гант, Таймлайн: прокрутка левого меню с наименованием задач/ПШЕ по канвасу
- корректная генерация логина в таблице приглашений
- придумать маску для имени группового чата (диалог создания, режим просмотра инфы)
- удаление/редактирование сообщений (нужен бэк)
- пагинация списка чатов и query (нужен бэк)
- перегрузка объекта чата при добавлении участников снаружи
- управление чатом (участниками чата)
- заменить кнопки табличных опций по дизайну