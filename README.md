**Необходимые зависимости**

- node >= 22.16
- pnpm >= 10

```bash
npm install -g pnpm@latest-10
```

**Env**

Пример использования переменных окружения в .env.example

**Установка зависимостей**
```bash
pnpm i
```
**Запуск сервера разработки**
```bash
pnpm start
```

**Продакшн билд**
```bash
pnpm build
```

**Предпросмотр продакшн билда локально (не для продакшена)**
```bash
pnpm preview
```

Документация в кратком виде в FE_Doc.md

В проекте используются:
- ahooks для дополнения/замены менее удобных React Hooks;
- AntD как библиотека компонентов;
- axios для сетевых запросов;
- Day.JS для работы с Date/DateTime/Time;
- _lodash для полезных функций (работа с массивами, объектами, поиск, глубокое сравнение);
- MobX как хранилище;
- pusher-js для чатов и уведомлений;
- React как основной фреймворк;
- ReactFlow для визуализации и работы на канвасе
- React-Router для роутинга;
- SASS (+scss) для описания стилей;
- typescript для типизации и проверки кода при компиляции;
- webpack и babel для сборки;